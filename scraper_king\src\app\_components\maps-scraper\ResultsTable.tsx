"use client";

import { useState, useMemo } from "react";
import type { ScrapedResult } from "./MapsScraperForm";

interface ResultsTableProps {
  data: ScrapedResult[];
}

type SortField = "name" | "city" | "category" | "rating" | "reviews" | "mostVisited";
type SortDirection = "asc" | "desc";

export function ResultsTable({ data }: ResultsTableProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [sortField, setSortField] = useState<SortField>("name");
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");
  const itemsPerPage = 10;

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (field === sortField) {
      // Toggle direction if same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // New field, default to ascending
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Sort and paginate data
  const sortedData = useMemo(() => {
    return [...data].sort((a, b) => {
      let comparison = 0;
      
      switch (sortField) {
        case "name":
          comparison = a.name.localeCompare(b.name);
          break;
        case "city":
          comparison = a.city.localeCompare(b.city);
          break;
        case "category":
          comparison = a.category.localeCompare(b.category);
          break;
        case "rating":
          comparison = a.rating - b.rating;
          break;
        case "reviews":
          comparison = a.reviews - b.reviews;
          break;
        case "mostVisited":
          comparison = (a.mostVisited === b.mostVisited) ? 0 : a.mostVisited ? 1 : -1;
          break;
      }
      
      return sortDirection === "asc" ? comparison : -comparison;
    });
  }, [data, sortField, sortDirection]);

  // Calculate pagination
  const totalPages = Math.ceil(sortedData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, sortedData.length);
  const currentData = sortedData.slice(startIndex, endIndex);
  
  // Handle pagination
  const handlePrevPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };
  
  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  // Download as CSV
  const downloadCsv = () => {
    // Create CSV content
    const headers = ["Name", "City", "Category", "Rating", "Reviews", "Most Visited", "Address", "Phone", "Website"];
    const rows = sortedData.map(item => [
      item.name,
      item.city,
      item.category,
      item.rating.toString(),
      item.reviews.toString(),
      item.mostVisited ? "Yes" : "No",
      item.address || "",
      item.phone || "",
      item.website || ""
    ]);
    
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
    ].join('\n');
    
    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'google_maps_results.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (data.length === 0) {
    return (
      <div className="rounded-lg bg-white/10 p-6 text-center">
        <p className="text-lg">No results found</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Scraped Results</h2>
        <button
          onClick={downloadCsv}
          className="rounded-lg bg-[hsl(280,100%,70%)] px-4 py-2 font-medium text-white transition hover:bg-[hsl(280,100%,60%)]"
        >
          Download CSV
        </button>
      </div>
      
      <div className="overflow-x-auto rounded-lg bg-white/5">
        <table className="w-full border-collapse">
          <thead>
            <tr className="border-b border-white/10 bg-white/10 text-left">
              <th 
                className="cursor-pointer p-4 font-semibold"
                onClick={() => handleSort("name")}
              >
                Name {sortField === "name" && (sortDirection === "asc" ? "↑" : "↓")}
              </th>
              <th 
                className="cursor-pointer p-4 font-semibold"
                onClick={() => handleSort("city")}
              >
                City {sortField === "city" && (sortDirection === "asc" ? "↑" : "↓")}
              </th>
              <th 
                className="cursor-pointer p-4 font-semibold"
                onClick={() => handleSort("category")}
              >
                Category {sortField === "category" && (sortDirection === "asc" ? "↑" : "↓")}
              </th>
              <th 
                className="cursor-pointer p-4 font-semibold"
                onClick={() => handleSort("rating")}
              >
                Rating {sortField === "rating" && (sortDirection === "asc" ? "↑" : "↓")}
              </th>
              <th 
                className="cursor-pointer p-4 font-semibold"
                onClick={() => handleSort("reviews")}
              >
                Reviews {sortField === "reviews" && (sortDirection === "asc" ? "↑" : "↓")}
              </th>
              <th 
                className="cursor-pointer p-4 font-semibold"
                onClick={() => handleSort("mostVisited")}
              >
                Most Visited {sortField === "mostVisited" && (sortDirection === "asc" ? "↑" : "↓")}
              </th>
            </tr>
          </thead>
          <tbody>
            {currentData.map((item, index) => (
              <tr 
                key={index} 
                className="border-b border-white/10 transition hover:bg-white/5"
              >
                <td className="p-4">{item.name}</td>
                <td className="p-4">{item.city}</td>
                <td className="p-4">{item.category}</td>
                <td className="p-4">{item.rating.toFixed(1)}</td>
                <td className="p-4">{item.reviews}</td>
                <td className="p-4">{item.mostVisited ? "Yes" : "No"}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-white/70">
            Showing {startIndex + 1}-{endIndex} of {sortedData.length} results
          </div>
          <div className="flex gap-2">
            <button
              onClick={handlePrevPage}
              disabled={currentPage === 1}
              className="rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50"
            >
              Previous
            </button>
            <div className="flex items-center justify-center rounded-lg bg-white/10 px-4 py-2">
              {currentPage} / {totalPages}
            </div>
            <button
              onClick={handleNextPage}
              disabled={currentPage === totalPages}
              className="rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
