@echo off
echo Starting Flask backend and Next.js frontend...

REM Start Flask backend in a new window
start cmd /k "python flask_backend.py"

REM Wait for Flask to start
timeout /t 3

REM Start Next.js frontend in a new window
cd scraper_king
start cmd /k "npm run dev"

echo Both servers are starting. Please wait...
echo Flask backend: http://localhost:5000
echo Next.js frontend: http://localhost:3000
