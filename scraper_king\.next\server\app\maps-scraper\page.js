(()=>{var e={};e.id=791,e.ids=[791],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16940:(e,s,t)=>{Promise.resolve().then(t.bind(t,39295)),Promise.resolve().then(t.bind(t,24903)),Promise.resolve().then(t.bind(t,8693)),Promise.resolve().then(t.bind(t,18228)),Promise.resolve().then(t.bind(t,87394)),Promise.resolve().then(t.bind(t,19100)),Promise.resolve().then(t.bind(t,54050)),Promise.resolve().then(t.bind(t,97322)),Promise.resolve().then(t.bind(t,93425)),Promise.resolve().then(t.bind(t,12030)),Promise.resolve().then(t.bind(t,46674)),Promise.resolve().then(t.bind(t,35522)),Promise.resolve().then(t.bind(t,45806)),Promise.resolve().then(t.t.bind(t,85814,23)),Promise.resolve().then(t.bind(t,51189))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29173:(e,s,t)=>{"use strict";t.d(s,{MapsScraperForm:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call MapsScraperForm() from the server but MapsScraperForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\_components\\maps-scraper\\MapsScraperForm.tsx","MapsScraperForm")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30092:(e,s,t)=>{Promise.resolve().then(t.bind(t,20665)),Promise.resolve().then(t.bind(t,55465)),Promise.resolve().then(t.bind(t,51503)),Promise.resolve().then(t.bind(t,82246)),Promise.resolve().then(t.bind(t,58920)),Promise.resolve().then(t.bind(t,93250)),Promise.resolve().then(t.bind(t,35917)),Promise.resolve().then(t.bind(t,74052)),Promise.resolve().then(t.bind(t,64480)),Promise.resolve().then(t.bind(t,25080)),Promise.resolve().then(t.bind(t,25480)),Promise.resolve().then(t.bind(t,10240)),Promise.resolve().then(t.bind(t,7512)),Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.bind(t,29173))},33873:e=>{"use strict";e.exports=require("path")},41097:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>h,tree:()=>c});var r=t(65239),i=t(48088),a=t(88170),n=t.n(a),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let c={children:["",{children:["maps-scraper",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,58371)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\maps-scraper\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\maps-scraper\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/maps-scraper/page",pathname:"/maps-scraper",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},51189:(e,s,t)=>{"use strict";t.d(s,{MapsScraperForm:()=>c});var r=t(60687),i=t(43210);function a({data:e}){let[s,t]=(0,i.useState)(1),[a,n]=(0,i.useState)("name"),[o,l]=(0,i.useState)("asc"),c=e=>{e===a?l("asc"===o?"desc":"asc"):(n(e),l("asc"))},d=(0,i.useMemo)(()=>[...e].sort((e,s)=>{let t=0;switch(a){case"name":t=e.name.localeCompare(s.name);break;case"city":t=e.city.localeCompare(s.city);break;case"category":t=e.category.localeCompare(s.category);break;case"rating":t=e.rating-s.rating;break;case"reviews":t=e.reviews-s.reviews;break;case"mostVisited":t=e.mostVisited===s.mostVisited?0:e.mostVisited?1:-1}return"asc"===o?t:-t}),[e,a,o]),m=Math.ceil(d.length/10),h=(s-1)*10,u=Math.min(h+10,d.length),p=d.slice(h,u);return 0===e.length?(0,r.jsx)("div",{className:"rounded-lg bg-white/10 p-6 text-center",children:(0,r.jsx)("p",{className:"text-lg",children:"No results found"})}):(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold",children:"Scraped Results"}),(0,r.jsx)("button",{onClick:()=>{let e=new Blob([["Name,City,Category,Rating,Reviews,Most Visited,Address,Phone,Website",...d.map(e=>[e.name,e.city,e.category,e.rating.toString(),e.reviews.toString(),e.mostVisited?"Yes":"No",e.address||"",e.phone||"",e.website||""]).map(e=>e.map(e=>`"${String(e).replace(/"/g,'""')}"`).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),s=URL.createObjectURL(e),t=document.createElement("a");t.setAttribute("href",s),t.setAttribute("download","google_maps_results.csv"),document.body.appendChild(t),t.click(),document.body.removeChild(t)},className:"rounded-lg bg-[hsl(280,100%,70%)] px-4 py-2 font-medium text-white transition hover:bg-[hsl(280,100%,60%)]",children:"Download CSV"})]}),(0,r.jsx)("div",{className:"overflow-x-auto rounded-lg bg-white/5",children:(0,r.jsxs)("table",{className:"w-full border-collapse",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b border-white/10 bg-white/10 text-left",children:[(0,r.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>c("name"),children:["Name ","name"===a&&("asc"===o?"↑":"↓")]}),(0,r.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>c("city"),children:["City ","city"===a&&("asc"===o?"↑":"↓")]}),(0,r.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>c("category"),children:["Category ","category"===a&&("asc"===o?"↑":"↓")]}),(0,r.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>c("rating"),children:["Rating ","rating"===a&&("asc"===o?"↑":"↓")]}),(0,r.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>c("reviews"),children:["Reviews ","reviews"===a&&("asc"===o?"↑":"↓")]}),(0,r.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>c("mostVisited"),children:["Most Visited ","mostVisited"===a&&("asc"===o?"↑":"↓")]})]})}),(0,r.jsx)("tbody",{children:p.map((e,s)=>(0,r.jsxs)("tr",{className:"border-b border-white/10 transition hover:bg-white/5",children:[(0,r.jsx)("td",{className:"p-4",children:e.name}),(0,r.jsx)("td",{className:"p-4",children:e.city}),(0,r.jsx)("td",{className:"p-4",children:e.category}),(0,r.jsx)("td",{className:"p-4",children:e.rating.toFixed(1)}),(0,r.jsx)("td",{className:"p-4",children:e.reviews}),(0,r.jsx)("td",{className:"p-4",children:e.mostVisited?"Yes":"No"})]},s))})]})}),m>1&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"text-sm text-white/70",children:["Showing ",h+1,"-",u," of ",d.length," results"]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("button",{onClick:()=>{t(e=>Math.max(e-1,1))},disabled:1===s,className:"rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50",children:"Previous"}),(0,r.jsxs)("div",{className:"flex items-center justify-center rounded-lg bg-white/10 px-4 py-2",children:[s," / ",m]}),(0,r.jsx)("button",{onClick:()=>{t(e=>Math.min(e+1,m))},disabled:s===m,className:"rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50",children:"Next"})]})]})]})}function n({isProcessingMultiple:e,progress:s}){return(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-8",children:[(0,r.jsx)("div",{className:"mb-4 h-12 w-12 animate-spin rounded-full border-4 border-white/20 border-t-[hsl(280,100%,70%)]"}),e&&s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("p",{className:"text-lg font-medium",children:["Scraping Google Maps... (",s.current+1,"/",s.total,")"]}),(0,r.jsx)("div",{className:"mt-3 h-2 w-64 overflow-hidden rounded-full bg-white/10",children:(0,r.jsx)("div",{className:"h-full bg-[hsl(280,100%,70%)]",style:{width:`${Math.round(s.current/s.total*100)}%`}})}),(0,r.jsx)("p",{className:"mt-2 text-sm text-white/70",children:"Processing multiple cities. Please wait..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{className:"text-lg font-medium",children:"Scraping Google Maps..."}),(0,r.jsx)("p",{className:"mt-2 text-sm text-white/70",children:"This may take a few moments depending on the number of results."})]})]})}var o=t(79755);let l=[{code:"us",name:"United States",cities:["New York","Los Angeles","Chicago","Houston","Phoenix","Philadelphia","San Antonio","San Diego","Dallas","San Jose"]},{code:"uk",name:"United Kingdom",cities:["London","Birmingham","Manchester","Glasgow","Liverpool","Bristol","Edinburgh","Leeds","Sheffield","Newcastle"]},{code:"ca",name:"Canada",cities:["Toronto","Montreal","Vancouver","Calgary","Edmonton","Ottawa","Winnipeg","Quebec City","Hamilton","Kitchener"]},{code:"au",name:"Australia",cities:["Sydney","Melbourne","Brisbane","Perth","Adelaide","Gold Coast","Newcastle","Canberra","Wollongong","Hobart"]},{code:"in",name:"India",cities:["Mumbai","Delhi","Bangalore","Hyderabad","Chennai","Kolkata","Pune","Ahmedabad","Jaipur","Surat"]},{code:"de",name:"Germany",cities:["Berlin","Hamburg","Munich","Cologne","Frankfurt","Stuttgart","D\xfcsseldorf","Leipzig","Dortmund","Essen"]},{code:"fr",name:"France",cities:["Paris","Marseille","Lyon","Toulouse","Nice","Nantes","Strasbourg","Montpellier","Bordeaux","Lille"]},{code:"es",name:"Spain",cities:["Madrid","Barcelona","Valencia","Seville","Zaragoza","M\xe1laga","Murcia","Palma","Las Palmas","Bilbao"]},{code:"it",name:"Italy",cities:["Rome","Milan","Naples","Turin","Palermo","Genoa","Bologna","Florence","Bari","Catania"]},{code:"jp",name:"Japan",cities:["Tokyo","Yokohama","Osaka","Nagoya","Sapporo","Fukuoka","Kobe","Kyoto","Kawasaki","Saitama"]},{code:"br",name:"Brazil",cities:["S\xe3o Paulo","Rio de Janeiro","Bras\xedlia","Salvador","Fortaleza","Belo Horizonte","Manaus","Curitiba","Recife","Porto Alegre"]},{code:"mx",name:"Mexico",cities:["Mexico City","Guadalajara","Monterrey","Puebla","Tijuana","Le\xf3n","Ju\xe1rez","Zapopan","M\xe9rida","Canc\xfan"]}];function c(){let[e,s]=(0,i.useState)([]),[t,c]=(0,i.useState)(""),[d,m]=(0,i.useState)(""),[h,u]=(0,i.useState)("us"),[p,x]=(0,i.useState)(3),[g,b]=(0,i.useState)(10),[v,f]=(0,i.useState)(!1),[j,w]=(0,i.useState)(null),[N,y]=(0,i.useState)(null),[P,S]=(0,i.useState)(!1),[C,k]=(0,i.useState)({current:0,total:0}),M=l.find(e=>e.code===h),F=M?.cities||[],_=o.F.scraper.scrapeGoogleMaps.useMutation({onSuccess:s=>{P?(k(e=>({...e,current:e.current+1})),C.current+1>=C.total?(S(!1),y(e=>[...e||[],...s])):R(e[C.current+1])):y(s)},onError:e=>{w(e.message),S(!1)}}),R=e=>{_.mutate({city:e.trim(),category:d.trim(),country:h,minRating:p,minReviews:g,sortByMostVisited:v})},A=e=>{_.mutate({city:e[0].trim(),cities:e.map(e=>e.trim()),category:d.trim(),country:h,minRating:p,minReviews:g,sortByMostVisited:v})},B=e=>{s(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},G=()=>{s([]),c(""),m(""),u("us"),x(3),b(10),f(!1),w(null),y(null),S(!1),k({current:0,total:0})};return(0,r.jsxs)("div",{className:"flex flex-col gap-6",children:[!_.isPending&&!N&&(0,r.jsxs)("form",{onSubmit:s=>(s.preventDefault(),w(null),0===e.length)?void w("At least one city must be selected"):d.trim()?void(y(null),A(e)):void w("Category is required"),className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)("label",{htmlFor:"country",className:"font-medium",children:"Country"}),(0,r.jsx)("select",{id:"country",value:h,onChange:e=>{u(e.target.value),s([])},className:"rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]",children:l.map(e=>(0,r.jsx)("option",{value:e.code,children:e.name},e.code))})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsxs)("label",{htmlFor:"category",className:"font-medium",children:["Category ",(0,r.jsx)("span",{className:"text-red-400",children:"*"})]}),(0,r.jsx)("input",{id:"category",type:"text",value:d,onChange:e=>m(e.target.value),placeholder:"e.g., restaurants, hotels, etc.",className:"rounded-lg bg-white/10 px-4 py-2 text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]",required:!0})]}),(0,r.jsxs)("div",{className:"col-span-1 flex flex-col gap-2 md:col-span-2",children:[(0,r.jsxs)("label",{className:"font-medium",children:["Cities ",(0,r.jsx)("span",{className:"text-red-400",children:"*"}),(0,r.jsx)("span",{className:"ml-2 text-sm text-white/70",children:"(Select cities to scrape or add your own)"})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-2 rounded-lg bg-white/5 p-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5",children:F.map(s=>(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("input",{type:"checkbox",id:`city-${s}`,checked:e.includes(s),onChange:()=>B(s),className:"h-4 w-4 rounded border-white/30 bg-white/10 text-[hsl(280,100%,70%)] focus:ring-[hsl(280,100%,70%)]"}),(0,r.jsx)("label",{htmlFor:`city-${s}`,className:"text-sm",children:s})]},s))}),(0,r.jsxs)("div",{className:"mt-2 flex gap-2",children:[(0,r.jsx)("input",{type:"text",value:t,onChange:e=>c(e.target.value),placeholder:"Add a custom city",className:"flex-1 rounded-lg bg-white/10 px-4 py-2 text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]"}),(0,r.jsx)("button",{type:"button",onClick:()=>{t.trim()&&!e.includes(t.trim())&&(s(e=>[...e,t.trim()]),c(""))},disabled:!t.trim(),className:"rounded-lg bg-white/10 px-4 py-2 font-medium transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50",children:"Add"})]}),e.length>0&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("p",{className:"mb-1 text-sm text-white/70",children:"Selected Cities:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:e.map(e=>(0,r.jsxs)("span",{className:"flex items-center gap-1 rounded-full bg-[hsl(280,100%,70%)]/20 px-3 py-1 text-sm",children:[e,(0,r.jsx)("button",{type:"button",onClick:()=>B(e),className:"ml-1 rounded-full p-1 text-white/70 hover:bg-white/10 hover:text-white",children:"✕"})]},e))})]})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)("label",{htmlFor:"minRating",className:"font-medium",children:"Minimum Star Rating"}),(0,r.jsx)("input",{id:"minRating",type:"number",min:"1",max:"5",step:"0.1",value:p,onChange:e=>x(parseFloat(e.target.value)),className:"rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]"})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)("label",{htmlFor:"minReviews",className:"font-medium",children:"Minimum Number of Reviews"}),(0,r.jsx)("input",{id:"minReviews",type:"number",min:"0",value:g,onChange:e=>b(parseInt(e.target.value)),className:"rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("input",{id:"sortByMostVisited",type:"checkbox",checked:v,onChange:e=>f(e.target.checked),className:"h-5 w-5 rounded border-white/30 bg-white/10 text-[hsl(280,100%,70%)] focus:ring-[hsl(280,100%,70%)]"}),(0,r.jsx)("label",{htmlFor:"sortByMostVisited",className:"font-medium",children:"Sort by Most Visited"})]})]}),j&&(0,r.jsx)("div",{className:"rounded-lg bg-red-500/20 p-4 text-red-200",children:(0,r.jsx)("p",{children:j})}),(0,r.jsxs)("div",{className:"mt-4 flex gap-4",children:[(0,r.jsx)("button",{type:"submit",className:"flex-1 rounded-lg bg-[hsl(280,100%,70%)] px-6 py-3 font-semibold text-white transition hover:bg-[hsl(280,100%,60%)]",children:"Start Scraping"}),(0,r.jsx)("button",{type:"button",onClick:G,className:"rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20",children:"Reset"})]})]}),_.isPending&&(0,r.jsx)(n,{isProcessingMultiple:P,progress:P?C:void 0}),N&&(0,r.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,r.jsx)(a,{data:N}),(0,r.jsx)("div",{className:"flex gap-4",children:(0,r.jsx)("button",{onClick:G,className:"rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20",children:"New Search"})})]})]})}},58371:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c,metadata:()=>l});var r=t(37413),i=t(4536),a=t.n(i),n=t(29173),o=t(60250);let l={title:"Google Maps Scraper - Scraper King",description:"Scrape business data from Google Maps"};function c(){return(0,r.jsx)(o.d,{children:(0,r.jsx)("main",{className:"min-h-screen bg-gradient-to-b from-[#2e026d] to-[#15162c] text-white",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("header",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("h1",{className:"text-4xl font-extrabold tracking-tight sm:text-5xl",children:["Google Maps ",(0,r.jsx)("span",{className:"text-[hsl(280,100%,70%)]",children:"Scraper"})]}),(0,r.jsx)(a(),{href:"/",className:"rounded-full bg-white/10 px-4 py-2 font-medium transition hover:bg-white/20",children:"← Back to Home"})]}),(0,r.jsx)("p",{className:"mt-2 text-lg text-white/70",children:"Scrape business data from Google Maps based on location and category"})]}),(0,r.jsx)("div",{className:"rounded-xl bg-white/10 p-6 shadow-lg",children:(0,r.jsx)(n.MapsScraperForm,{})})]})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79646:e=>{"use strict";e.exports=require("child_process")},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[719,338,814,788,923,240],()=>t(41097));module.exports=r})();