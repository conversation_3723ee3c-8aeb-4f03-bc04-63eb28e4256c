# Business Scraper Application

This application consists of a Flask backend and a Next.js frontend for scraping business data.

## Project Structure

- `flask_backend.py`: Flask API backend that handles business scraping requests
- `abcd.py`: Core Python scraping functionality
- `business_scraper.py`: Adapter script for the business scraper
- `scraper_king/`: Next.js frontend application

## Prerequisites

- Python 3.8 or higher
- Node.js 16 or higher
- npm or yarn

## Setup Instructions

### 1. Install Python Dependencies

```bash
pip install -r requirements.txt
```

### 2. Install Node.js Dependencies

```bash
cd scraper_king
npm install
```

## Running the Application

### Option 1: Using the Start Script

On Windows, you can use the provided batch file to start both the backend and frontend:

```bash
start.bat
```

### Option 2: Manual Start

#### Start the Flask Backend

```bash
python flask_backend.py
```

The Flask backend will run on http://localhost:5000.

#### Start the Next.js Frontend

```bash
cd scraper_king
npm run dev
```

The Next.js frontend will run on http://localhost:3000.

## Using the Application

1. Open your browser and navigate to http://localhost:3000
2. Use the business scraper form to search for businesses
3. Enter a search term (business name, type, or any business-related term)
4. Select a location and country
5. Click "Start Scraping" to begin the scraping process
6. View the results in the table and download them as CSV if needed

## API Endpoints

The Flask backend provides the following API endpoints:

- `POST /api/scrape`: Scrape business data
  - Request body: `{ "category": "search term", "location": "location", "country": "country code" }`
  - Response: Array of business data objects

- `GET /api/health`: Health check endpoint
  - Response: `{ "status": "ok", "message": "Flask backend is running" }`

## Troubleshooting

- If you encounter CORS issues, make sure the Flask backend is running and properly configured
- Check the browser console for any API errors
- Ensure all dependencies are installed correctly
