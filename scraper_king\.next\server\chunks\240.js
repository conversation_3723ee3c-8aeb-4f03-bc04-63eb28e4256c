exports.id=240,exports.ids=[240],exports.modules={8516:(e,r,t)=>{"use strict";t.d(r,{_:()=>n});var o=t(32871),s=t(70762);let n=(0,o.w)({server:{DATABASE_URL:s.z.string().url(),NODE_ENV:s.z.enum(["development","test","production"]).default("development")},client:{},runtimeEnv:{DATABASE_URL:process.env.DATABASE_URL,NODE_ENV:"production"},skipValidation:!!process.env.SKIP_ENV_VALIDATION,emptyStringAsUndefined:!0})},35692:()=>{},38545:(e,r,t)=>{"use strict";t.d(r,{OA:()=>u,dW:()=>d,LO:()=>h,JI:()=>v});var o=t(50469),s=t(23041),n=t(70762),i=t(96330),a=t(8516);let l=globalThis,c=l.prisma??new i.PrismaClient({log:"development"===a._.NODE_ENV?["query","error","warn"]:["error"]});"production"!==a._.NODE_ENV&&(l.prisma=c);let d=async e=>({db:c,...e}),p=o.Al.context().create({transformer:s.Ay,errorFormatter:({shape:e,error:r})=>({...e,data:{...e.data,zodError:r.cause instanceof n.G?r.cause.flatten():null}})}),u=p.createCallerFactory,h=p.router,m=p.middleware(async({next:e,path:r})=>{let t=Date.now();if(p._config.isDev){let e=Math.floor(400*Math.random())+100;await new Promise(r=>setTimeout(r,e))}let o=await e(),s=Date.now();return console.log(`[TRPC] ${r} took ${s-t}ms to execute`),o}),v=p.procedure.use(m)},47702:(e,r,t)=>{Promise.resolve().then(t.bind(t,89544))},60250:(e,r,t)=>{"use strict";t.d(r,{d:()=>v,F:()=>m});var o=t(38124),s=t(44999),n=t(61120),i=t(88948),a=t(38545),l=t(94532),c=t(49821),d=t(23041);let p=(0,n.cache)(async()=>{let e=new Headers(await (0,s.b3)());return e.set("x-trpc-source","rsc"),(0,a.dW)({headers:e})}),u=(0,n.cache)(()=>new l.E({defaultOptions:{queries:{staleTime:3e4},dehydrate:{serializeData:d.Ay.serialize,shouldDehydrateQuery:e=>(0,c.XS)(e)||"pending"===e.state.status},hydrate:{deserializeData:d.Ay.deserialize}}})),h=(0,i.u)(p),{trpc:m,HydrateClient:v}=(0,o.i)(h,u)},77093:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},79755:(e,r,t)=>{"use strict";t.d(r,{TRPCReactProvider:()=>m,F:()=>h});var o=t(60687),s=t(8693),n=t(68357),i=t(63839),a=t(43210),l=t(558),c=t(62087),d=t(72083);let p=()=>new c.E({defaultOptions:{queries:{staleTime:3e4},dehydrate:{serializeData:l.Ay.serialize,shouldDehydrateQuery:e=>(0,d.XS)(e)||"pending"===e.state.status},hydrate:{deserializeData:l.Ay.deserialize}}}),u=()=>p(),h=(0,i.pY)();function m(e){let r=u(),[t]=(0,a.useState)(()=>h.createClient({links:[(0,n.$H)({enabled:e=>"down"===e.direction&&e.result instanceof Error}),(0,n.N9)({transformer:l.Ay,url:(process.env.VERCEL_URL?`https://${process.env.VERCEL_URL}`:`http://localhost:${process.env.PORT??3e3}`)+"/api/trpc",headers:()=>{let e=new Headers;return e.set("x-trpc-source","nextjs-react"),e}})]}));return(0,o.jsx)(s.QueryClientProvider,{client:r,children:(0,o.jsx)(h.Provider,{client:t,queryClient:r,children:e.children})})}},88948:(e,r,t)=>{"use strict";t.d(r,{P:()=>b,u:()=>P});var o=t(70762),s=t(38545);let n=(0,s.LO)({hello:s.JI.input(o.z.object({text:o.z.string()})).query(({input:e})=>({greeting:`Hello ${e.text}`})),create:s.JI.input(o.z.object({name:o.z.string().min(1)})).mutation(async({ctx:e,input:r})=>e.db.post.create({data:{name:r.name}})),getLatest:s.JI.query(async({ctx:e})=>await e.db.post.findFirst({orderBy:{createdAt:"desc"}})??null)});var i=t(79646),a=t(28354),l=t(33873),c=t.n(l),d=t(29021),p=t.n(d);let u=(0,a.promisify)(i.exec),h=e=>{let r=e.replace(/^https?:\/\//,"").replace(/\/$/,"").split("/")[0],t=r.split(".")[0].charAt(0).toUpperCase()+r.split(".")[0].slice(1),o=["New York","Los Angeles","Chicago","Houston","Phoenix","Philadelphia"],s=o[Math.floor(Math.random()*o.length)],n=Math.floor(1e3*Math.random())+1,i=["Main St","Broadway","Park Ave","Oak St","Maple Ave","Washington Blvd"],a=i[Math.floor(Math.random()*i.length)];return{url:e,name:t,address:`${n} ${a}, ${s}, NY`,phone:Math.random()>.2?`+1 (${Math.floor(900*Math.random())+100}) ${Math.floor(900*Math.random())+100}-${Math.floor(9e3*Math.random())+1e3}`:"",email:Math.random()>.3?`contact@${r}`:""}},m=e=>e.split(/\r?\n/).filter(e=>""!==e.trim()).map(e=>{if(e.startsWith('"')){let r=e.match(/"([^"]+)"/);return r?r[1]:""}return e.split(",")[0]}).filter(e=>e.startsWith("http")),v=o.z.object({location:o.z.string().min(1),category:o.z.string().min(1),country:o.z.string().min(1)}),y=o.z.array(o.z.object({name:o.z.string(),address:o.z.string(),phone:o.z.string(),email:o.z.string(),url:o.z.string(),category:o.z.string().optional(),social_links:o.z.string().optional()})),f=async e=>{try{let r=c().resolve(process.cwd(),"business_scraper.py");if(!p().existsSync(r)){console.error(`Business scraper script not found at ${r}`);let e=c().resolve(process.cwd(),"..","business_scraper.py");if(!p().existsSync(e))throw console.error(`Business scraper script not found at alternative path ${e} either`),Error("Business scraper script not found");console.log(`Found business scraper script at alternative path: ${e}`),r=e}console.log(`Using business scraper script at: ${r}`),console.log(`Running with parameters: location=${e.location}, search term=${e.category}, country=${e.country}`);let t=`python "${r}" --location "${e.location}" --category "${e.category}" --country "${e.country}"`;console.log(`Executing command: ${t}`);let{stdout:o,stderr:s}=await u(t);s&&!s.includes("DevTools listening")&&console.error(`Python script error: ${s}`),console.log("Python script executed successfully");try{return JSON.parse(o).map(e=>({...e,social_links:e.social_links||"N/A"}))}catch(e){throw console.error(`Error parsing JSON output: ${e}`),console.error(`Raw output: ${o}`),Error("Failed to parse Python script output")}}catch(e){throw console.error("Error running Python script:",e),Error(`Failed to scrape business data: ${e instanceof Error?e.message:String(e)}`)}},g=(0,s.LO)({uploadCsv:s.JI.input(o.z.object({fileName:o.z.string(),fileContent:o.z.string()})).mutation(async({input:e})=>{try{let r=Buffer.from(e.fileContent,"base64").toString("utf-8"),t=m(r);if(0===t.length)throw Error("No valid URLs found in the CSV file");return t.map(e=>h(e))}catch(e){throw console.error("Error processing CSV:",e),Error("Failed to process the CSV file")}}),scrapeBusiness:s.JI.input(v).mutation(async({input:e})=>{try{let r=await f(e);return y.parse(r)}catch(e){throw console.error("Error scraping business data:",e),Error("Failed to scrape business data")}})}),b=(0,s.LO)({post:n,scraper:g}),P=(0,s.OA)(b)},89544:(e,r,t)=>{"use strict";t.d(r,{TRPCReactProvider:()=>s});var o=t(12907);(0,o.registerClientReference)(function(){throw Error("Attempted to call api() from the server but api is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\trpc\\react.tsx","api");let s=(0,o.registerClientReference)(function(){throw Error("Attempted to call TRPCReactProvider() from the server but TRPCReactProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\trpc\\react.tsx","TRPCReactProvider")},90245:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>a});var o=t(37413);t(35692);var s=t(67799),n=t.n(s),i=t(89544);let a={title:"Create T3 App",description:"Generated by create-t3-app",icons:[{rel:"icon",url:"/favicon.ico"}]};function l({children:e}){return(0,o.jsx)("html",{lang:"en",className:`${n().variable}`,children:(0,o.jsx)("body",{children:(0,o.jsx)(i.TRPCReactProvider,{children:e})})})}},94558:(e,r,t)=>{Promise.resolve().then(t.bind(t,79755))}};