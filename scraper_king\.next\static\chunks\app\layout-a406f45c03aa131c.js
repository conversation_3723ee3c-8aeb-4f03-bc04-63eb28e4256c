(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{3964:(e,r,t)=>{"use strict";let i;t.d(r,{TRPCReactProvider:()=>f,F:()=>_});var a=t(5155),s=t(6715),l=t(1213),n=t(7566),d=t(2115),o=t(9177),c=t(2775),u=t(1451);let h=()=>new c.E({defaultOptions:{queries:{staleTime:3e4},dehydrate:{serializeData:o.Ay.serialize,shouldDehydrateQuery:e=>(0,u.XS)(e)||"pending"===e.state.status},hydrate:{deserializeData:o.Ay.deserialize}}});t(9509);let y=()=>(null!=i||(i=h()),i),_=(0,n.pY)();function f(e){let r=y(),[t]=(0,d.useState)(()=>_.createClient({links:[(0,l.$H)({enabled:e=>"down"===e.direction&&e.result instanceof Error}),(0,l.N9)({transformer:o.Ay,url:window.location.origin+"/api/trpc",headers:()=>{let e=new Headers;return e.set("x-trpc-source","nextjs-react"),e}})]}));return(0,a.jsx)(s.QueryClientProvider,{client:r,children:(0,a.jsx)(_.Provider,{client:t,queryClient:r,children:e.children})})}},5786:()=>{},5902:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},5962:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,5786,23)),Promise.resolve().then(t.t.bind(t,5902,23)),Promise.resolve().then(t.bind(t,3964))}},e=>{var r=r=>e(e.s=r);e.O(0,[817,616,308,441,684,358],()=>r(5962)),_N_E=e.O()}]);