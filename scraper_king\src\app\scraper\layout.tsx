import { type Metadata } from "next";

export const metadata: Metadata = {
  title: "Scraper King - CSV Upload & Scraping",
  description: "Upload CSV files and scrape data from websites",
};

export default function ScraperLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <div className="min-h-screen bg-gradient-to-b from-[#2e026d] to-[#15162c] text-white">
      <div className="container mx-auto px-4 py-8">
        <header className="mb-8">
          <h1 className="text-4xl font-extrabold tracking-tight sm:text-5xl">
            Scraper <span className="text-[hsl(280,100%,70%)]">King</span>
          </h1>
          <p className="mt-2 text-lg text-white/70">
            Upload CSV files and scrape data from websites
          </p>
        </header>
        {children}
      </div>
    </div>
  );
}
