"use strict";exports.id=788,exports.ids=[788],exports.modules={122:(e,t,r)=>{r.d(t,{G:()=>d});var n=r(6894),a=r(90043),s=r(13495),i=r(63334);function o(e){let t=null,r=s.IT;return{read:async()=>(r!==s.IT||(null===t&&(t=e().catch(e=>{if(e instanceof n.gt)throw e;throw new n.gt({code:"BAD_REQUEST",message:e instanceof Error?e.message:"Invalid input",cause:e})})),r=await t,t=null),r),result:()=>r!==s.IT?r:void 0}}let u={isMatch:e=>!!e.headers.get("content-type")?.startsWith("application/json"),async parse(e){let{req:t}=e,r="1"===e.searchParams.get("batch"),u=r?e.path.split(","):[e.path],l=o(async()=>{let a;if("GET"===t.method){let t=e.searchParams.get("input");t&&(a=JSON.parse(t))}else a=await t.json();if(void 0===a)return{};if(!r)return{0:e.router._def._config.transformer.input.deserialize(a)};if(!(0,s.Gv)(a))throw new n.gt({code:"BAD_REQUEST",message:'"input" needs to be an object when doing a batch call'});let i={};for(let t of u.keys()){let r=a[t];void 0!==r&&(i[t]=e.router._def._config.transformer.input.deserialize(r))}return i}),d=await Promise.all(u.map(async(t,r)=>{let n=await (0,a.Iw)(e.router,t);return{path:t,procedure:n,getRawInput:async()=>{let t=(await l.read())[r];if(n?._def.type==="subscription"){let r=e.headers.get("last-event-id")??e.searchParams.get("lastEventId")??e.searchParams.get("Last-Event-Id");r&&((0,s.Gv)(t)?t={...t,lastEventId:r}:t??(t={lastEventId:r}))}return t},result:()=>l.result()?.[r]}})),c=new Set(d.map(e=>e.procedure?._def.type).filter(Boolean));if(c.size>1)throw new n.gt({code:"BAD_REQUEST",message:`Cannot mix procedure types in call: ${Array.from(c).join(", ")}`});let p=c.values().next().value??"unknown",f=e.searchParams.get("connectionParams");return{isBatchCall:r,accept:t.headers.get("trpc-accept"),calls:d,type:p,connectionParams:null===f?null:(0,i.z)(f),signal:t.signal,url:e.url}}},l=[u,{isMatch:e=>!!e.headers.get("content-type")?.startsWith("multipart/form-data"),async parse(e){let{req:t}=e;if("POST"!==t.method)throw new n.gt({code:"METHOD_NOT_SUPPORTED",message:"Only POST requests are supported for multipart/form-data requests"});let r=o(async()=>await t.formData()),s=await (0,a.Iw)(e.router,e.path);return{accept:null,calls:[{path:e.path,getRawInput:r.read,result:r.result,procedure:s}],isBatchCall:!1,type:"mutation",connectionParams:null,signal:t.signal,url:e.url}}},{isMatch:e=>!!e.headers.get("content-type")?.startsWith("application/octet-stream"),async parse(e){let{req:t}=e;if("POST"!==t.method)throw new n.gt({code:"METHOD_NOT_SUPPORTED",message:"Only POST requests are supported for application/octet-stream requests"});let r=o(async()=>t.body);return{calls:[{path:e.path,getRawInput:r.read,result:r.result,procedure:await (0,a.Iw)(e.router,e.path)}],isBatchCall:!1,accept:null,type:"mutation",connectionParams:null,signal:t.signal,url:e.url}}}];async function d(e){let t=function(e){let t=l.find(t=>t.isMatch(e));if(t)return t;if(!t&&"GET"===e.method)return u;throw new n.gt({code:"UNSUPPORTED_MEDIA_TYPE",message:e.headers.has("content-type")?`Unsupported content-type "${e.headers.get("content-type")}`:"Missing content-type header"})}(e.req);return await t.parse(e)}},3271:(e,t,r)=>{r.d(t,{vJ:()=>a});let n=Symbol();function a(e){return Array.isArray(e)&&e[2]===n}},6894:(e,t,r)=>{r.d(t,{gt:()=>o,qO:()=>i});var n=r(13495);function a(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class s extends Error{}function i(e){if(e instanceof o||e instanceof Error&&"TRPCError"===e.name)return e;let t=new o({code:"INTERNAL_SERVER_ERROR",cause:e});return e instanceof Error&&e.stack&&(t.stack=e.stack),t}class o extends Error{constructor(e){let t=function(e){if(e instanceof Error)return e;let t=typeof e;if("undefined"!==t&&"function"!==t&&null!==e){if("object"!==t)return Error(String(e));if((0,n.Gv)(e)){let t=new s;for(let r in e)t[r]=e[r];return t}}}(e.cause);super(e.message??t?.message??e.code,{cause:t}),a(this,"cause",void 0),a(this,"code",void 0),this.code=e.code,this.name="TRPCError",this.cause||(this.cause=t)}}},7128:(e,t,r)=>{r.d(t,{_:()=>s,s:()=>a});var n=r(35355);let a=Symbol();function s(e){let t=null;return(0,n.T)({start(){if(t)throw Error("Timer already started");return new Promise(r=>{t=setTimeout(()=>r(a),e)})}},()=>{t&&clearTimeout(t)})}},7982:(e,t,r)=>{r.d(t,{Y:()=>n,u:()=>a});let n={PARSE_ERROR:-32700,BAD_REQUEST:-32600,INTERNAL_SERVER_ERROR:-32603,NOT_IMPLEMENTED:-32603,BAD_GATEWAY:-32603,SERVICE_UNAVAILABLE:-32603,GATEWAY_TIMEOUT:-32603,UNAUTHORIZED:-32001,FORBIDDEN:-32003,NOT_FOUND:-32004,METHOD_NOT_SUPPORTED:-32005,TIMEOUT:-32008,CONFLICT:-32009,PRECONDITION_FAILED:-32012,PAYLOAD_TOO_LARGE:-32013,UNSUPPORTED_MEDIA_TYPE:-32015,UNPROCESSABLE_CONTENT:-32022,TOO_MANY_REQUESTS:-32029,CLIENT_CLOSED_REQUEST:-32099},a={[-32700]:"PARSE_ERROR",[-32600]:"BAD_REQUEST",[-32603]:"INTERNAL_SERVER_ERROR",[-32001]:"UNAUTHORIZED",[-32003]:"FORBIDDEN",[-32004]:"NOT_FOUND",[-32005]:"METHOD_NOT_SUPPORTED",[-32008]:"TIMEOUT",[-32009]:"CONFLICT",[-32012]:"PRECONDITION_FAILED",[-32013]:"PAYLOAD_TOO_LARGE",[-32015]:"UNSUPPORTED_MEDIA_TYPE",[-32022]:"UNPROCESSABLE_CONTENT",[-32029]:"TOO_MANY_REQUESTS",[-32099]:"CLIENT_CLOSED_REQUEST"}},8096:(e,t,r)=>{r.d(t,{E$:()=>i,aV:()=>o});var n=r(7982),a=r(13495);let s={PARSE_ERROR:400,BAD_REQUEST:400,UNAUTHORIZED:401,FORBIDDEN:403,NOT_FOUND:404,METHOD_NOT_SUPPORTED:405,TIMEOUT:408,CONFLICT:409,PRECONDITION_FAILED:412,PAYLOAD_TOO_LARGE:413,UNSUPPORTED_MEDIA_TYPE:415,UNPROCESSABLE_CONTENT:422,TOO_MANY_REQUESTS:429,CLIENT_CLOSED_REQUEST:499,INTERNAL_SERVER_ERROR:500,NOT_IMPLEMENTED:501,BAD_GATEWAY:502,SERVICE_UNAVAILABLE:503,GATEWAY_TIMEOUT:504};function i(e){let t=new Set((Array.isArray(e)?e:[e]).map(e=>{if("error"in e&&(0,a.Gv)(e.error.data))return"number"==typeof e.error.data?.httpStatus?e.error.data.httpStatus:s[n.u[e.error.code]]??500;return 200}));return 1!==t.size?207:t.values().next().value}function o(e){return s[e.code]??500}},13495:(e,t,r)=>{r.d(t,{D_:()=>c,Gv:()=>s,IT:()=>n,QQ:()=>o,Td:()=>l,Tn:()=>i,eF:()=>d,uf:()=>a});let n=Symbol();function a(e,...t){let r=Object.assign(Object.create(null),e);for(let e of t)for(let t in e){if(t in r&&r[t]!==e[t])throw Error(`Duplicate key ${t}`);r[t]=e[t]}return r}function s(e){return!!e&&!Array.isArray(e)&&"object"==typeof e}function i(e){return"function"==typeof e}function o(e){return Object.assign(Object.create(null),e)}let u="function"==typeof Symbol&&!!Symbol.asyncIterator;function l(e){return u&&s(e)&&Symbol.asyncIterator in e}let d=e=>e();function c(e){return e}},14406:(e,t,r)=>{r.d(t,{yL:()=>p,jU:()=>m});var n=r(13495),a=r(28681),s=r(35355);function i(){let e,t;return{promise:new Promise((r,n)=>{e=r,t=n}),resolve:e,reject:t}}function o(e){var t="function"==typeof SuppressedError?SuppressedError:function(e,t,r){var n=Error(r);return n.name="SuppressedError",n.error=e,n.suppressed=t,n};return(o=function(e){function r(r){e.error=e.hasError?new t(r,e.error,"An error was suppressed during disposal."):r,e.hasError=!0}var n,a=0;return function t(){for(;n=e.stack.pop();)try{if(!n.async&&1===a)return a=0,e.stack.push(n),Promise.resolve().then(t);if(n.dispose){var s=n.dispose.call(n.value);if(n.async)return a|=2,Promise.resolve(s).then(t,function(e){return r(e),t()})}else a|=1}catch(e){r(e)}if(1===a)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()})(e)}var u=r(74329),l=r(84065);function d(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function c(e){var t="function"==typeof SuppressedError?SuppressedError:function(e,t,r){var n=Error(r);return n.name="SuppressedError",n.error=e,n.suppressed=t,n};return(c=function(e){function r(r){e.error=e.hasError?new t(r,e.error,"An error was suppressed during disposal."):r,e.hasError=!0}var n,a=0;return function t(){for(;n=e.stack.pop();)try{if(!n.async&&1===a)return a=0,e.stack.push(n),Promise.resolve().then(t);if(n.dispose){var s=n.dispose.call(n.value);if(n.async)return a|=2,Promise.resolve(s).then(t,function(e){return r(e),t()})}else a|=1}catch(e){r(e)}if(1===a)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()})(e)}function p(e){return((0,n.Gv)(e)||(0,n.Tn)(e))&&"function"==typeof e?.then&&"function"==typeof e?.catch}class f extends Error{constructor(e){super("Max depth reached at path: "+e.join(".")),d(this,"path",void 0),this.path=e}}async function*h(e){let{data:t}=e,r=0,u=function(){let e="idle",t=i(),r=[],n=new Set,a=[];function u(r){if("pending"!==e)return;let s=function(e,t){let r=e[Symbol.asyncIterator](),n="idle";function a(){n="done",t=()=>{}}return{pull:function(){"idle"===n&&(n="pending",r.next().then(e=>{if(e.done){n="done",t({status:"return",value:e.value}),a();return}n="idle",t({status:"yield",value:e.value})}).catch(e=>{t({status:"error",error:e}),a()}))},destroy:async()=>{a(),await r.return?.()}}}(r,r=>{if("pending"===e){switch(r.status){case"yield":a.push([s,r]);break;case"return":n.delete(s);break;case"error":a.push([s,r]),n.delete(s)}t.resolve()}});n.add(s),s.pull()}return{add(t){switch(e){case"idle":r.push(t);break;case"pending":u(t)}},async *[Symbol.asyncIterator](){let l={stack:[],error:void 0,hasError:!1};try{if("idle"!==e)throw Error("Cannot iterate twice");for(e="pending",!function(e,t,r){if(null!=t){var n,a;if("object"!=typeof t&&"function"!=typeof t)throw TypeError("Object expected.");if(!Symbol.asyncDispose)throw TypeError("Symbol.asyncDispose is not defined.");if(void 0===(n=t[Symbol.asyncDispose])){if(!Symbol.dispose)throw TypeError("Symbol.dispose is not defined.");a=n=t[Symbol.dispose]}if("function"!=typeof n)throw TypeError("Object not disposable.");a&&(n=function(){try{a.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:t,dispose:n,async:r})}else e.stack.push({async:!0})}(l,(0,s.j)({},async()=>{e="done";let r=[];if(await Promise.all(Array.from(n.values()).map(async e=>{try{await e.destroy()}catch(e){r.push(e)}})),a.length=0,n.clear(),t.resolve(),r.length>0)throw AggregateError(r)}),!0);r.length>0;)u(r.shift());for(;n.size>0;){for(await t.promise;a.length>0;){let[e,t]=a.shift();switch(t.status){case"yield":yield t.value,e.pull();break;case"error":throw t.error}}t=i()}}catch(e){l.error=e,l.hasError=!0}finally{let e=o(l);e&&await e}}}}();function d(e){let t=r++,n=e(t);return u.add(n),t}function h(t){return e.maxDepth&&t.length>e.maxDepth?new f(t):null}function m(t,r){var s;if(p(t))return[0,(s=t,d(async function*(t){let n=h(r);n&&(s.catch(t=>{e.onError?.({error:t,path:r})}),s=Promise.reject(n));try{let e=await s;yield[t,0,y(e,r)]}catch(n){e.onError?.({error:n,path:r}),yield[t,1,e.formatError?.({error:n,path:r})]}}))];if((0,n.Td)(t)){if(e.maxDepth&&r.length>=e.maxDepth)throw Error("Max depth reached");return[1,d(async function*(n){let s={stack:[],error:void 0,hasError:!1};try{let i=h(r);if(i)throw i;let o=function(e,t,r){if(null!=t){var n,a;if("object"!=typeof t&&"function"!=typeof t)throw TypeError("Object expected.");if(r){if(!Symbol.asyncDispose)throw TypeError("Symbol.asyncDispose is not defined.");n=t[Symbol.asyncDispose]}if(void 0===n){if(!Symbol.dispose)throw TypeError("Symbol.dispose is not defined.");n=t[Symbol.dispose],r&&(a=n)}if("function"!=typeof n)throw TypeError("Object not disposable.");a&&(n=function(){try{a.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:t,dispose:n,async:r})}else r&&e.stack.push({async:!0});return t}(s,(0,a.X4)(t),!0);try{for(;;){let e=await o.next();if(e.done){yield[n,0,y(e.value,r)];break}yield[n,1,y(e.value,r)]}}catch(t){e.onError?.({error:t,path:r}),yield[n,2,e.formatError?.({error:t,path:r})]}}catch(e){s.error=e,s.hasError=!0}finally{let e=c(s);e&&await e}})]}return null}function y(e,t){if(void 0===e)return[[]];let r=m(e,t);if(r)return[[0],[null,...r]];if("[object Object]"!==Object.prototype.toString.call(e))return[[e]];let n={},a=[];for(let[r,s]of Object.entries(e)){let e=m(s,[...t,r]);if(!e){n[r]=s;continue}n[r]=0,a.push([r,...e])}return[[n],...a]}let v={};for(let[e,r]of Object.entries(t))v[e]=y(r,[e]);yield v;let g=u;for await(let t of(e.pingMs&&(g=(0,l.r)(u,e.pingMs)),g))yield t}function m(e){let t=(0,u.d)(h(e)),{serialize:r}=e;return r&&(t=t.pipeThrough(new TransformStream({transform(e,t){e===l.J?t.enqueue(l.J):t.enqueue(r(e))}}))),t.pipeThrough(new TransformStream({transform(e,t){e===l.J?t.enqueue(" "):t.enqueue(JSON.stringify(e)+"\n")}})).pipeThrough(new TextEncoderStream)}},23041:(e,t,r)=>{var n,a,s,i,o;r.d(t,{Ay:()=>ee});class u{constructor(){this.keyToValue=new Map,this.valueToKey=new Map}set(e,t){this.keyToValue.set(e,t),this.valueToKey.set(t,e)}getByKey(e){return this.keyToValue.get(e)}getByValue(e){return this.valueToKey.get(e)}clear(){this.keyToValue.clear(),this.valueToKey.clear()}}class l{constructor(e){this.generateIdentifier=e,this.kv=new u}register(e,t){this.kv.getByValue(e)||(t||(t=this.generateIdentifier(e)),this.kv.set(t,e))}clear(){this.kv.clear()}getIdentifier(e){return this.kv.getByValue(e)}getValue(e){return this.kv.getByKey(e)}}class d extends l{constructor(){super(e=>e.name),this.classToAllowedProps=new Map}register(e,t){"object"==typeof t?(t.allowProps&&this.classToAllowedProps.set(e,t.allowProps),super.register(e,t.identifier)):super.register(e,t)}getAllowedProps(e){return this.classToAllowedProps.get(e)}}function c(e,t){Object.entries(e).forEach(([e,r])=>t(r,e))}function p(e,t){return -1!==e.indexOf(t)}function f(e,t){for(let r=0;r<e.length;r++){let n=e[r];if(t(n))return n}}class h{constructor(){this.transfomers={}}register(e){this.transfomers[e.name]=e}findApplicable(e){return function(e,t){let r=function(e){if("values"in Object)return Object.values(e);let t=[];for(let r in e)e.hasOwnProperty(r)&&t.push(e[r]);return t}(e);if("find"in r)return r.find(t);for(let e=0;e<r.length;e++){let n=r[e];if(t(n))return n}}(this.transfomers,t=>t.isApplicable(e))}findByName(e){return this.transfomers[e]}}let m=e=>Object.prototype.toString.call(e).slice(8,-1),y=e=>void 0===e,v=e=>null===e,g=e=>"object"==typeof e&&null!==e&&e!==Object.prototype&&(null===Object.getPrototypeOf(e)||Object.getPrototypeOf(e)===Object.prototype),_=e=>g(e)&&0===Object.keys(e).length,b=e=>Array.isArray(e),w=e=>"string"==typeof e,E=e=>"number"==typeof e&&!isNaN(e),k=e=>"boolean"==typeof e,O=e=>e instanceof Map,T=e=>e instanceof Set,x=e=>"Symbol"===m(e),S=e=>"number"==typeof e&&isNaN(e),A=e=>k(e)||v(e)||y(e)||E(e)||w(e)||x(e),R=e=>e===1/0||e===-1/0,P=e=>e.replace(/\./g,"\\."),N=e=>e.map(String).map(P).join("."),j=e=>{let t=[],r="";for(let n=0;n<e.length;n++){let a=e.charAt(n);if("\\"===a&&"."===e.charAt(n+1)){r+=".",n++;continue}if("."===a){t.push(r),r="";continue}r+=a}let n=r;return t.push(n),t};function I(e,t,r,n){return{isApplicable:e,annotation:t,transform:r,untransform:n}}let Z=[I(y,"undefined",()=>null,()=>void 0),I(e=>"bigint"==typeof e,"bigint",e=>e.toString(),e=>"undefined"!=typeof BigInt?BigInt(e):(console.error("Please add a BigInt polyfill."),e)),I(e=>e instanceof Date&&!isNaN(e.valueOf()),"Date",e=>e.toISOString(),e=>new Date(e)),I(e=>e instanceof Error,"Error",(e,t)=>{let r={name:e.name,message:e.message};return t.allowedErrorProps.forEach(t=>{r[t]=e[t]}),r},(e,t)=>{let r=Error(e.message);return r.name=e.name,r.stack=e.stack,t.allowedErrorProps.forEach(t=>{r[t]=e[t]}),r}),I(e=>e instanceof RegExp,"regexp",e=>""+e,e=>new RegExp(e.slice(1,e.lastIndexOf("/")),e.slice(e.lastIndexOf("/")+1))),I(T,"set",e=>[...e.values()],e=>new Set(e)),I(O,"map",e=>[...e.entries()],e=>new Map(e)),I(e=>S(e)||R(e),"number",e=>S(e)?"NaN":e>0?"Infinity":"-Infinity",Number),I(e=>0===e&&1/e==-1/0,"number",()=>"-0",Number),I(e=>e instanceof URL,"URL",e=>e.toString(),e=>new URL(e))];function C(e,t,r,n){return{isApplicable:e,annotation:t,transform:r,untransform:n}}let D=C((e,t)=>!!x(e)&&!!t.symbolRegistry.getIdentifier(e),(e,t)=>["symbol",t.symbolRegistry.getIdentifier(e)],e=>e.description,(e,t,r)=>{let n=r.symbolRegistry.getValue(t[1]);if(!n)throw Error("Trying to deserialize unknown symbol");return n}),M=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,Uint8ClampedArray].reduce((e,t)=>(e[t.name]=t,e),{}),U=C(e=>ArrayBuffer.isView(e)&&!(e instanceof DataView),e=>["typed-array",e.constructor.name],e=>[...e],(e,t)=>{let r=M[t[1]];if(!r)throw Error("Trying to deserialize unknown typed array");return new r(e)});function $(e,t){return!!e?.constructor&&!!t.classRegistry.getIdentifier(e.constructor)}let L=C($,(e,t)=>["class",t.classRegistry.getIdentifier(e.constructor)],(e,t)=>{let r=t.classRegistry.getAllowedProps(e.constructor);if(!r)return{...e};let n={};return r.forEach(t=>{n[t]=e[t]}),n},(e,t,r)=>{let n=r.classRegistry.getValue(t[1]);if(!n)throw Error(`Trying to deserialize unknown class '${t[1]}' - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564`);return Object.assign(Object.create(n.prototype),e)}),z=C((e,t)=>!!t.customTransformerRegistry.findApplicable(e),(e,t)=>["custom",t.customTransformerRegistry.findApplicable(e).name],(e,t)=>t.customTransformerRegistry.findApplicable(e).serialize(e),(e,t,r)=>{let n=r.customTransformerRegistry.findByName(t[1]);if(!n)throw Error("Trying to deserialize unknown custom value");return n.deserialize(e)}),F=[L,D,z,U],V=(e,t)=>{let r=f(F,r=>r.isApplicable(e,t));if(r)return{value:r.transform(e,t),type:r.annotation(e,t)};let n=f(Z,r=>r.isApplicable(e,t));if(n)return{value:n.transform(e,t),type:n.annotation}},B={};Z.forEach(e=>{B[e.annotation]=e});let q=(e,t,r)=>{if(b(t))switch(t[0]){case"symbol":return D.untransform(e,t,r);case"class":return L.untransform(e,t,r);case"custom":return z.untransform(e,t,r);case"typed-array":return U.untransform(e,t,r);default:throw Error("Unknown transformation: "+t)}{let n=B[t];if(!n)throw Error("Unknown transformation: "+t);return n.untransform(e,r)}},K=(e,t)=>{if(t>e.size)throw Error("index out of bounds");let r=e.keys();for(;t>0;)r.next(),t--;return r.next().value};function J(e){if(p(e,"__proto__"))throw Error("__proto__ is not allowed as a property");if(p(e,"prototype"))throw Error("prototype is not allowed as a property");if(p(e,"constructor"))throw Error("constructor is not allowed as a property")}let G=(e,t)=>{J(t);for(let r=0;r<t.length;r++){let n=t[r];if(T(e))e=K(e,+n);else if(O(e)){let a=+n,s=0==+t[++r]?"key":"value",i=K(e,a);switch(s){case"key":e=i;break;case"value":e=e.get(i)}}else e=e[n]}return e},W=(e,t,r)=>{if(J(t),0===t.length)return r(e);let n=e;for(let e=0;e<t.length-1;e++){let r=t[e];if(b(n))n=n[+r];else if(g(n))n=n[r];else if(T(n))n=K(n,+r);else if(O(n)){if(e===t.length-2)break;let a=+r,s=0==+t[++e]?"key":"value",i=K(n,a);switch(s){case"key":n=i;break;case"value":n=n.get(i)}}}let a=t[t.length-1];if(b(n)?n[+a]=r(n[+a]):g(n)&&(n[a]=r(n[a])),T(n)){let e=K(n,+a),t=r(e);e!==t&&(n.delete(e),n.add(t))}if(O(n)){let e=K(n,+t[t.length-2]);switch(0==+a?"key":"value"){case"key":{let t=r(e);n.set(t,n.get(e)),t!==e&&n.delete(e);break}case"value":n.set(e,r(n.get(e)))}}return e},Y=(e,t)=>g(e)||b(e)||O(e)||T(e)||$(e,t),Q=(e,t,r,n,a=[],s=[],i=new Map)=>{let o=A(e);if(!o){!function(e,t,r){let n=r.get(e);n?n.push(t):r.set(e,[t])}(e,a,t);let r=i.get(e);if(r)return n?{transformedValue:null}:r}if(!Y(e,r)){let t=V(e,r),n=t?{transformedValue:t.value,annotations:[t.type]}:{transformedValue:e};return o||i.set(e,n),n}if(p(s,e))return{transformedValue:null};let u=V(e,r),l=u?.value??e,d=b(l)?[]:{},f={};c(l,(o,u)=>{if("__proto__"===u||"constructor"===u||"prototype"===u)throw Error(`Detected property ${u}. This is a prototype pollution risk, please remove it from your object.`);let l=Q(o,t,r,n,[...a,u],[...s,e],i);d[u]=l.transformedValue,b(l.annotations)?f[u]=l.annotations:g(l.annotations)&&c(l.annotations,(e,t)=>{f[P(u)+"."+t]=e})});let h=_(f)?{transformedValue:d,annotations:u?[u.type]:void 0}:{transformedValue:d,annotations:u?[u.type,f]:f};return o||i.set(e,h),h};function H(e){return Object.prototype.toString.call(e).slice(8,-1)}function X(e){return"Array"===H(e)}n=function(e){return"Null"===H(e)},a=function(e){return"Undefined"===H(e)};class ee{constructor({dedupe:e=!1}={}){this.classRegistry=new d,this.symbolRegistry=new l(e=>e.description??""),this.customTransformerRegistry=new h,this.allowedErrorProps=[],this.dedupe=e}serialize(e){let t=new Map,r=Q(e,t,this,this.dedupe),n={json:r.transformedValue};r.annotations&&(n.meta={...n.meta,values:r.annotations});let a=function(e,t){let r,n={};return(e.forEach(e=>{if(e.length<=1)return;t||(e=e.map(e=>e.map(String)).sort((e,t)=>e.length-t.length));let[a,...s]=e;0===a.length?r=s.map(N):n[N(a)]=s.map(N)}),r)?_(n)?[r]:[r,n]:_(n)?void 0:n}(t,this.dedupe);return a&&(n.meta={...n.meta,referentialEqualities:a}),n}deserialize(e){var t,r,n;let{json:a,meta:s}=e,i=function e(t,r={}){return X(t)?t.map(t=>e(t,r)):!function(e){if("Object"!==H(e))return!1;let t=Object.getPrototypeOf(e);return!!t&&t.constructor===Object&&t===Object.prototype}(t)?t:[...Object.getOwnPropertyNames(t),...Object.getOwnPropertySymbols(t)].reduce((n,a)=>{if(X(r.props)&&!r.props.includes(a))return n;let s=e(t[a],r);var i=r.nonenumerable;let o=({}).propertyIsEnumerable.call(t,a)?"enumerable":"nonenumerable";return"enumerable"===o&&(n[a]=s),i&&"nonenumerable"===o&&Object.defineProperty(n,a,{value:s,enumerable:!1,writable:!0,configurable:!0}),n},{})}(a);return s?.values&&(t=i,r=s.values,n=this,function e(t,r,n=[]){if(!t)return;if(!b(t))return void c(t,(t,a)=>e(t,r,[...n,...j(a)]));let[a,s]=t;s&&c(s,(t,a)=>{e(t,r,[...n,...j(a)])}),r(a,n)}(r,(e,r)=>{t=W(t,r,t=>q(t,e,n))}),i=t),s?.referentialEqualities&&(i=function(e,t){function r(t,r){let n=G(e,j(r));t.map(j).forEach(t=>{e=W(e,t,()=>n)})}if(b(t)){let[n,a]=t;n.forEach(t=>{e=W(e,j(t),()=>e)}),a&&c(a,r)}else c(t,r);return e}(i,s.referentialEqualities)),i}stringify(e){return JSON.stringify(this.serialize(e))}parse(e){return this.deserialize(JSON.parse(e))}registerClass(e,t){this.classRegistry.register(e,t)}registerSymbol(e,t){this.symbolRegistry.register(e,t)}registerCustom(e,t){this.customTransformerRegistry.register({name:t,...e})}allowErrorProps(...e){this.allowedErrorProps.push(...e)}}ee.defaultInstance=new ee,ee.serialize=ee.defaultInstance.serialize.bind(ee.defaultInstance),ee.deserialize=ee.defaultInstance.deserialize.bind(ee.defaultInstance),ee.stringify=ee.defaultInstance.stringify.bind(ee.defaultInstance),ee.parse=ee.defaultInstance.parse.bind(ee.defaultInstance),ee.registerClass=ee.defaultInstance.registerClass.bind(ee.defaultInstance),ee.registerSymbol=ee.defaultInstance.registerSymbol.bind(ee.defaultInstance),ee.registerCustom=ee.defaultInstance.registerCustom.bind(ee.defaultInstance),ee.allowErrorProps=ee.defaultInstance.allowErrorProps.bind(ee.defaultInstance),ee.serialize,ee.deserialize,ee.stringify,ee.parse,ee.registerClass,ee.registerCustom,ee.registerSymbol,ee.allowErrorProps},23271:(e,t,r)=>{r.d(t,{A:()=>d});var n=r(74157),a=r(59376),s=r(72736),i=r(58218),o=r(90043),u=r(46049);class l{context(){return new l}meta(){return new l}create(e){let t={...e,transformer:(0,u.u$)(e?.transformer??u.bJ),isDev:e?.isDev??globalThis.process?.env.NODE_ENV!=="production",allowOutsideOfServer:e?.allowOutsideOfServer??!1,errorFormatter:e?.errorFormatter??n.E,isServer:e?.isServer??i.N,$types:null};if(!(e?.isServer??i.N)&&e?.allowOutsideOfServer!==!0)throw Error("You're trying to use @trpc/server in a non-server environment. This is not supported by default.");return{_config:t,procedure:(0,s.I)({meta:e?.defaultMeta}),middleware:(0,a.py)(),router:(0,o.OX)(t),mergeRouters:o.ri,createCallerFactory:(0,o.OA)()}}}let d=new l},28681:(e,t,r)=>{r.d(t,{C$:()=>c,H9:()=>d,X4:()=>l});var n=r(30785),a=r(41443),s=r(35355),i=r(7128);function o(e,t,r){if(null!=t){var n,a;if("object"!=typeof t&&"function"!=typeof t)throw TypeError("Object expected.");if(r){if(!Symbol.asyncDispose)throw TypeError("Symbol.asyncDispose is not defined.");n=t[Symbol.asyncDispose]}if(void 0===n){if(!Symbol.dispose)throw TypeError("Symbol.dispose is not defined.");n=t[Symbol.dispose],r&&(a=n)}if("function"!=typeof n)throw TypeError("Object not disposable.");a&&(n=function(){try{a.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:t,dispose:n,async:r})}else r&&e.stack.push({async:!0});return t}function u(e){var t="function"==typeof SuppressedError?SuppressedError:function(e,t,r){var n=Error(r);return n.name="SuppressedError",n.error=e,n.suppressed=t,n};return(u=function(e){function r(r){e.error=e.hasError?new t(r,e.error,"An error was suppressed during disposal."):r,e.hasError=!0}var n,a=0;return function t(){for(;n=e.stack.pop();)try{if(!n.async&&1===a)return a=0,e.stack.push(n),Promise.resolve().then(t);if(n.dispose){var s=n.dispose.call(n.value);if(n.async)return a|=2,Promise.resolve(s).then(t,function(e){return r(e),t()})}else a|=1}catch(e){r(e)}if(1===a)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()})(e)}function l(e){let t=e[Symbol.asyncIterator]();return(0,s.j)(t,async()=>{await t.return?.()})}async function*d(e,t){let r={stack:[],error:void 0,hasError:!1};try{let s,u=o(r,l(e),!0),d=o(r,(0,i._)(t.maxDurationMs),!1).start();for(;;){if((s=await n.V.race([u.next(),d]))===i.s&&(0,a.P)(),s.done)return s;yield s.value,s=null}}catch(e){r.error=e,r.hasError=!0}finally{let e=u(r);e&&await e}}async function*c(e,t){let r={stack:[],error:void 0,hasError:!1};try{let s,u=o(r,l(e),!0),d=o(r,(0,i._)(t.gracePeriodMs),!1),c=t.count,p=new Promise(()=>{});for(;;){if((s=await n.V.race([u.next(),p]))===i.s&&(0,a.P)(),s.done)return s.value;yield s.value,0==--c&&(p=d.start()),s=null}}catch(e){r.error=e,r.hasError=!0}finally{let e=u(r);e&&await e}}},30785:(e,t,r)=>{var n;function a(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}r.d(t,{V:()=>o});let s=new WeakMap,i=()=>{};n=Symbol.toStringTag;class o{subscribe(){let e,t,{settlement:r}=this;if(null===r){var n;let r,a;if(null===this.subscribers)throw Error("Unpromise settled but still has subscribers");let s={promise:new Promise((e,t)=>{r=e,a=t}),resolve:r,reject:a};this.subscribers=(n=this.subscribers,[...n,s]),e=s.promise,t=()=>{null!==this.subscribers&&(this.subscribers=function(e,t){let r=e.indexOf(t);if(-1!==r)return[...e.slice(0,r),...e.slice(r+1)];return e}(this.subscribers,s))}}else{let{status:n}=r;e="fulfilled"===n?Promise.resolve(r.value):Promise.reject(r.reason),t=i}return Object.assign(e,{unsubscribe:t})}then(e,t){let r=this.subscribe(),{unsubscribe:n}=r;return Object.assign(r.then(e,t),{unsubscribe:n})}catch(e){let t=this.subscribe(),{unsubscribe:r}=t;return Object.assign(t.catch(e),{unsubscribe:r})}finally(e){let t=this.subscribe(),{unsubscribe:r}=t;return Object.assign(t.finally(e),{unsubscribe:r})}static proxy(e){let t=o.getSubscribablePromise(e);return void 0!==t?t:o.createSubscribablePromise(e)}static createSubscribablePromise(e){let t=new o(e);return s.set(e,t),s.set(t,t),t}static getSubscribablePromise(e){return s.get(e)}static resolve(e){let t="object"==typeof e&&null!==e&&"then"in e&&"function"==typeof e.then?e:Promise.resolve(e);return o.proxy(t).subscribe()}static async any(e){let t=(Array.isArray(e)?e:[...e]).map(o.resolve);try{return await Promise.any(t)}finally{t.forEach(({unsubscribe:e})=>{e()})}}static async race(e){let t=(Array.isArray(e)?e:[...e]).map(o.resolve);try{return await Promise.race(t)}finally{t.forEach(({unsubscribe:e})=>{e()})}}static async raceReferences(e){let t=e.map(u);try{return await Promise.race(t)}finally{for(let e of t)e.unsubscribe()}}constructor(e){a(this,"promise",void 0),a(this,"subscribers",[]),a(this,"settlement",null),a(this,n,"Unpromise"),"function"==typeof e?this.promise=new Promise(e):this.promise=e;let t=this.promise.then(e=>{let{subscribers:t}=this;this.subscribers=null,this.settlement={status:"fulfilled",value:e},t?.forEach(({resolve:t})=>{t(e)})});"catch"in t&&t.catch(e=>{let{subscribers:t}=this;this.subscribers=null,this.settlement={status:"rejected",reason:e},t?.forEach(({reject:t})=>{t(e)})})}}function u(e){return o.proxy(e).then(()=>[e])}},32871:(e,t,r)=>{r.d(t,{w:()=>n});function n(e){let t="object"==typeof e.client?e.client:{},r="object"==typeof e.server?e.server:{},n=e.shared,a=e.runtimeEnv?e.runtimeEnv:{...process.env,...e.experimental__runtimeEnv};return function(e){let t=e.runtimeEnvStrict??e.runtimeEnv??process.env;if(e.emptyStringAsUndefined)for(let[e,r]of Object.entries(t))""===r&&delete t[e];if(e.skipValidation)return t;let r="object"==typeof e.client?e.client:{},n="object"==typeof e.server?e.server:{},a="object"==typeof e.shared?e.shared:{},s=e.isServer??("undefined"==typeof window||"Deno"in window),i=function(e,t){let r={},n=[];for(let a in e){let s=e[a],i=t[a],o=s["~standard"].validate(i);if(o instanceof Promise)throw Error(`Validation must be synchronous, but ${a} returned a Promise.`);if(o.issues){n.push(...o.issues.map(e=>({...e,path:[a,...e.path??[]]})));continue}r[a]=o.value}return n.length?{issues:n}:{value:r}}(s?{...n,...a,...r}:{...r,...a},t),o=e.onValidationError??(e=>{throw console.error("❌ Invalid environment variables:",e),Error("Invalid environment variables")}),u=e.onInvalidAccess??(()=>{throw Error("❌ Attempted to access a server-side environment variable on the client")});if(i.issues)return o(i.issues);let l=t=>!e.clientPrefix||!t.startsWith(e.clientPrefix)&&!(t in a),d=e=>s||!l(e),c=e=>"__esModule"===e||"$$typeof"===e,p=(e.extends??[]).reduce((e,t)=>Object.assign(e,t),{});return new Proxy(Object.assign(i.value,p),{get(e,t){if("string"==typeof t&&!c(t))return d(t)?Reflect.get(e,t):u(t)}})}({...e,shared:n,client:t,server:r,clientPrefix:"NEXT_PUBLIC_",runtimeEnv:a})}},35355:(e,t,r)=>{var n,a;function s(e,t){let r=e[Symbol.dispose];return e[Symbol.dispose]=()=>{t(),r?.()},e}function i(e,t){let r=e[Symbol.asyncDispose];return e[Symbol.asyncDispose]=async()=>{await t(),await r?.()},e}r.d(t,{T:()=>s,j:()=>i}),(n=Symbol).dispose??(n.dispose=Symbol()),(a=Symbol).asyncDispose??(a.asyncDispose=Symbol())},41443:(e,t,r)=>{r.d(t,{P:()=>s,z:()=>a});var n=r(13495);function a(e){return(0,n.Gv)(e)&&"AbortError"===e.name}function s(e="AbortError"){throw new DOMException(e,"AbortError")}},43949:(e,t,r)=>{function n(e){return"object"==typeof e&&null!==e&&"subscribe"in e}r.d(t,{S:()=>g});function a(e,t){let r=(function(e,t){let r=null,n=()=>{r?.unsubscribe(),r=null,t.removeEventListener("abort",n)};return new ReadableStream({start(a){r=e.subscribe({next(e){a.enqueue({ok:!0,value:e})},error(e){a.enqueue({ok:!1,error:e}),a.close()},complete(){a.close()}}),t.aborted?n():t.addEventListener("abort",n,{once:!0})},cancel(){n()}})})(e,t).getReader(),n={async next(){let e=await r.read();if(e.done)return{value:void 0,done:!0};let{value:t}=e;if(!t.ok)throw t.error;return{value:t.value,done:!1}},return:async()=>(await r.cancel(),{value:void 0,done:!0})};return{[Symbol.asyncIterator]:()=>n}}var s=r(58830),i=r(6894),o=r(14406),u=r(55194),l=r(46049),d=r(13495),c=r(122),p=r(8096);function f(e){return(0,d.eF)(async function*(){throw e})}let h={mutation:["POST"],query:["GET"],subscription:["GET"]},m={mutation:["POST"],query:["GET","POST"],subscription:["GET","POST"]};function y(e){let{ctx:t,info:r,responseMeta:n,untransformedJSON:a,errors:s=[],headers:i}=e,o=a?(0,p.E$)(a):200,u=!a,l=u?[]:Array.isArray(a)?a:[a],d=n?.({ctx:t,info:r,paths:r?.calls.map(e=>e.path),data:l,errors:s,eagerGeneration:u,type:r?.calls.find(e=>e.procedure?._def.type)?.procedure?._def.type??"unknown"})??{};if(d.headers)if(d.headers instanceof Headers)for(let[e,t]of d.headers.entries())i.append(e,t);else for(let[e,t]of Object.entries(d.headers))if(Array.isArray(t))for(let r of t)i.append(e,r);else"string"==typeof t&&i.set(e,t);return d.status&&(o=d.status),{status:o}}function v(e){return!!(0,d.Gv)(e)&&(!!(0,d.Td)(e)||Object.values(e).some(o.yL)||Object.values(e).some(d.Td))}async function g(e){let{router:t,req:r}=e,p=new Headers([["vary","trpc-accept"]]),g=t._def._config,_=new URL(r.url);if("HEAD"===r.method)return new Response(null,{status:204});let b=e.allowBatching??e.batching?.enabled??!0,w=(e.allowMethodOverride??!1)&&"POST"===r.method,E=await (0,d.eF)(async()=>{try{return[void 0,await (0,c.G)({req:r,path:decodeURIComponent(e.path),router:t,searchParams:_.searchParams,headers:e.req.headers,url:_})]}catch(e){return[(0,i.qO)(e),void 0]}}),k=(0,d.eF)(()=>{let t;return{valueOrUndefined:()=>{if(t)return t[1]},value:()=>{let[e,r]=t;if(e)throw e;return r},create:async r=>{if(t)throw Error("This should only be called once - report a bug in tRPC");try{let n=await e.createContext({info:r});t=[void 0,n]}catch(e){t=[(0,i.qO)(e),void 0]}}}}),O=w?m:h,T="application/jsonl"===r.headers.get("trpc-accept"),x=g.sse?.enabled??!0;try{let[t,c]=E;if(t)throw t;if(c.isBatchCall&&!b)throw new i.gt({code:"BAD_REQUEST",message:"Batching is not enabled on the server"});if(T&&!c.isBatchCall)throw new i.gt({message:"Streaming requests must be batched (you can do a batch of 1)",code:"BAD_REQUEST"});await k.create(c);let h=c.calls.map(async t=>{let n=t.procedure;try{if(e.error)throw e.error;if(!n)throw new i.gt({code:"NOT_FOUND",message:`No procedure found on path "${t.path}"`});if(!O[n._def.type].includes(r.method))throw new i.gt({code:"METHOD_NOT_SUPPORTED",message:`Unsupported ${r.method}-request to ${n._def.type} procedure at path "${t.path}"`});if("subscription"===n._def.type&&c.isBatchCall)throw new i.gt({code:"BAD_REQUEST",message:"Cannot batch subscription calls"});let a=await n({path:t.path,getRawInput:t.getRawInput,ctx:k.value(),type:n._def.type,signal:e.req.signal});return[void 0,{data:a}]}catch(a){let r=(0,i.qO)(a),n=t.result();return e.onError?.({error:r,path:t.path,input:n,ctx:k.valueOrUndefined(),type:t.procedure?._def.type??"unknown",req:e.req}),[r,void 0]}});if(!c.isBatchCall){let[t]=c.calls,[r,o]=await h[0];switch(c.type){case"unknown":case"mutation":case"query":{if(p.set("content-type","application/json"),v(o?.data))throw new i.gt({code:"UNSUPPORTED_MEDIA_TYPE",message:"Cannot use stream-like response in non-streaming request - use httpBatchStreamLink"});let n=r?{error:(0,s.K)({config:g,ctx:k.valueOrUndefined(),error:r,input:t.result(),path:t.path,type:c.type})}:{result:{data:o.data}},a=y({ctx:k.valueOrUndefined(),info:c,responseMeta:e.responseMeta,errors:r?[r]:[],headers:p,untransformedJSON:[n]});return new Response(JSON.stringify((0,l.t9)(g,n)),{status:a.status,headers:p})}case"subscription":{let l=(0,d.eF)(()=>r?f(r):x?n(o.data)||(0,d.Td)(o.data)?n(o.data)?a(o.data,e.req.signal):o.data:f(new i.gt({message:`Subscription ${t.path} did not return an observable or a AsyncGenerator`,code:"INTERNAL_SERVER_ERROR"})):f(new i.gt({code:"METHOD_NOT_SUPPORTED",message:'Missing experimental flag "sseSubscriptions"'}))),h=(0,u.kN)({...g.sse,data:l,serialize:e=>g.transformer.output.serialize(e),formatError(r){let n=(0,i.qO)(r.error),a=t?.result(),o=t?.path,u=t?.procedure?._def.type??"unknown";return e.onError?.({error:n,path:o,input:a,ctx:k.valueOrUndefined(),req:e.req,type:u}),(0,s.K)({config:g,ctx:k.valueOrUndefined(),error:n,input:a,path:o,type:u})}});for(let[e,t]of Object.entries(u.w7))p.set(e,t);let m=y({ctx:k.valueOrUndefined(),info:c,responseMeta:e.responseMeta,errors:[],headers:p,untransformedJSON:null});return new Response(h,{headers:p,status:m.status})}}}if("application/jsonl"===c.accept){p.set("content-type","application/json"),p.set("transfer-encoding","chunked");let t=y({ctx:k.valueOrUndefined(),info:c,responseMeta:e.responseMeta,errors:[],headers:p,untransformedJSON:null}),r=(0,o.jU)({...g.jsonl,maxDepth:1/0,data:h.map(async t=>{let[r,i]=await t,o=c.calls[0];if(r)return{error:(0,s.K)({config:g,ctx:k.valueOrUndefined(),error:r,input:o.result(),path:o.path,type:o.procedure?._def.type??"unknown"})};let u=n(i.data)?a(i.data,e.req.signal):Promise.resolve(i.data);return{result:Promise.resolve({data:u})}}),serialize:g.transformer.output.serialize,onError:t=>{e.onError?.({error:(0,i.qO)(t),path:void 0,input:void 0,ctx:k.valueOrUndefined(),req:e.req,type:c?.type??"unknown"})},formatError(e){let t=c?.calls[e.path[0]],r=(0,i.qO)(e.error),n=t?.result(),a=t?.path,o=t?.procedure?._def.type??"unknown";return(0,s.K)({config:g,ctx:k.valueOrUndefined(),error:r,input:n,path:a,type:o})}});return new Response(r,{headers:p,status:t.status})}p.set("content-type","application/json");let m=(await Promise.all(h)).map(e=>{let[t,r]=e;return t?e:v(r.data)?[new i.gt({code:"UNSUPPORTED_MEDIA_TYPE",message:"Cannot use stream-like response in non-streaming request - use httpBatchStreamLink"}),void 0]:e}),_=m.map(([e,t],r)=>{let n=c.calls[r];return e?{error:(0,s.K)({config:g,ctx:k.valueOrUndefined(),error:e,input:n.result(),path:n.path,type:n.procedure?._def.type??"unknown"})}:{result:{data:t.data}}}),w=m.map(([e])=>e).filter(Boolean),S=y({ctx:k.valueOrUndefined(),info:c,responseMeta:e.responseMeta,untransformedJSON:_,errors:w,headers:p});return new Response(JSON.stringify((0,l.t9)(g,_)),{status:S.status,headers:p})}catch(d){let[t,r]=E,n=k.valueOrUndefined(),{error:a,untransformedJSON:o,body:u}=function(e,t){let{router:r,req:n,onError:a}=t.opts,o=(0,i.qO)(e);a?.({error:o,path:t.path,input:t.input,ctx:t.ctx,type:t.type,req:n});let u={error:(0,s.K)({config:r._def._config,error:o,type:t.type,path:t.path,input:t.input,ctx:t.ctx})},d=JSON.stringify((0,l.t9)(r._def._config,u));return{error:o,untransformedJSON:u,body:d}}(d,{opts:e,ctx:k.valueOrUndefined(),type:r?.type??"unknown"});return new Response(u,{status:y({ctx:n,info:r,responseMeta:e.responseMeta,untransformedJSON:o,errors:[a],headers:p}).status,headers:p})}}},46049:(e,t,r)=>{function n(e){return"input"in e?e:{input:e,output:e}}r.d(t,{bJ:()=>a,t9:()=>i,u$:()=>n}),r(13495);let a={input:{serialize:e=>e,deserialize:e=>e},output:{serialize:e=>e,deserialize:e=>e}};function s(e,t){return"error"in t?{...t,error:e.transformer.output.serialize(t.error)}:"data"in t.result?{...t,result:{...t.result,data:e.transformer.output.serialize(t.result.data)}}:t}function i(e,t){return Array.isArray(t)?t.map(t=>s(e,t)):s(e,t)}},50469:(e,t,r)=>{r.d(t,{Al:()=>n.A}),r(58830),r(6894),r(90043),r(30785),r(35355),r(3271),r(46049);var n=r(23271);r(59376),r(63590),r(58218)},55194:(e,t,r)=>{r.d(t,{kN:()=>c,w7:()=>p}),r(30785);var n=r(6894),a=r(41443),s=r(13495),i=r(3271),o=r(28681);r(35355);var u=r(74329);r(7128);var l=r(84065);function d(e){var t="function"==typeof SuppressedError?SuppressedError:function(e,t,r){var n=Error(r);return n.name="SuppressedError",n.error=e,n.suppressed=t,n};return(d=function(e){function r(r){e.error=e.hasError?new t(r,e.error,"An error was suppressed during disposal."):r,e.hasError=!0}var n,a=0;return function t(){for(;n=e.stack.pop();)try{if(!n.async&&1===a)return a=0,e.stack.push(n),Promise.resolve().then(t);if(n.dispose){var s=n.dispose.call(n.value);if(n.async)return a|=2,Promise.resolve(s).then(t,function(e){return r(e),t()})}else a|=1}catch(e){r(e)}if(1===a)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()})(e)}function c(e){let{serialize:t=s.D_}=e,r={enabled:e.ping?.enabled??!1,intervalMs:e.ping?.intervalMs??1e3},d=e.client??{};if(r.enabled&&d.reconnectAfterInactivityMs&&r.intervalMs>d.reconnectAfterInactivityMs)throw Error(`Ping interval must be less than client reconnect interval to prevent unnecessary reconnection - ping.intervalMs: ${r.intervalMs} client.reconnectAfterInactivityMs: ${d.reconnectAfterInactivityMs}`);async function*c(){let n,a;yield{event:"connected",data:JSON.stringify(d)};let s=e.data;for await(n of(e.emitAndEndImmediately&&(s=(0,o.C$)(s,{count:1,gracePeriodMs:1})),e.maxDurationMs&&e.maxDurationMs>0&&e.maxDurationMs!==1/0&&(s=(0,o.H9)(s,{maxDurationMs:e.maxDurationMs})),r.enabled&&r.intervalMs!==1/0&&r.intervalMs>0&&(s=(0,l.r)(s,r.intervalMs)),s)){if(n===l.J){yield{event:"ping",data:""};continue}(a=(0,i.vJ)(n)?{id:n[0],data:n[1]}:{data:n}).data=JSON.stringify(t(a.data)),yield a,n=null,a=null}}async function*p(){try{yield*c(),yield{event:"return",data:""}}catch(i){if((0,a.z)(i))return;let r=(0,n.qO)(i),s=e.formatError?.({error:r})??null;yield{event:"serialized-error",data:JSON.stringify(t(s))}}}return(0,u.d)(p()).pipeThrough(new TransformStream({transform(e,t){"event"in e&&t.enqueue(`event: ${e.event}
`),"data"in e&&t.enqueue(`data: ${e.data}
`),"id"in e&&t.enqueue(`id: ${e.id}
`),"comment"in e&&t.enqueue(`: ${e.comment}
`),t.enqueue("\n\n")}})).pipeThrough(new TextEncoderStream)}let p={"Content-Type":"text/event-stream","Cache-Control":"no-cache, no-transform","X-Accel-Buffering":"no",Connection:"keep-alive"}},58218:(e,t,r)=>{r.d(t,{N:()=>n});let n="undefined"==typeof window||"Deno"in window||globalThis.process?.env?.NODE_ENV==="test"||!!globalThis.process?.env?.JEST_WORKER_ID||!!globalThis.process?.env?.VITEST_WORKER_ID},58830:(e,t,r)=>{r.d(t,{K:()=>s});var n=r(8096),a=r(7982);function s(e){let{path:t,error:r,config:s}=e,{code:i}=e.error,o={message:r.message,code:a.Y[i],data:{code:i,httpStatus:(0,n.aV)(r)}};return s.isDev&&"string"==typeof e.error.stack&&(o.data.stack=e.error.stack),"string"==typeof t&&(o.data.path=t),s.errorFormatter({...e,shape:o})}},59376:(e,t,r)=>{r.d(t,{l$:()=>s,lL:()=>o,py:()=>i,yU:()=>u});var n=r(6894),a=r(13495);let s="middlewareMarker";function i(){return function(e){return function e(t){return{_middlewares:t,unstable_pipe:r=>e([...t,..."_middlewares"in r?r._middlewares:[r]])}}([e])}}function o(e){let t=async function(t){let r,s=await t.getRawInput();try{r=await e(s)}catch(e){throw new n.gt({code:"BAD_REQUEST",cause:e})}let i=(0,a.Gv)(t.input)&&(0,a.Gv)(r)?{...t.input,...r}:r;return t.next({input:i})};return t._type="input",t}function u(e){let t=async function({next:t}){let r=await t();if(!r.ok)return r;try{let t=await e(r.data);return{...r,data:t}}catch(e){throw new n.gt({message:"Output validation failed",code:"INTERNAL_SERVER_ERROR",cause:e})}};return t._type="output",t}},62505:(e,t,r)=>{r.d(t,{i:()=>a});var n=r(63590);function a(e){let t="~standard"in e;if("function"==typeof e&&"function"==typeof e.assert)return e.assert.bind(e);if("function"==typeof e&&!t)return e;if("function"==typeof e.parseAsync)return e.parseAsync.bind(e);if("function"==typeof e.parse)return e.parse.bind(e);if("function"==typeof e.validateSync)return e.validateSync.bind(e);if("function"==typeof e.create)return e.create.bind(e);if("function"==typeof e.assert)return t=>(e.assert(t),t);if(t)return async t=>{let r=await e["~standard"].validate(t);if(r.issues)throw new n.e(r.issues);return r.value};throw Error("Could not find a validator fn")}},63334:(e,t,r)=>{r.d(t,{z:()=>s});var n=r(6894),a=r(13495);function s(e){let t;try{t=JSON.parse(e)}catch(e){throw new n.gt({code:"PARSE_ERROR",message:"Not JSON-parsable query params",cause:e})}var r=t;try{if(null===r)return null;if(!(0,a.Gv)(r))throw Error("Expected object");let e=Object.entries(r).filter(([e,t])=>"string"!=typeof t);if(e.length>0)throw Error(`Expected connectionParams to be string values. Got ${e.map(([e,t])=>`${e}: ${typeof t}`).join(", ")}`);return r}catch(e){throw new n.gt({code:"PARSE_ERROR",message:"Invalid connection params shape",cause:e})}}},63590:(e,t,r)=>{r.d(t,{e:()=>n});class n extends Error{constructor(e){super(e[0]?.message),function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(this,"issues",void 0),this.name="SchemaError",this.issues=e}}},69648:(e,t,r)=>{r.d(t,{v:()=>s});let n=()=>{},a=e=>{Object.freeze&&Object.freeze(e)},s=e=>(function e(t,r,s){let i=r.join(".");return s[i]??(s[i]=new Proxy(n,{get(n,a){if("string"==typeof a&&"then"!==a)return e(t,[...r,a],s)},apply(e,n,s){let i=r[r.length-1],o={args:s,path:r};return"call"===i?o={args:s.length>=2?[s[1]]:[],path:r.slice(0,-1)}:"apply"===i&&(o={args:s.length>=2?s[1]:[],path:r.slice(0,-1)}),a(o.args),a(o.path),t(o)}})),s[i]})(e,[],Object.create(null))},70762:(e,t,r)=>{let n;r.d(t,{G:()=>o,z:()=>tl}),function(e){e.assertEqual=e=>e,e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let r of e)t[r]=r;return t},e.getValidEnumValues=t=>{let r=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),n={};for(let e of r)n[e]=t[e];return e.objectValues(n)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t},e.find=(e,t)=>{for(let r of e)if(t(r))return r},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(tn||(tn={})),(ta||(ta={})).mergeShapes=(e,t)=>({...e,...t});let a=tn.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),s=e=>{switch(typeof e){case"undefined":return a.undefined;case"string":return a.string;case"number":return isNaN(e)?a.nan:a.number;case"boolean":return a.boolean;case"function":return a.function;case"bigint":return a.bigint;case"symbol":return a.symbol;case"object":if(Array.isArray(e))return a.array;if(null===e)return a.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return a.promise;if("undefined"!=typeof Map&&e instanceof Map)return a.map;if("undefined"!=typeof Set&&e instanceof Set)return a.set;if("undefined"!=typeof Date&&e instanceof Date)return a.date;return a.object;default:return a.unknown}},i=tn.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class o extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},r={_errors:[]},n=e=>{for(let a of e.issues)if("invalid_union"===a.code)a.unionErrors.map(n);else if("invalid_return_type"===a.code)n(a.returnTypeError);else if("invalid_arguments"===a.code)n(a.argumentsError);else if(0===a.path.length)r._errors.push(t(a));else{let e=r,n=0;for(;n<a.path.length;){let r=a.path[n];n===a.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(t(a))):e[r]=e[r]||{_errors:[]},e=e[r],n++}}};return n(this),r}static assert(e){if(!(e instanceof o))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,tn.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},r=[];for(let n of this.issues)n.path.length>0?(t[n.path[0]]=t[n.path[0]]||[],t[n.path[0]].push(e(n))):r.push(e(n));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}o.create=e=>new o(e);let u=(e,t)=>{let r;switch(e.code){case i.invalid_type:r=e.received===a.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case i.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,tn.jsonStringifyReplacer)}`;break;case i.unrecognized_keys:r=`Unrecognized key(s) in object: ${tn.joinValues(e.keys,", ")}`;break;case i.invalid_union:r="Invalid input";break;case i.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${tn.joinValues(e.options)}`;break;case i.invalid_enum_value:r=`Invalid enum value. Expected ${tn.joinValues(e.options)}, received '${e.received}'`;break;case i.invalid_arguments:r="Invalid function arguments";break;case i.invalid_return_type:r="Invalid function return type";break;case i.invalid_date:r="Invalid date";break;case i.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:tn.assertNever(e.validation):r="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case i.too_small:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case i.too_big:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case i.custom:r="Invalid input";break;case i.invalid_intersection_types:r="Intersection results could not be merged";break;case i.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case i.not_finite:r="Number must be finite";break;default:r=t.defaultError,tn.assertNever(e)}return{message:r}},l=u;function d(){return l}let c=e=>{let{data:t,path:r,errorMaps:n,issueData:a}=e,s=[...r,...a.path||[]],i={...a,path:s};if(void 0!==a.message)return{...a,path:s,message:a.message};let o="";for(let e of n.filter(e=>!!e).slice().reverse())o=e(i,{data:t,defaultError:o}).message;return{...a,path:s,message:o}};function p(e,t){let r=d(),n=c({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,r,r===u?void 0:u].filter(e=>!!e)});e.common.issues.push(n)}class f{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let r=[];for(let n of t){if("aborted"===n.status)return h;"dirty"===n.status&&e.dirty(),r.push(n.value)}return{status:e.value,value:r}}static async mergeObjectAsync(e,t){let r=[];for(let e of t){let t=await e.key,n=await e.value;r.push({key:t,value:n})}return f.mergeObjectSync(e,r)}static mergeObjectSync(e,t){let r={};for(let n of t){let{key:t,value:a}=n;if("aborted"===t.status||"aborted"===a.status)return h;"dirty"===t.status&&e.dirty(),"dirty"===a.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==a.value||n.alwaysSet)&&(r[t.value]=a.value)}return{status:e.value,value:r}}}let h=Object.freeze({status:"aborted"}),m=e=>({status:"dirty",value:e}),y=e=>({status:"valid",value:e}),v=e=>"aborted"===e.status,g=e=>"dirty"===e.status,_=e=>"valid"===e.status,b=e=>"undefined"!=typeof Promise&&e instanceof Promise;function w(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)}function E(e,t,r,n,a){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!a)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!a:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?a.call(e,r):a?a.value=r:t.set(e,r),r}"function"==typeof SuppressedError&&SuppressedError,function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:null==e?void 0:e.message}(ts||(ts={}));class k{constructor(e,t,r,n){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=n}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let O=(e,t)=>{if(_(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new o(e.common.issues);return this._error=t,this._error}}};function T(e){if(!e)return{};let{errorMap:t,invalid_type_error:r,required_error:n,description:a}=e;if(t&&(r||n))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:a}:{errorMap:(t,a)=>{var s,i;let{message:o}=e;return"invalid_enum_value"===t.code?{message:null!=o?o:a.defaultError}:void 0===a.data?{message:null!=(s=null!=o?o:n)?s:a.defaultError}:"invalid_type"!==t.code?{message:a.defaultError}:{message:null!=(i=null!=o?o:r)?i:a.defaultError}},description:a}}class x{get description(){return this._def.description}_getType(e){return s(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:s(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new f,ctx:{common:e.parent.common,data:e.data,parsedType:s(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(b(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){var r;let n={common:{issues:[],async:null!=(r=null==t?void 0:t.async)&&r,contextualErrorMap:null==t?void 0:t.errorMap},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:s(e)},a=this._parseSync({data:e,path:n.path,parent:n});return O(n,a)}"~validate"(e){var t,r;let n={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:s(e)};if(!this["~standard"].async)try{let t=this._parseSync({data:e,path:[],parent:n});return _(t)?{value:t.value}:{issues:n.common.issues}}catch(e){(null==(r=null==(t=null==e?void 0:e.message)?void 0:t.toLowerCase())?void 0:r.includes("encountered"))&&(this["~standard"].async=!0),n.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:n}).then(e=>_(e)?{value:e.value}:{issues:n.common.issues})}async parseAsync(e,t){let r=await this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error}async safeParseAsync(e,t){let r={common:{issues:[],contextualErrorMap:null==t?void 0:t.errorMap,async:!0},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:s(e)},n=this._parse({data:e,path:r.path,parent:r});return O(r,await (b(n)?n:Promise.resolve(n)))}refine(e,t){let r=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,n)=>{let a=e(t),s=()=>n.addIssue({code:i.custom,...r(t)});return"undefined"!=typeof Promise&&a instanceof Promise?a.then(e=>!!e||(s(),!1)):!!a||(s(),!1)})}refinement(e,t){return this._refinement((r,n)=>!!e(r)||(n.addIssue("function"==typeof t?t(r,n):t),!1))}_refinement(e){return new eb({schema:this,typeName:tu.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return ew.create(this,this._def)}nullable(){return eE.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return en.create(this)}promise(){return e_.create(this,this._def)}or(e){return es.create([this,e],this._def)}and(e){return eu.create(this,e,this._def)}transform(e){return new eb({...T(this._def),schema:this,typeName:tu.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new ek({...T(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:tu.ZodDefault})}brand(){return new eS({typeName:tu.ZodBranded,type:this,...T(this._def)})}catch(e){return new eO({...T(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:tu.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return eA.create(this,e)}readonly(){return eR.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let S=/^c[^\s-]{8,}$/i,A=/^[0-9a-z]+$/,R=/^[0-9A-HJKMNP-TV-Z]{26}$/i,P=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,N=/^[a-z0-9_-]{21}$/i,j=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,I=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Z=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,C=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,D=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,M=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,U=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,$=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,L=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,z="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",F=RegExp(`^${z}$`);function V(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let r=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${r}`}function B(e){let t=`${z}T${V(e)}`,r=[];return r.push(e.local?"Z?":"Z"),e.offset&&r.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${r.join("|")})`,RegExp(`^${t}$`)}class q extends x{_parse(e){var t,r,s,o;let u;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==a.string){let t=this._getOrReturnCtx(e);return p(t,{code:i.invalid_type,expected:a.string,received:t.parsedType}),h}let l=new f;for(let a of this._def.checks)if("min"===a.kind)e.data.length<a.value&&(p(u=this._getOrReturnCtx(e,u),{code:i.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),l.dirty());else if("max"===a.kind)e.data.length>a.value&&(p(u=this._getOrReturnCtx(e,u),{code:i.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),l.dirty());else if("length"===a.kind){let t=e.data.length>a.value,r=e.data.length<a.value;(t||r)&&(u=this._getOrReturnCtx(e,u),t?p(u,{code:i.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}):r&&p(u,{code:i.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}),l.dirty())}else if("email"===a.kind)Z.test(e.data)||(p(u=this._getOrReturnCtx(e,u),{validation:"email",code:i.invalid_string,message:a.message}),l.dirty());else if("emoji"===a.kind)n||(n=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),n.test(e.data)||(p(u=this._getOrReturnCtx(e,u),{validation:"emoji",code:i.invalid_string,message:a.message}),l.dirty());else if("uuid"===a.kind)P.test(e.data)||(p(u=this._getOrReturnCtx(e,u),{validation:"uuid",code:i.invalid_string,message:a.message}),l.dirty());else if("nanoid"===a.kind)N.test(e.data)||(p(u=this._getOrReturnCtx(e,u),{validation:"nanoid",code:i.invalid_string,message:a.message}),l.dirty());else if("cuid"===a.kind)S.test(e.data)||(p(u=this._getOrReturnCtx(e,u),{validation:"cuid",code:i.invalid_string,message:a.message}),l.dirty());else if("cuid2"===a.kind)A.test(e.data)||(p(u=this._getOrReturnCtx(e,u),{validation:"cuid2",code:i.invalid_string,message:a.message}),l.dirty());else if("ulid"===a.kind)R.test(e.data)||(p(u=this._getOrReturnCtx(e,u),{validation:"ulid",code:i.invalid_string,message:a.message}),l.dirty());else if("url"===a.kind)try{new URL(e.data)}catch(t){p(u=this._getOrReturnCtx(e,u),{validation:"url",code:i.invalid_string,message:a.message}),l.dirty()}else"regex"===a.kind?(a.regex.lastIndex=0,a.regex.test(e.data)||(p(u=this._getOrReturnCtx(e,u),{validation:"regex",code:i.invalid_string,message:a.message}),l.dirty())):"trim"===a.kind?e.data=e.data.trim():"includes"===a.kind?e.data.includes(a.value,a.position)||(p(u=this._getOrReturnCtx(e,u),{code:i.invalid_string,validation:{includes:a.value,position:a.position},message:a.message}),l.dirty()):"toLowerCase"===a.kind?e.data=e.data.toLowerCase():"toUpperCase"===a.kind?e.data=e.data.toUpperCase():"startsWith"===a.kind?e.data.startsWith(a.value)||(p(u=this._getOrReturnCtx(e,u),{code:i.invalid_string,validation:{startsWith:a.value},message:a.message}),l.dirty()):"endsWith"===a.kind?e.data.endsWith(a.value)||(p(u=this._getOrReturnCtx(e,u),{code:i.invalid_string,validation:{endsWith:a.value},message:a.message}),l.dirty()):"datetime"===a.kind?B(a).test(e.data)||(p(u=this._getOrReturnCtx(e,u),{code:i.invalid_string,validation:"datetime",message:a.message}),l.dirty()):"date"===a.kind?F.test(e.data)||(p(u=this._getOrReturnCtx(e,u),{code:i.invalid_string,validation:"date",message:a.message}),l.dirty()):"time"===a.kind?RegExp(`^${V(a)}$`).test(e.data)||(p(u=this._getOrReturnCtx(e,u),{code:i.invalid_string,validation:"time",message:a.message}),l.dirty()):"duration"===a.kind?I.test(e.data)||(p(u=this._getOrReturnCtx(e,u),{validation:"duration",code:i.invalid_string,message:a.message}),l.dirty()):"ip"===a.kind?(t=e.data,!(("v4"===(r=a.version)||!r)&&C.test(t)||("v6"===r||!r)&&M.test(t))&&1&&(p(u=this._getOrReturnCtx(e,u),{validation:"ip",code:i.invalid_string,message:a.message}),l.dirty())):"jwt"===a.kind?!function(e,t){if(!j.test(e))return!1;try{let[r]=e.split("."),n=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),a=JSON.parse(atob(n));if("object"!=typeof a||null===a||!a.typ||!a.alg||t&&a.alg!==t)return!1;return!0}catch(e){return!1}}(e.data,a.alg)&&(p(u=this._getOrReturnCtx(e,u),{validation:"jwt",code:i.invalid_string,message:a.message}),l.dirty()):"cidr"===a.kind?(s=e.data,!(("v4"===(o=a.version)||!o)&&D.test(s)||("v6"===o||!o)&&U.test(s))&&1&&(p(u=this._getOrReturnCtx(e,u),{validation:"cidr",code:i.invalid_string,message:a.message}),l.dirty())):"base64"===a.kind?$.test(e.data)||(p(u=this._getOrReturnCtx(e,u),{validation:"base64",code:i.invalid_string,message:a.message}),l.dirty()):"base64url"===a.kind?L.test(e.data)||(p(u=this._getOrReturnCtx(e,u),{validation:"base64url",code:i.invalid_string,message:a.message}),l.dirty()):tn.assertNever(a);return{status:l.value,value:e.data}}_regex(e,t,r){return this.refinement(t=>e.test(t),{validation:t,code:i.invalid_string,...ts.errToObj(r)})}_addCheck(e){return new q({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...ts.errToObj(e)})}url(e){return this._addCheck({kind:"url",...ts.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...ts.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...ts.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...ts.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...ts.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...ts.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...ts.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...ts.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...ts.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...ts.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...ts.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...ts.errToObj(e)})}datetime(e){var t,r;return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===(null==e?void 0:e.precision)?null:null==e?void 0:e.precision,offset:null!=(t=null==e?void 0:e.offset)&&t,local:null!=(r=null==e?void 0:e.local)&&r,...ts.errToObj(null==e?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===(null==e?void 0:e.precision)?null:null==e?void 0:e.precision,...ts.errToObj(null==e?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...ts.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...ts.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:null==t?void 0:t.position,...ts.errToObj(null==t?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...ts.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...ts.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...ts.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...ts.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...ts.errToObj(t)})}nonempty(e){return this.min(1,ts.errToObj(e))}trim(){return new q({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new q({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new q({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}q.create=e=>{var t;return new q({checks:[],typeName:tu.ZodString,coerce:null!=(t=null==e?void 0:e.coerce)&&t,...T(e)})};class K extends x{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==a.number){let t=this._getOrReturnCtx(e);return p(t,{code:i.invalid_type,expected:a.number,received:t.parsedType}),h}let r=new f;for(let n of this._def.checks)"int"===n.kind?tn.isInteger(e.data)||(p(t=this._getOrReturnCtx(e,t),{code:i.invalid_type,expected:"integer",received:"float",message:n.message}),r.dirty()):"min"===n.kind?(n.inclusive?e.data<n.value:e.data<=n.value)&&(p(t=this._getOrReturnCtx(e,t),{code:i.too_small,minimum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty()):"max"===n.kind?(n.inclusive?e.data>n.value:e.data>=n.value)&&(p(t=this._getOrReturnCtx(e,t),{code:i.too_big,maximum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty()):"multipleOf"===n.kind?0!==function(e,t){let r=(e.toString().split(".")[1]||"").length,n=(t.toString().split(".")[1]||"").length,a=r>n?r:n;return parseInt(e.toFixed(a).replace(".",""))%parseInt(t.toFixed(a).replace(".",""))/Math.pow(10,a)}(e.data,n.value)&&(p(t=this._getOrReturnCtx(e,t),{code:i.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):"finite"===n.kind?Number.isFinite(e.data)||(p(t=this._getOrReturnCtx(e,t),{code:i.not_finite,message:n.message}),r.dirty()):tn.assertNever(n);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,ts.toString(t))}gt(e,t){return this.setLimit("min",e,!1,ts.toString(t))}lte(e,t){return this.setLimit("max",e,!0,ts.toString(t))}lt(e,t){return this.setLimit("max",e,!1,ts.toString(t))}setLimit(e,t,r,n){return new K({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:ts.toString(n)}]})}_addCheck(e){return new K({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:ts.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:ts.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:ts.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:ts.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:ts.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:ts.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:ts.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:ts.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:ts.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&tn.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let r of this._def.checks)if("finite"===r.kind||"int"===r.kind||"multipleOf"===r.kind)return!0;else"min"===r.kind?(null===t||r.value>t)&&(t=r.value):"max"===r.kind&&(null===e||r.value<e)&&(e=r.value);return Number.isFinite(t)&&Number.isFinite(e)}}K.create=e=>new K({checks:[],typeName:tu.ZodNumber,coerce:(null==e?void 0:e.coerce)||!1,...T(e)});class J extends x{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch(t){return this._getInvalidInput(e)}if(this._getType(e)!==a.bigint)return this._getInvalidInput(e);let r=new f;for(let n of this._def.checks)"min"===n.kind?(n.inclusive?e.data<n.value:e.data<=n.value)&&(p(t=this._getOrReturnCtx(e,t),{code:i.too_small,type:"bigint",minimum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty()):"max"===n.kind?(n.inclusive?e.data>n.value:e.data>=n.value)&&(p(t=this._getOrReturnCtx(e,t),{code:i.too_big,type:"bigint",maximum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty()):"multipleOf"===n.kind?e.data%n.value!==BigInt(0)&&(p(t=this._getOrReturnCtx(e,t),{code:i.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):tn.assertNever(n);return{status:r.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return p(t,{code:i.invalid_type,expected:a.bigint,received:t.parsedType}),h}gte(e,t){return this.setLimit("min",e,!0,ts.toString(t))}gt(e,t){return this.setLimit("min",e,!1,ts.toString(t))}lte(e,t){return this.setLimit("max",e,!0,ts.toString(t))}lt(e,t){return this.setLimit("max",e,!1,ts.toString(t))}setLimit(e,t,r,n){return new J({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:ts.toString(n)}]})}_addCheck(e){return new J({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:ts.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:ts.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:ts.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:ts.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:ts.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}J.create=e=>{var t;return new J({checks:[],typeName:tu.ZodBigInt,coerce:null!=(t=null==e?void 0:e.coerce)&&t,...T(e)})};class G extends x{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==a.boolean){let t=this._getOrReturnCtx(e);return p(t,{code:i.invalid_type,expected:a.boolean,received:t.parsedType}),h}return y(e.data)}}G.create=e=>new G({typeName:tu.ZodBoolean,coerce:(null==e?void 0:e.coerce)||!1,...T(e)});class W extends x{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==a.date){let t=this._getOrReturnCtx(e);return p(t,{code:i.invalid_type,expected:a.date,received:t.parsedType}),h}if(isNaN(e.data.getTime()))return p(this._getOrReturnCtx(e),{code:i.invalid_date}),h;let r=new f;for(let n of this._def.checks)"min"===n.kind?e.data.getTime()<n.value&&(p(t=this._getOrReturnCtx(e,t),{code:i.too_small,message:n.message,inclusive:!0,exact:!1,minimum:n.value,type:"date"}),r.dirty()):"max"===n.kind?e.data.getTime()>n.value&&(p(t=this._getOrReturnCtx(e,t),{code:i.too_big,message:n.message,inclusive:!0,exact:!1,maximum:n.value,type:"date"}),r.dirty()):tn.assertNever(n);return{status:r.value,value:new Date(e.data.getTime())}}_addCheck(e){return new W({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:ts.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:ts.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}W.create=e=>new W({checks:[],coerce:(null==e?void 0:e.coerce)||!1,typeName:tu.ZodDate,...T(e)});class Y extends x{_parse(e){if(this._getType(e)!==a.symbol){let t=this._getOrReturnCtx(e);return p(t,{code:i.invalid_type,expected:a.symbol,received:t.parsedType}),h}return y(e.data)}}Y.create=e=>new Y({typeName:tu.ZodSymbol,...T(e)});class Q extends x{_parse(e){if(this._getType(e)!==a.undefined){let t=this._getOrReturnCtx(e);return p(t,{code:i.invalid_type,expected:a.undefined,received:t.parsedType}),h}return y(e.data)}}Q.create=e=>new Q({typeName:tu.ZodUndefined,...T(e)});class H extends x{_parse(e){if(this._getType(e)!==a.null){let t=this._getOrReturnCtx(e);return p(t,{code:i.invalid_type,expected:a.null,received:t.parsedType}),h}return y(e.data)}}H.create=e=>new H({typeName:tu.ZodNull,...T(e)});class X extends x{constructor(){super(...arguments),this._any=!0}_parse(e){return y(e.data)}}X.create=e=>new X({typeName:tu.ZodAny,...T(e)});class ee extends x{constructor(){super(...arguments),this._unknown=!0}_parse(e){return y(e.data)}}ee.create=e=>new ee({typeName:tu.ZodUnknown,...T(e)});class et extends x{_parse(e){let t=this._getOrReturnCtx(e);return p(t,{code:i.invalid_type,expected:a.never,received:t.parsedType}),h}}et.create=e=>new et({typeName:tu.ZodNever,...T(e)});class er extends x{_parse(e){if(this._getType(e)!==a.undefined){let t=this._getOrReturnCtx(e);return p(t,{code:i.invalid_type,expected:a.void,received:t.parsedType}),h}return y(e.data)}}er.create=e=>new er({typeName:tu.ZodVoid,...T(e)});class en extends x{_parse(e){let{ctx:t,status:r}=this._processInputParams(e),n=this._def;if(t.parsedType!==a.array)return p(t,{code:i.invalid_type,expected:a.array,received:t.parsedType}),h;if(null!==n.exactLength){let e=t.data.length>n.exactLength.value,a=t.data.length<n.exactLength.value;(e||a)&&(p(t,{code:e?i.too_big:i.too_small,minimum:a?n.exactLength.value:void 0,maximum:e?n.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:n.exactLength.message}),r.dirty())}if(null!==n.minLength&&t.data.length<n.minLength.value&&(p(t,{code:i.too_small,minimum:n.minLength.value,type:"array",inclusive:!0,exact:!1,message:n.minLength.message}),r.dirty()),null!==n.maxLength&&t.data.length>n.maxLength.value&&(p(t,{code:i.too_big,maximum:n.maxLength.value,type:"array",inclusive:!0,exact:!1,message:n.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map((e,r)=>n.type._parseAsync(new k(t,e,t.path,r)))).then(e=>f.mergeArray(r,e));let s=[...t.data].map((e,r)=>n.type._parseSync(new k(t,e,t.path,r)));return f.mergeArray(r,s)}get element(){return this._def.type}min(e,t){return new en({...this._def,minLength:{value:e,message:ts.toString(t)}})}max(e,t){return new en({...this._def,maxLength:{value:e,message:ts.toString(t)}})}length(e,t){return new en({...this._def,exactLength:{value:e,message:ts.toString(t)}})}nonempty(e){return this.min(1,e)}}en.create=(e,t)=>new en({type:e,minLength:null,maxLength:null,exactLength:null,typeName:tu.ZodArray,...T(t)});class ea extends x{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=tn.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==a.object){let t=this._getOrReturnCtx(e);return p(t,{code:i.invalid_type,expected:a.object,received:t.parsedType}),h}let{status:t,ctx:r}=this._processInputParams(e),{shape:n,keys:s}=this._getCached(),o=[];if(!(this._def.catchall instanceof et&&"strip"===this._def.unknownKeys))for(let e in r.data)s.includes(e)||o.push(e);let u=[];for(let e of s){let t=n[e],a=r.data[e];u.push({key:{status:"valid",value:e},value:t._parse(new k(r,a,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof et){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of o)u.push({key:{status:"valid",value:e},value:{status:"valid",value:r.data[e]}});else if("strict"===e)o.length>0&&(p(r,{code:i.unrecognized_keys,keys:o}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of o){let n=r.data[t];u.push({key:{status:"valid",value:t},value:e._parse(new k(r,n,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of u){let r=await t.key,n=await t.value;e.push({key:r,value:n,alwaysSet:t.alwaysSet})}return e}).then(e=>f.mergeObjectSync(t,e)):f.mergeObjectSync(t,u)}get shape(){return this._def.shape()}strict(e){return ts.errToObj,new ea({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,r)=>{var n,a,s,i;let o=null!=(s=null==(a=(n=this._def).errorMap)?void 0:a.call(n,t,r).message)?s:r.defaultError;return"unrecognized_keys"===t.code?{message:null!=(i=ts.errToObj(e).message)?i:o}:{message:o}}}:{}})}strip(){return new ea({...this._def,unknownKeys:"strip"})}passthrough(){return new ea({...this._def,unknownKeys:"passthrough"})}extend(e){return new ea({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new ea({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:tu.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new ea({...this._def,catchall:e})}pick(e){let t={};return tn.objectKeys(e).forEach(r=>{e[r]&&this.shape[r]&&(t[r]=this.shape[r])}),new ea({...this._def,shape:()=>t})}omit(e){let t={};return tn.objectKeys(this.shape).forEach(r=>{e[r]||(t[r]=this.shape[r])}),new ea({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof ea){let r={};for(let n in t.shape){let a=t.shape[n];r[n]=ew.create(e(a))}return new ea({...t._def,shape:()=>r})}if(t instanceof en)return new en({...t._def,type:e(t.element)});if(t instanceof ew)return ew.create(e(t.unwrap()));if(t instanceof eE)return eE.create(e(t.unwrap()));if(t instanceof el)return el.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};return tn.objectKeys(this.shape).forEach(r=>{let n=this.shape[r];e&&!e[r]?t[r]=n:t[r]=n.optional()}),new ea({...this._def,shape:()=>t})}required(e){let t={};return tn.objectKeys(this.shape).forEach(r=>{if(e&&!e[r])t[r]=this.shape[r];else{let e=this.shape[r];for(;e instanceof ew;)e=e._def.innerType;t[r]=e}}),new ea({...this._def,shape:()=>t})}keyof(){return ey(tn.objectKeys(this.shape))}}ea.create=(e,t)=>new ea({shape:()=>e,unknownKeys:"strip",catchall:et.create(),typeName:tu.ZodObject,...T(t)}),ea.strictCreate=(e,t)=>new ea({shape:()=>e,unknownKeys:"strict",catchall:et.create(),typeName:tu.ZodObject,...T(t)}),ea.lazycreate=(e,t)=>new ea({shape:e,unknownKeys:"strip",catchall:et.create(),typeName:tu.ZodObject,...T(t)});class es extends x{_parse(e){let{ctx:t}=this._processInputParams(e),r=this._def.options;if(t.common.async)return Promise.all(r.map(async e=>{let r={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:r}),ctx:r}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;let r=e.map(e=>new o(e.ctx.common.issues));return p(t,{code:i.invalid_union,unionErrors:r}),h});{let e,n=[];for(let a of r){let r={...t,common:{...t.common,issues:[]},parent:null},s=a._parseSync({data:t.data,path:t.path,parent:r});if("valid"===s.status)return s;"dirty"!==s.status||e||(e={result:s,ctx:r}),r.common.issues.length&&n.push(r.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let a=n.map(e=>new o(e));return p(t,{code:i.invalid_union,unionErrors:a}),h}}get options(){return this._def.options}}es.create=(e,t)=>new es({options:e,typeName:tu.ZodUnion,...T(t)});let ei=e=>{if(e instanceof eh)return ei(e.schema);if(e instanceof eb)return ei(e.innerType());if(e instanceof em)return[e.value];if(e instanceof ev)return e.options;if(e instanceof eg)return tn.objectValues(e.enum);else if(e instanceof ek)return ei(e._def.innerType);else if(e instanceof Q)return[void 0];else if(e instanceof H)return[null];else if(e instanceof ew)return[void 0,...ei(e.unwrap())];else if(e instanceof eE)return[null,...ei(e.unwrap())];else if(e instanceof eS)return ei(e.unwrap());else if(e instanceof eR)return ei(e.unwrap());else if(e instanceof eO)return ei(e._def.innerType);else return[]};class eo extends x{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==a.object)return p(t,{code:i.invalid_type,expected:a.object,received:t.parsedType}),h;let r=this.discriminator,n=t.data[r],s=this.optionsMap.get(n);return s?t.common.async?s._parseAsync({data:t.data,path:t.path,parent:t}):s._parseSync({data:t.data,path:t.path,parent:t}):(p(t,{code:i.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),h)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,r){let n=new Map;for(let r of t){let t=ei(r.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let a of t){if(n.has(a))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(a)}`);n.set(a,r)}}return new eo({typeName:tu.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:n,...T(r)})}}class eu extends x{_parse(e){let{status:t,ctx:r}=this._processInputParams(e),n=(e,n)=>{if(v(e)||v(n))return h;let o=function e(t,r){let n=s(t),i=s(r);if(t===r)return{valid:!0,data:t};if(n===a.object&&i===a.object){let n=tn.objectKeys(r),a=tn.objectKeys(t).filter(e=>-1!==n.indexOf(e)),s={...t,...r};for(let n of a){let a=e(t[n],r[n]);if(!a.valid)return{valid:!1};s[n]=a.data}return{valid:!0,data:s}}if(n===a.array&&i===a.array){if(t.length!==r.length)return{valid:!1};let n=[];for(let a=0;a<t.length;a++){let s=e(t[a],r[a]);if(!s.valid)return{valid:!1};n.push(s.data)}return{valid:!0,data:n}}if(n===a.date&&i===a.date&&+t==+r)return{valid:!0,data:t};return{valid:!1}}(e.value,n.value);return o.valid?((g(e)||g(n))&&t.dirty(),{status:t.value,value:o.data}):(p(r,{code:i.invalid_intersection_types}),h)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([e,t])=>n(e,t)):n(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}eu.create=(e,t,r)=>new eu({left:e,right:t,typeName:tu.ZodIntersection,...T(r)});class el extends x{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==a.array)return p(r,{code:i.invalid_type,expected:a.array,received:r.parsedType}),h;if(r.data.length<this._def.items.length)return p(r,{code:i.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),h;!this._def.rest&&r.data.length>this._def.items.length&&(p(r,{code:i.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let n=[...r.data].map((e,t)=>{let n=this._def.items[t]||this._def.rest;return n?n._parse(new k(r,e,r.path,t)):null}).filter(e=>!!e);return r.common.async?Promise.all(n).then(e=>f.mergeArray(t,e)):f.mergeArray(t,n)}get items(){return this._def.items}rest(e){return new el({...this._def,rest:e})}}el.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new el({items:e,typeName:tu.ZodTuple,rest:null,...T(t)})};class ed extends x{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==a.object)return p(r,{code:i.invalid_type,expected:a.object,received:r.parsedType}),h;let n=[],s=this._def.keyType,o=this._def.valueType;for(let e in r.data)n.push({key:s._parse(new k(r,e,r.path,e)),value:o._parse(new k(r,r.data[e],r.path,e)),alwaysSet:e in r.data});return r.common.async?f.mergeObjectAsync(t,n):f.mergeObjectSync(t,n)}get element(){return this._def.valueType}static create(e,t,r){return new ed(t instanceof x?{keyType:e,valueType:t,typeName:tu.ZodRecord,...T(r)}:{keyType:q.create(),valueType:e,typeName:tu.ZodRecord,...T(t)})}}class ec extends x{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==a.map)return p(r,{code:i.invalid_type,expected:a.map,received:r.parsedType}),h;let n=this._def.keyType,s=this._def.valueType,o=[...r.data.entries()].map(([e,t],a)=>({key:n._parse(new k(r,e,r.path,[a,"key"])),value:s._parse(new k(r,t,r.path,[a,"value"]))}));if(r.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let r of o){let n=await r.key,a=await r.value;if("aborted"===n.status||"aborted"===a.status)return h;("dirty"===n.status||"dirty"===a.status)&&t.dirty(),e.set(n.value,a.value)}return{status:t.value,value:e}})}{let e=new Map;for(let r of o){let n=r.key,a=r.value;if("aborted"===n.status||"aborted"===a.status)return h;("dirty"===n.status||"dirty"===a.status)&&t.dirty(),e.set(n.value,a.value)}return{status:t.value,value:e}}}}ec.create=(e,t,r)=>new ec({valueType:t,keyType:e,typeName:tu.ZodMap,...T(r)});class ep extends x{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==a.set)return p(r,{code:i.invalid_type,expected:a.set,received:r.parsedType}),h;let n=this._def;null!==n.minSize&&r.data.size<n.minSize.value&&(p(r,{code:i.too_small,minimum:n.minSize.value,type:"set",inclusive:!0,exact:!1,message:n.minSize.message}),t.dirty()),null!==n.maxSize&&r.data.size>n.maxSize.value&&(p(r,{code:i.too_big,maximum:n.maxSize.value,type:"set",inclusive:!0,exact:!1,message:n.maxSize.message}),t.dirty());let s=this._def.valueType;function o(e){let r=new Set;for(let n of e){if("aborted"===n.status)return h;"dirty"===n.status&&t.dirty(),r.add(n.value)}return{status:t.value,value:r}}let u=[...r.data.values()].map((e,t)=>s._parse(new k(r,e,r.path,t)));return r.common.async?Promise.all(u).then(e=>o(e)):o(u)}min(e,t){return new ep({...this._def,minSize:{value:e,message:ts.toString(t)}})}max(e,t){return new ep({...this._def,maxSize:{value:e,message:ts.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}ep.create=(e,t)=>new ep({valueType:e,minSize:null,maxSize:null,typeName:tu.ZodSet,...T(t)});class ef extends x{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==a.function)return p(t,{code:i.invalid_type,expected:a.function,received:t.parsedType}),h;function r(e,r){return c({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,d(),u].filter(e=>!!e),issueData:{code:i.invalid_arguments,argumentsError:r}})}function n(e,r){return c({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,d(),u].filter(e=>!!e),issueData:{code:i.invalid_return_type,returnTypeError:r}})}let s={errorMap:t.common.contextualErrorMap},l=t.data;if(this._def.returns instanceof e_){let e=this;return y(async function(...t){let a=new o([]),i=await e._def.args.parseAsync(t,s).catch(e=>{throw a.addIssue(r(t,e)),a}),u=await Reflect.apply(l,this,i);return await e._def.returns._def.type.parseAsync(u,s).catch(e=>{throw a.addIssue(n(u,e)),a})})}{let e=this;return y(function(...t){let a=e._def.args.safeParse(t,s);if(!a.success)throw new o([r(t,a.error)]);let i=Reflect.apply(l,this,a.data),u=e._def.returns.safeParse(i,s);if(!u.success)throw new o([n(i,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new ef({...this._def,args:el.create(e).rest(ee.create())})}returns(e){return new ef({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,r){return new ef({args:e||el.create([]).rest(ee.create()),returns:t||ee.create(),typeName:tu.ZodFunction,...T(r)})}}class eh extends x{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}eh.create=(e,t)=>new eh({getter:e,typeName:tu.ZodLazy,...T(t)});class em extends x{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return p(t,{received:t.data,code:i.invalid_literal,expected:this._def.value}),h}return{status:"valid",value:e.data}}get value(){return this._def.value}}function ey(e,t){return new ev({values:e,typeName:tu.ZodEnum,...T(t)})}em.create=(e,t)=>new em({value:e,typeName:tu.ZodLiteral,...T(t)});class ev extends x{constructor(){super(...arguments),ti.set(this,void 0)}_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),r=this._def.values;return p(t,{expected:tn.joinValues(r),received:t.parsedType,code:i.invalid_type}),h}if(w(this,ti,"f")||E(this,ti,new Set(this._def.values),"f"),!w(this,ti,"f").has(e.data)){let t=this._getOrReturnCtx(e),r=this._def.values;return p(t,{received:t.data,code:i.invalid_enum_value,options:r}),h}return y(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return ev.create(e,{...this._def,...t})}exclude(e,t=this._def){return ev.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}ti=new WeakMap,ev.create=ey;class eg extends x{constructor(){super(...arguments),to.set(this,void 0)}_parse(e){let t=tn.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==a.string&&r.parsedType!==a.number){let e=tn.objectValues(t);return p(r,{expected:tn.joinValues(e),received:r.parsedType,code:i.invalid_type}),h}if(w(this,to,"f")||E(this,to,new Set(tn.getValidEnumValues(this._def.values)),"f"),!w(this,to,"f").has(e.data)){let e=tn.objectValues(t);return p(r,{received:r.data,code:i.invalid_enum_value,options:e}),h}return y(e.data)}get enum(){return this._def.values}}to=new WeakMap,eg.create=(e,t)=>new eg({values:e,typeName:tu.ZodNativeEnum,...T(t)});class e_ extends x{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==a.promise&&!1===t.common.async?(p(t,{code:i.invalid_type,expected:a.promise,received:t.parsedType}),h):y((t.parsedType===a.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}e_.create=(e,t)=>new e_({type:e,typeName:tu.ZodPromise,...T(t)});class eb extends x{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===tu.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:r}=this._processInputParams(e),n=this._def.effect||null,a={addIssue:e=>{p(r,e),e.fatal?t.abort():t.dirty()},get path(){return r.path}};if(a.addIssue=a.addIssue.bind(a),"preprocess"===n.type){let e=n.transform(r.data,a);if(r.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return h;let n=await this._def.schema._parseAsync({data:e,path:r.path,parent:r});return"aborted"===n.status?h:"dirty"===n.status||"dirty"===t.value?m(n.value):n});{if("aborted"===t.value)return h;let n=this._def.schema._parseSync({data:e,path:r.path,parent:r});return"aborted"===n.status?h:"dirty"===n.status||"dirty"===t.value?m(n.value):n}}if("refinement"===n.type){let e=e=>{let t=n.refinement(e,a);if(r.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(r=>"aborted"===r.status?h:("dirty"===r.status&&t.dirty(),e(r.value).then(()=>({status:t.value,value:r.value}))));{let n=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===n.status?h:("dirty"===n.status&&t.dirty(),e(n.value),{status:t.value,value:n.value})}}if("transform"===n.type)if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(e=>_(e)?Promise.resolve(n.transform(e.value,a)).then(e=>({status:t.value,value:e})):e);else{let e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!_(e))return e;let s=n.transform(e.value,a);if(s instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:s}}tn.assertNever(n)}}eb.create=(e,t,r)=>new eb({schema:e,typeName:tu.ZodEffects,effect:t,...T(r)}),eb.createWithPreprocess=(e,t,r)=>new eb({schema:t,effect:{type:"preprocess",transform:e},typeName:tu.ZodEffects,...T(r)});class ew extends x{_parse(e){return this._getType(e)===a.undefined?y(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ew.create=(e,t)=>new ew({innerType:e,typeName:tu.ZodOptional,...T(t)});class eE extends x{_parse(e){return this._getType(e)===a.null?y(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eE.create=(e,t)=>new eE({innerType:e,typeName:tu.ZodNullable,...T(t)});class ek extends x{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return t.parsedType===a.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}ek.create=(e,t)=>new ek({innerType:e,typeName:tu.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...T(t)});class eO extends x{_parse(e){let{ctx:t}=this._processInputParams(e),r={...t,common:{...t.common,issues:[]}},n=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return b(n)?n.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new o(r.common.issues)},input:r.data})})):{status:"valid",value:"valid"===n.status?n.value:this._def.catchValue({get error(){return new o(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}eO.create=(e,t)=>new eO({innerType:e,typeName:tu.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...T(t)});class eT extends x{_parse(e){if(this._getType(e)!==a.nan){let t=this._getOrReturnCtx(e);return p(t,{code:i.invalid_type,expected:a.nan,received:t.parsedType}),h}return{status:"valid",value:e.data}}}eT.create=e=>new eT({typeName:tu.ZodNaN,...T(e)});let ex=Symbol("zod_brand");class eS extends x{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}class eA extends x{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?h:"dirty"===e.status?(t.dirty(),m(e.value)):this._def.out._parseAsync({data:e.value,path:r.path,parent:r})})();{let e=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?h:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:r.path,parent:r})}}static create(e,t){return new eA({in:e,out:t,typeName:tu.ZodPipeline})}}class eR extends x{_parse(e){let t=this._def.innerType._parse(e),r=e=>(_(e)&&(e.value=Object.freeze(e.value)),e);return b(t)?t.then(e=>r(e)):r(t)}unwrap(){return this._def.innerType}}function eP(e,t){let r="function"==typeof e?e(t):"string"==typeof e?{message:e}:e;return"string"==typeof r?{message:r}:r}function eN(e,t={},r){return e?X.create().superRefine((n,a)=>{var s,i;let o=e(n);if(o instanceof Promise)return o.then(e=>{var s,i;if(!e){let e=eP(t,n),o=null==(i=null!=(s=e.fatal)?s:r)||i;a.addIssue({code:"custom",...e,fatal:o})}});if(!o){let e=eP(t,n),o=null==(i=null!=(s=e.fatal)?s:r)||i;a.addIssue({code:"custom",...e,fatal:o})}}):X.create()}eR.create=(e,t)=>new eR({innerType:e,typeName:tu.ZodReadonly,...T(t)});let ej={object:ea.lazycreate};!function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(tu||(tu={}));let eI=q.create,eZ=K.create,eC=eT.create,eD=J.create,eM=G.create,eU=W.create,e$=Y.create,eL=Q.create,ez=H.create,eF=X.create,eV=ee.create,eB=et.create,eq=er.create,eK=en.create,eJ=ea.create,eG=ea.strictCreate,eW=es.create,eY=eo.create,eQ=eu.create,eH=el.create,eX=ed.create,e0=ec.create,e1=ep.create,e9=ef.create,e4=eh.create,e2=em.create,e3=ev.create,e5=eg.create,e6=e_.create,e8=eb.create,e7=ew.create,te=eE.create,tt=eb.createWithPreprocess,tr=eA.create;var tn,ta,ts,ti,to,tu,tl=Object.freeze({__proto__:null,defaultErrorMap:u,setErrorMap:function(e){l=e},getErrorMap:d,makeIssue:c,EMPTY_PATH:[],addIssueToContext:p,ParseStatus:f,INVALID:h,DIRTY:m,OK:y,isAborted:v,isDirty:g,isValid:_,isAsync:b,get util(){return tn},get objectUtil(){return ta},ZodParsedType:a,getParsedType:s,ZodType:x,datetimeRegex:B,ZodString:q,ZodNumber:K,ZodBigInt:J,ZodBoolean:G,ZodDate:W,ZodSymbol:Y,ZodUndefined:Q,ZodNull:H,ZodAny:X,ZodUnknown:ee,ZodNever:et,ZodVoid:er,ZodArray:en,ZodObject:ea,ZodUnion:es,ZodDiscriminatedUnion:eo,ZodIntersection:eu,ZodTuple:el,ZodRecord:ed,ZodMap:ec,ZodSet:ep,ZodFunction:ef,ZodLazy:eh,ZodLiteral:em,ZodEnum:ev,ZodNativeEnum:eg,ZodPromise:e_,ZodEffects:eb,ZodTransformer:eb,ZodOptional:ew,ZodNullable:eE,ZodDefault:ek,ZodCatch:eO,ZodNaN:eT,BRAND:ex,ZodBranded:eS,ZodPipeline:eA,ZodReadonly:eR,custom:eN,Schema:x,ZodSchema:x,late:ej,get ZodFirstPartyTypeKind(){return tu},coerce:{string:e=>q.create({...e,coerce:!0}),number:e=>K.create({...e,coerce:!0}),boolean:e=>G.create({...e,coerce:!0}),bigint:e=>J.create({...e,coerce:!0}),date:e=>W.create({...e,coerce:!0})},any:eF,array:eK,bigint:eD,boolean:eM,date:eU,discriminatedUnion:eY,effect:e8,enum:e3,function:e9,instanceof:(e,t={message:`Input not instance of ${e.name}`})=>eN(t=>t instanceof e,t),intersection:eQ,lazy:e4,literal:e2,map:e0,nan:eC,nativeEnum:e5,never:eB,null:ez,nullable:te,number:eZ,object:eJ,oboolean:()=>eM().optional(),onumber:()=>eZ().optional(),optional:e7,ostring:()=>eI().optional(),pipeline:tr,preprocess:tt,promise:e6,record:eX,set:e1,strictObject:eG,string:eI,symbol:e$,transformer:e8,tuple:eH,undefined:eL,union:eW,unknown:eV,void:eq,NEVER:h,ZodIssueCode:i,quotelessJson:e=>JSON.stringify(e,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:o})},72736:(e,t,r)=>{r.d(t,{I:()=>u});var n=r(6894),a=r(59376),s=r(62505),i=r(13495);function o(e,t){let{middlewares:r=[],inputs:n,meta:a,...s}=t;return u({...(0,i.uf)(e,s),inputs:[...e.inputs,...n??[]],middlewares:[...e.middlewares,...r],meta:e.meta&&a?{...e.meta,...a}:a??e.meta})}function u(e={}){let t={procedure:!0,inputs:[],middlewares:[],...e};return{_def:t,input(e){let r=(0,s.i)(e);return o(t,{inputs:[e],middlewares:[(0,a.lL)(r)]})},output(e){let r=(0,s.i)(e);return o(t,{output:e,middlewares:[(0,a.yU)(r)]})},meta:e=>o(t,{meta:e}),use:e=>o(t,{middlewares:"_middlewares"in e?e._middlewares:[e]}),unstable_concat:e=>o(t,e._def),concat:e=>o(t,e._def),query:e=>l({...t,type:"query"},e),mutation:e=>l({...t,type:"mutation"},e),subscription:e=>l({...t,type:"subscription"},e),experimental_caller:e=>o(t,{caller:e})}}function l(e,t){let r=o(e,{resolver:t,middlewares:[async function(e){let r=await t(e);return{marker:a.l$,ok:!0,data:r,ctx:e.ctx}}]}),s={...r._def,type:e.type,experimental_caller:!!r._def.caller,meta:r._def.meta,$types:null},i=function(e){async function t(t){if(!t||!("getRawInput"in t))throw Error(d);let r=await c(0,e,t);if(!r)throw new n.gt({code:"INTERNAL_SERVER_ERROR",message:"No result from middlewares - did you forget to `return next()`?"});if(!r.ok)throw r.error;return r.data}return t._def=e,t.procedure=!0,t}(r._def),u=r._def.caller;if(!u)return i;let l=async(...e)=>await u({args:e,invoke:i,_def:s});return l._def=s,l}let d=`
This is a client-only function.
If you want to call this function on the server, see https://trpc.io/docs/v11/server/server-side-calls
`.trim();async function c(e,t,r){try{let n=t.middlewares[e];return await n({...r,meta:t.meta,input:r.input,next:n=>c(e+1,t,{...r,ctx:n?.ctx?{...r.ctx,...n.ctx}:r.ctx,input:n&&"input"in n?n.input:r.input,getRawInput:n?.getRawInput??r.getRawInput})})}catch(e){return{ok:!1,error:(0,n.qO)(e),marker:a.l$}}}},74157:(e,t,r)=>{r.d(t,{E:()=>n});let n=({shape:e})=>e},74329:(e,t,r)=>{r.d(t,{d:()=>n});function n(e){let t=e[Symbol.asyncIterator]();return new ReadableStream({async cancel(){await t.return?.()},async pull(e){let r=await t.next();if(r.done)return void e.close();e.enqueue(r.value)}})}},84065:(e,t,r)=>{r.d(t,{J:()=>u,r:()=>l});var n=r(30785),a=r(28681),s=r(7128);function i(e,t,r){if(null!=t){var n,a;if("object"!=typeof t&&"function"!=typeof t)throw TypeError("Object expected.");if(r){if(!Symbol.asyncDispose)throw TypeError("Symbol.asyncDispose is not defined.");n=t[Symbol.asyncDispose]}if(void 0===n){if(!Symbol.dispose)throw TypeError("Symbol.dispose is not defined.");n=t[Symbol.dispose],r&&(a=n)}if("function"!=typeof n)throw TypeError("Object not disposable.");a&&(n=function(){try{a.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:t,dispose:n,async:r})}else r&&e.stack.push({async:!0});return t}function o(e){var t="function"==typeof SuppressedError?SuppressedError:function(e,t,r){var n=Error(r);return n.name="SuppressedError",n.error=e,n.suppressed=t,n};return(o=function(e){function r(r){e.error=e.hasError?new t(r,e.error,"An error was suppressed during disposal."):r,e.hasError=!0}var n,a=0;return function t(){for(;n=e.stack.pop();)try{if(!n.async&&1===a)return a=0,e.stack.push(n),Promise.resolve().then(t);if(n.dispose){var s=n.dispose.call(n.value);if(n.async)return a|=2,Promise.resolve(s).then(t,function(e){return r(e),t()})}else a|=1}catch(e){r(e)}if(1===a)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()})(e)}let u=Symbol("ping");async function*l(e,t){let r={stack:[],error:void 0,hasError:!1};try{let l,d=i(r,(0,a.X4)(e),!0),c=d.next();for(;;){let e={stack:[],error:void 0,hasError:!1};try{let r=i(e,(0,s._)(t),!1);if((l=await n.V.race([c,r.start()]))===s.s){yield u;continue}if(l.done)return l.value;c=d.next(),yield l.value,l=null}catch(t){e.error=t,e.hasError=!0}finally{o(e)}}}catch(e){r.error=e,r.hasError=!0}finally{let e=o(r);e&&await e}}},90043:(e,t,r)=>{r.d(t,{Iw:()=>p,OA:()=>f,OX:()=>c,ri:()=>h});var n=r(69648),a=r(74157),s=r(6894),i=r(46049),o=r(13495);let u=Symbol("lazy"),l={_ctx:null,_errorShape:null,_meta:null,queries:{},mutations:{},subscriptions:{},errorFormatter:a.E,transformer:i.bJ},d=["then","call","apply"];function c(e){return function(t){let r=new Set(Object.keys(t).filter(e=>d.includes(e)));if(r.size>0)throw Error("Reserved words used in `router({})` call: "+Array.from(r).join(", "));let n=(0,o.QQ)({}),a=(0,o.QQ)({}),s=function e(t,r=[]){let s=(0,o.QQ)({});for(let[l,d]of Object.entries(t??{})){var i;if("function"==typeof d&&u in d){a[[...r,l].join(".")]=function t(r){return{ref:r.ref,load:function(e){let t=Symbol(),r=t;return()=>(r===t&&(r=e()),r)}(async()=>{let n=await r.ref(),s=[...r.path,r.key],i=s.join(".");for(let[o,u]of(r.aggregate[r.key]=e(n._def.record,s),delete a[i],Object.entries(n._def.lazy)))a[[...s,o].join(".")]=t({ref:u.ref,path:s,key:o,aggregate:r.aggregate[r.key]})})}}({path:r,ref:d,key:l,aggregate:s});continue}if(i=d,(0,o.Gv)(i)&&(0,o.Gv)(i._def)&&"router"in i._def){s[l]=e(d._def.record,[...r,l]);continue}if(!function(e){return"function"==typeof e}(d)){s[l]=e(d,[...r,l]);continue}let t=[...r,l].join(".");if(n[t])throw Error(`Duplicate key: ${t}`);n[t]=d,s[l]=d}return s}(t),i={_config:e,router:!0,procedures:n,lazy:a,...l,record:s};return{...s,_def:i,createCaller:f()({_def:i})}}}async function p(e,t){let{_def:r}=e,n=r.procedures[t];for(;!n;){let e=Object.keys(r.lazy).find(e=>t.startsWith(e));if(!e)return null;let a=r.lazy[e];await a.load(),n=r.procedures[t]}return n}function f(){return function(e){let{_def:t}=e;return function(r,a){return(0,n.v)(async({path:n,args:i})=>{let u,l=n.join(".");if(1===n.length&&"_def"===n[0])return t;let d=await p(e,l);try{if(!d)throw new s.gt({code:"NOT_FOUND",message:`No procedure found on path "${n}"`});return u=(0,o.Tn)(r)?await Promise.resolve(r()):r,await d({path:l,getRawInput:async()=>i[0],ctx:u,type:d._def.type,signal:a?.signal})}catch(e){throw a?.onError?.({ctx:u,error:(0,s.qO)(e),input:i[0],path:l,type:d?._def.type??"unknown"}),e}})}}}function h(...e){let t=(0,o.uf)({},...e.map(e=>e._def.record));return c({errorFormatter:e.reduce((e,t)=>{if(t._def._config.errorFormatter&&t._def._config.errorFormatter!==a.E){if(e!==a.E&&e!==t._def._config.errorFormatter)throw Error("You seem to have several error formatters");return t._def._config.errorFormatter}return e},a.E),transformer:e.reduce((e,t)=>{if(t._def._config.transformer&&t._def._config.transformer!==i.bJ){if(e!==i.bJ&&e!==t._def._config.transformer)throw Error("You seem to have several transformers");return t._def._config.transformer}return e},i.bJ),isDev:e.every(e=>e._def._config.isDev),allowOutsideOfServer:e.every(e=>e._def._config.allowOutsideOfServer),isServer:e.every(e=>e._def._config.isServer),$types:e[0]?._def._config.$types})(t)}}};