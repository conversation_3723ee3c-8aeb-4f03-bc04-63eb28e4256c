(()=>{var e={};e.id=492,e.ids=[492],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35692:()=>{},41011:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>l});var n=t(65239),s=t(48088),o=t(88170),i=t.n(o),a=t(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],p={require:t,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},47702:(e,r,t)=>{Promise.resolve().then(t.bind(t,89544))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77093:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},79755:(e,r,t)=>{"use strict";t.d(r,{TRPCReactProvider:()=>v,F:()=>h});var n=t(60687),s=t(8693),o=t(68357),i=t(63839),a=t(43210),d=t(558),l=t(62087),c=t(72083);let p=()=>new l.E({defaultOptions:{queries:{staleTime:3e4},dehydrate:{serializeData:d.Ay.serialize,shouldDehydrateQuery:e=>(0,c.XS)(e)||"pending"===e.state.status},hydrate:{deserializeData:d.Ay.deserialize}}}),u=()=>p(),h=(0,i.pY)();function v(e){let r=u(),[t]=(0,a.useState)(()=>h.createClient({links:[(0,o.$H)({enabled:e=>"down"===e.direction&&e.result instanceof Error}),(0,o.N9)({transformer:d.Ay,url:(process.env.VERCEL_URL?`https://${process.env.VERCEL_URL}`:`http://localhost:${process.env.PORT??3e3}`)+"/api/trpc",headers:()=>{let e=new Headers;return e.set("x-trpc-source","nextjs-react"),e}})]}));return(0,n.jsx)(s.QueryClientProvider,{client:r,children:(0,n.jsx)(h.Provider,{client:t,queryClient:r,children:e.children})})}},89544:(e,r,t)=>{"use strict";t.d(r,{TRPCReactProvider:()=>s});var n=t(12907);(0,n.registerClientReference)(function(){throw Error("Attempted to call api() from the server but api is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\trpc\\react.tsx","api");let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call TRPCReactProvider() from the server but TRPCReactProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\trpc\\react.tsx","TRPCReactProvider")},90245:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d,metadata:()=>a});var n=t(37413);t(35692);var s=t(67799),o=t.n(s),i=t(89544);let a={title:"Create T3 App",description:"Generated by create-t3-app",icons:[{rel:"icon",url:"/favicon.ico"}]};function d({children:e}){return(0,n.jsx)("html",{lang:"en",className:`${o().variable}`,children:(0,n.jsx)("body",{children:(0,n.jsx)(i.TRPCReactProvider,{children:e})})})}},94558:(e,r,t)=>{Promise.resolve().then(t.bind(t,79755))}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[719,338],()=>t(41011));module.exports=n})();