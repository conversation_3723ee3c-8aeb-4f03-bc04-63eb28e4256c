import Link from "next/link";
import { MapsScraperForm } from "~/app/_components/maps-scraper/MapsScraperForm";
import { HydrateClient } from "~/trpc/server";

export const metadata = {
  title: "Google Maps Scraper - Scraper King",
  description: "Scrape business data from Google Maps",
};

export default function MapsScraperPage() {
  return (
    <HydrateClient>
      <main className="min-h-screen bg-gradient-to-b from-[#2e026d] to-[#15162c] text-white">
        <div className="container mx-auto px-4 py-8">
          <header className="mb-8">
            <div className="flex items-center justify-between">
              <h1 className="text-4xl font-extrabold tracking-tight sm:text-5xl">
                Google Maps <span className="text-[hsl(280,100%,70%)]">Scraper</span>
              </h1>
              <Link
                href="/"
                className="rounded-full bg-white/10 px-4 py-2 font-medium transition hover:bg-white/20"
              >
                ← Back to Home
              </Link>
            </div>
            <p className="mt-2 text-lg text-white/70">
              Scrape business data from Google Maps based on location and category
            </p>
          </header>
          
          <div className="rounded-xl bg-white/10 p-6 shadow-lg">
            <MapsScraperForm />
          </div>
        </div>
      </main>
    </HydrateClient>
  );
}
