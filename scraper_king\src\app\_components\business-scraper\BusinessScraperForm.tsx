"use client";

import { useState, useEffect } from "react";
import { ResultsTable, type ScrapedResult } from "./ResultsTable";
import { LoadingIndicator } from "./LoadingIndicator";
import { scrapeBusiness, checkApiHealth } from "~/api/flask-api";
import { CsvUploader } from "./CsvUploader";
import "./dropdown-styles.css";

// Define the countries, states, and cities for the dropdowns
const COUNTRIES_DATA = [
  {
    code: "us",
    name: "United States",
    states: [
      { code: "al", name: "Alabama", cities: ["Birmingham", "Montgomery", "Mobile", "Huntsville"] },
      { code: "ak", name: "Alaska", cities: ["Anchorage", "Fairbanks", "Juneau", "Sitka"] },
      { code: "az", name: "Arizona", cities: ["Phoenix", "Tucson", "Mesa", "Chandler"] },
      { code: "ca", name: "California", cities: ["Los Angeles", "San Francisco", "San Diego", "Sacramento"] },
      { code: "co", name: "Colorado", cities: ["Denver", "Colorado Springs", "Aurora", "Fort Collins"] },
      { code: "fl", name: "Florida", cities: ["Miami", "Orlando", "Tampa", "Jacksonville"] },
      { code: "ga", name: "Georgia", cities: ["Atlanta", "Savannah", "Augusta", "Columbus"] },
      { code: "ny", name: "New York", cities: ["New York City", "Buffalo", "Rochester", "Syracuse"] },
      { code: "tx", name: "Texas", cities: ["Houston", "Dallas", "Austin", "San Antonio"] },
    ]
  },
  {
    code: "uk",
    name: "United Kingdom",
    states: [
      { code: "eng", name: "England", cities: ["London", "Manchester", "Birmingham", "Liverpool"] },
      { code: "sct", name: "Scotland", cities: ["Edinburgh", "Glasgow", "Aberdeen", "Dundee"] },
      { code: "wls", name: "Wales", cities: ["Cardiff", "Swansea", "Newport", "Bangor"] },
      { code: "nir", name: "Northern Ireland", cities: ["Belfast", "Derry", "Lisburn", "Newry"] },
    ]
  },
  {
    code: "ca",
    name: "Canada",
    states: [
      { code: "on", name: "Ontario", cities: ["Toronto", "Ottawa", "Hamilton", "London"] },
      { code: "qc", name: "Quebec", cities: ["Montreal", "Quebec City", "Laval", "Gatineau"] },
      { code: "bc", name: "British Columbia", cities: ["Vancouver", "Victoria", "Surrey", "Burnaby"] },
      { code: "ab", name: "Alberta", cities: ["Calgary", "Edmonton", "Red Deer", "Lethbridge"] },
    ]
  },
  {
    code: "au",
    name: "Australia",
    states: [
      { code: "nsw", name: "New South Wales", cities: ["Sydney", "Newcastle", "Wollongong", "Wagga Wagga"] },
      { code: "vic", name: "Victoria", cities: ["Melbourne", "Geelong", "Ballarat", "Bendigo"] },
      { code: "qld", name: "Queensland", cities: ["Brisbane", "Gold Coast", "Cairns", "Townsville"] },
      { code: "wa", name: "Western Australia", cities: ["Perth", "Fremantle", "Bunbury", "Geraldton"] },
    ]
  },
  {
    code: "in",
    name: "India",
    states: [
      { code: "mh", name: "Maharashtra", cities: ["Mumbai", "Pune", "Nagpur", "Nashik"] },
      { code: "dl", name: "Delhi", cities: ["New Delhi", "Delhi", "Noida", "Gurgaon"] },
      { code: "ka", name: "Karnataka", cities: ["Bangalore", "Mysore", "Hubli", "Mangalore"] },
      { code: "tn", name: "Tamil Nadu", cities: ["Chennai", "Coimbatore", "Madurai", "Salem"] },
    ]
  },
  {
    code: "de",
    name: "Germany",
    states: [
      { code: "by", name: "Bavaria", cities: ["Munich", "Nuremberg", "Augsburg", "Regensburg"] },
      { code: "nw", name: "North Rhine-Westphalia", cities: ["Cologne", "Düsseldorf", "Dortmund", "Essen"] },
      { code: "be", name: "Berlin", cities: ["Berlin"] },
      { code: "hh", name: "Hamburg", cities: ["Hamburg"] },
    ]
  },
];

export function BusinessScraperForm() {
  // Form state
  const [category, setCategory] = useState("");
  const [country, setCountry] = useState("us");
  const [state, setState] = useState("");
  const [city, setCity] = useState("");
  const [customLocation, setCustomLocation] = useState("");
  const [maxResults, setMaxResults] = useState(5);
  const [filterShopify, setFilterShopify] = useState(false);
  const [filterActive, setFilterActive] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<ScrapedResult[] | null>(null);
  const [progress, setProgress] = useState<{ current: number; total: number } | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [apiStatus, setApiStatus] = useState<{ status: string; message: string } | null>(null);
  const [activeTab, setActiveTab] = useState<"form" | "csv">("form");

  // Get available states for the selected country
  const selectedCountry = COUNTRIES_DATA.find(c => c.code === country);
  const availableStates = selectedCountry?.states || [];

  // Get available cities for the selected state
  const selectedState = availableStates.find(s => s.code === state);
  const availableCities = selectedState?.cities || [];

  // Handle country change
  const handleCountryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newCountry = e.target.value;
    setCountry(newCountry);
    setState(""); // Reset state when country changes
    setCity(""); // Reset city when country changes
  };

  // Handle state change
  const handleStateChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newState = e.target.value;
    setState(newState);
    setCity(""); // Reset city when state changes
  };

  // Check API health when component mounts
  useEffect(() => {
    const checkHealth = async () => {
      try {
        const health = await checkApiHealth();
        setApiStatus(health);
      } catch (error) {
        console.error("API health check failed:", error);
        setApiStatus({ status: "error", message: "Flask API is not available" });
      }
    };

    checkHealth();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Determine the location
    let location = "";
    if (city) {
      // If city is selected, use it
      location = city;
      if (state) {
        // Add state if selected
        const stateName = selectedState?.name || "";
        location += `, ${stateName}`;
      }
    } else if (customLocation.trim()) {
      // If custom location is provided, use it
      location = customLocation.trim();
    } else {
      // If neither city nor custom location is provided, show error
      setError("Please select a city or enter a custom location");
      return;
    }

    if (!category.trim()) {
      setError("Search term is required");
      return;
    }

    // Reset results and set loading state
    setResults(null);
    setIsLoading(true);
    setProgress({ current: 0, total: maxResults });

    try {
      // Call the Flask API
      const data = await scrapeBusiness({
        location,
        category: category.trim(),
        country,
        maxResults,
        filterShopify,
        filterActive,
      });

      // Update results
      if (data && data.length > 0) {
        setResults(data);
      } else {
        setError("No results found. Please try a different search query.");
      }
    } catch (error) {
      console.error("Scraping error:", error);
      setError(`Error: ${error instanceof Error ? error.message : "Failed to scrape business data. Please try again."}`);
    } finally {
      setIsLoading(false);
      setProgress(null);
    }
  };

  const handleReset = () => {
    setCategory("");
    setCountry("us");
    setState("");
    setCity("");
    setCustomLocation("");
    setMaxResults(5);
    setFilterShopify(false);
    setFilterActive(false);
    setError(null);
    setResults(null);
    setProgress(null);
    setIsLoading(false);
  };

  return (
    <div className="flex flex-col gap-6">
      {/* API Status Indicator */}
      {apiStatus && apiStatus.status === "error" && (
        <div className="rounded-lg bg-red-500/20 p-4 text-red-200">
          <p className="font-semibold">API Error:</p>
          <p>{apiStatus.message}</p>
          <p className="mt-2 text-sm">
            Please make sure the Flask backend is running on http://localhost:5000
          </p>
        </div>
      )}

      {!isLoading && !results && (
        <div className="flex flex-col gap-4">
          {/* Tabs */}
          <div className="flex border-b border-white/10">
            <button
              type="button"
              className={`px-4 py-2 font-medium transition ${
                activeTab === "form"
                  ? "border-b-2 border-[hsl(280,100%,70%)] text-[hsl(280,100%,80%)]"
                  : "text-white/70 hover:text-white"
              }`}
              onClick={() => setActiveTab("form")}
            >
              Search Form
            </button>
            <button
              type="button"
              className={`px-4 py-2 font-medium transition ${
                activeTab === "csv"
                  ? "border-b-2 border-[hsl(280,100%,70%)] text-[hsl(280,100%,80%)]"
                  : "text-white/70 hover:text-white"
              }`}
              onClick={() => setActiveTab("csv")}
            >
              CSV Upload
            </button>
          </div>

          {/* Form Tab */}
          {activeTab === "form" && (
            <form onSubmit={handleSubmit} className="flex flex-col gap-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {/* Country Selection */}
            <div className="flex flex-col gap-2">
              <label htmlFor="country" className="font-medium">
                Country <span className="text-red-400">*</span>
              </label>
              <select
                id="country"
                value={country}
                onChange={handleCountryChange}
                className="rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)] scrollable-select"
              >
                {COUNTRIES_DATA.map((c) => (
                  <option key={c.code} value={c.code}>
                    {c.name}
                  </option>
                ))}
              </select>
            </div>

            {/* State Selection */}
            <div className="flex flex-col gap-2">
              <label htmlFor="state" className="font-medium">
                State/Region
              </label>
              <select
                id="state"
                value={state}
                onChange={handleStateChange}
                className="rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)] scrollable-select"
                disabled={availableStates.length === 0}
              >
                <option value="">Select a state/region</option>
                {availableStates.map((s) => (
                  <option key={s.code} value={s.code}>
                    {s.name}
                  </option>
                ))}
              </select>
            </div>

            {/* City Selection */}
            <div className="flex flex-col gap-2">
              <label htmlFor="city" className="font-medium">
                City
              </label>
              <select
                id="city"
                value={city}
                onChange={(e) => setCity(e.target.value)}
                className="rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)] scrollable-select"
                disabled={availableCities.length === 0}
              >
                <option value="">Select a city</option>
                {availableCities.map((cityName) => (
                  <option key={cityName} value={cityName}>
                    {cityName}
                  </option>
                ))}
              </select>
            </div>

            {/* Custom Location Input */}
            <div className="flex flex-col gap-2">
              <label htmlFor="customLocation" className="font-medium">
                Custom Location
                <span className="ml-2 text-sm text-white/70">
                  (Optional, if city not listed)
                </span>
              </label>
              <input
                id="customLocation"
                type="text"
                value={customLocation}
                onChange={(e) => setCustomLocation(e.target.value)}
                placeholder="Enter specific location"
                className="rounded-lg bg-white/10 px-4 py-2 text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]"
              />
            </div>

            {/* Search Term Input */}
            <div className="flex flex-col gap-2 md:col-span-2">
              <label htmlFor="category" className="font-medium">
                Search Term <span className="text-red-400">*</span>
              </label>
              <input
                id="category"
                type="text"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                placeholder="Business name, type, or any search term (e.g., Starbucks, restaurants, plumbers)"
                className="rounded-lg bg-white/10 px-4 py-2 text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]"
                required
              />
              <p className="mt-1 text-sm text-white/70">
                Enter any business name, type, or search term related to the businesses you want to find
              </p>
            </div>

            {/* Number of Results Input */}
            <div className="flex flex-col gap-2 md:col-span-2">
              <label htmlFor="maxResults" className="font-medium">
                Number of Searches to Scrape
              </label>
              <div className="flex items-center gap-4">
                <input
                  id="maxResults"
                  type="range"
                  min="1"
                  max="20"
                  value={maxResults}
                  onChange={(e) => setMaxResults(parseInt(e.target.value))}
                  className="w-full"
                />
                <span className="min-w-[2rem] text-center">{maxResults}</span>
              </div>
              <p className="mt-1 text-sm text-white/70">
                Higher values may take longer but provide more results
              </p>
            </div>

            {/* Filter Options */}
            <div className="flex flex-col gap-2 md:col-span-2">
              <label className="font-medium">Filter Options</label>
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center gap-2">
                  <input
                    id="filterShopify"
                    type="checkbox"
                    checked={filterShopify}
                    onChange={(e) => setFilterShopify(e.target.checked)}
                    className="h-4 w-4 rounded border-white/30 bg-white/10 text-[hsl(280,100%,70%)]"
                  />
                  <label htmlFor="filterShopify" className="text-sm">
                    Shopify Sites Only
                  </label>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    id="filterActive"
                    type="checkbox"
                    checked={filterActive}
                    onChange={(e) => setFilterActive(e.target.checked)}
                    className="h-4 w-4 rounded border-white/30 bg-white/10 text-[hsl(280,100%,70%)]"
                  />
                  <label htmlFor="filterActive" className="text-sm">
                    Active Domains Only
                  </label>
                </div>
              </div>
              <p className="mt-1 text-sm text-white/70">
                Filter results to include only specific types of websites
              </p>
            </div>
          </div>

          {error && (
            <div className="rounded-lg bg-red-500/20 p-4 text-red-200">
              <p className="font-semibold">Error:</p>
              <p>{error.replace(/^Error: /, '')}</p>
              <p className="mt-2 text-sm">
                Please check your search parameters and try again. Make sure you've entered a valid location and category.
              </p>
            </div>
          )}

          <div className="mt-4 flex gap-4">
            <button
              type="submit"
              className="flex-1 rounded-lg bg-[hsl(280,100%,70%)] px-6 py-3 font-semibold text-white transition hover:bg-[hsl(280,100%,60%)]"
            >
              Start Scraping
            </button>
            <button
              type="button"
              onClick={handleReset}
              className="rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20"
            >
              Reset
            </button>
          </div>
        </form>
          )}

          {/* CSV Upload Tab */}
          {activeTab === "csv" && (
            <CsvUploader
              onResults={setResults}
              onError={setError}
              onLoading={setIsLoading}
            />
          )}
        </div>
      )}

      {isLoading && <LoadingIndicator isProcessingMultiple={true} progress={progress || { current: 0, total: 5 }} />}

      {results && (
        <div className="flex flex-col gap-6">
          <ResultsTable data={results} />
          <div className="flex gap-4">
            <button
              onClick={handleReset}
              className="rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20"
            >
              New Search
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
