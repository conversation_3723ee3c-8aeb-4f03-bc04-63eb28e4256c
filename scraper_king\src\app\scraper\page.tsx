import Link from "next/link";
import { FileUpload } from "~/app/_components/scraper/FileUpload";
import { HydrateClient } from "~/trpc/server";

export default function ScraperPage() {
  return (
    <HydrateClient>
      <main className="flex flex-col gap-8">
        <div className="flex justify-end">
          <Link
            href="/"
            className="rounded-full bg-white/10 px-4 py-2 font-medium transition hover:bg-white/20"
          >
            ← Back to Home
          </Link>
        </div>
        
        <div className="rounded-xl bg-white/10 p-6 shadow-lg">
          <h2 className="mb-6 text-2xl font-bold">Upload CSV File</h2>
          <FileUpload />
        </div>
      </main>
    </HydrateClient>
  );
}
