#!/usr/bin/env python3
"""
Business Scraper Adapter Script

This script serves as an adapter between the React frontend and the existing Python backend.
It takes command-line arguments from the frontend, calls the appropriate functions from abcd.py,
and returns the results in JSON format.

This is an optimized version that uses requests/BeautifulSoup for faster scraping
and implements caching to avoid repeated scraping of the same data.
"""

import argparse
import json
import sys
import time
import os
import hashlib
import requests
import re
import asyncio
import random
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
from bs4 import BeautifulSoup

# Try to import functions from abcd.py
try:
    from abcd import (
        extract_with_requests,
        extract_phone,
        HEADERS,
        REQUEST_TIMEOUT,
        scrape_duckduckgo,
        clean_url
    )
except ImportError:
    print(json.dumps({"error": "Failed to import functions from abcd.py"}))
    sys.exit(1)

# Cache directory for storing scraped data
CACHE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cache")
os.makedirs(CACHE_DIR, exist_ok=True)

# Maximum number of concurrent requests
MAX_WORKERS = 5

def parse_arguments() -> argparse.Namespace:
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Business Scraper")
    parser.add_argument("--location", required=True, help="Location to search in")
    parser.add_argument("--category", required=True, help="Business name, type, or any search term related to the businesses you want to find")
    parser.add_argument("--country", required=True, help="Country for proxy selection")

    return parser.parse_args()

def get_cache_key(url: str) -> str:
    """Generate a cache key for a URL."""
    return hashlib.md5(url.encode()).hexdigest()

def get_cached_data(url: str) -> Optional[Dict[str, Any]]:
    """Get cached data for a URL if it exists."""
    cache_key = get_cache_key(url)
    cache_file = os.path.join(CACHE_DIR, f"{cache_key}.json")

    if os.path.exists(cache_file):
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # Check if the cache is still valid (less than 24 hours old)
                if time.time() - data.get('timestamp', 0) < 86400:  # 24 hours
                    print(f"Using cached data for {url}")
                    return data.get('data')
        except Exception as e:
            print(f"Error reading cache for {url}: {e}")

    return None

def save_to_cache(url: str, data: Dict[str, Any]) -> None:
    """Save data to cache."""
    cache_key = get_cache_key(url)
    cache_file = os.path.join(CACHE_DIR, f"{cache_key}.json")

    try:
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': time.time(),
                'data': data
            }, f)
    except Exception as e:
        print(f"Error saving cache for {url}: {e}")

def scrape_url(url: str, category: str) -> Dict[str, Any]:
    """
    Scrape a single URL using the extract_with_requests function from abcd.py.

    Args:
        url: The URL to scrape
        category: The business category

    Returns:
        Dictionary with business data
    """
    # Check cache first
    cached_data = get_cached_data(url)
    if cached_data:
        return cached_data

    try:
        # Use the extract_with_requests function from abcd.py
        data = extract_with_requests(url)

        if data:
            # Transform the data to match our expected format
            business_data = {
                "name": data.get("page_title", "Unknown Business"),
                "address": data.get("address", "N/A"),
                "phone": data.get("phone", "N/A"),
                "email": data.get("email", "N/A"),
                "url": url,
                "category": category,
                "social_links": data.get("social_links", "N/A")
            }

            # Save to cache
            save_to_cache(url, business_data)

            return business_data
        else:
            # If extract_with_requests returned None, raise an exception
            raise Exception("Failed to extract data from URL")
    except Exception as e:
        print(f"Error scraping {url}: {e}")
        # Re-raise the exception to be handled by the caller
        raise

async def get_search_results(query: str, max_results: int = 5) -> List[Dict[str, str]]:
    """
    Get search results from DuckDuckGo for a given query.

    Args:
        query: The search query
        max_results: Maximum number of results to return

    Returns:
        List of dictionaries with title and URL
    """
    try:
        # Use the scrape_duckduckgo function from abcd.py
        results = await scrape_duckduckgo([query], max_results)
        return results
    except Exception as e:
        print(f"Error getting search results: {e}")
        return []

def scrape_business_data(args: argparse.Namespace) -> List[Dict[str, Any]]:
    """
    Scrape business data using the abcd.py functions.

    Args:
        args: Command-line arguments

    Returns:
        List of business data dictionaries
    """
    try:
        # Construct search query using the search term (stored in args.category)
        search_query = f"{args.category} in {args.location}, {args.country}"
        print(f"Searching for: {search_query}")

        # Get search results from DuckDuckGo
        # We need to run the async function in a synchronous context
        import asyncio
        search_results = asyncio.run(get_search_results(search_query))

        if not search_results:
            print("No search results found.")
            raise Exception("No search results found for the given query")

        # Extract URLs from search results
        urls = []
        for result in search_results:
            url = result.get("Result URL")
            if url:
                clean_result_url = clean_url(url)
                if clean_result_url:
                    urls.append(clean_result_url)

        if not urls:
            print("No valid URLs found in search results.")
            raise Exception("No valid URLs found in search results")

        print(f"Found {len(urls)} URLs to scrape")

        # Use ThreadPoolExecutor for concurrent scraping
        results = []
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # Submit all scraping tasks
            future_to_url = {
                executor.submit(scrape_url, url, args.category): url
                for url in urls
            }

            # Process results as they complete
            for future in future_to_url:
                url = future_to_url[future]
                try:
                    data = future.result()
                    results.append(data)
                    # Print progress
                    print(f"Scraped {len(results)}/{len(urls)}: {url}")
                except Exception as e:
                    print(f"Error processing {url}: {e}")
                    # Skip failed URLs instead of adding mock data
                    continue

        if not results:
            print("No results were successfully scraped.")
            raise Exception("No results were successfully scraped")

        return results

    except Exception as e:
        print(f"Error in scrape_business_data: {e}")
        # Re-raise the exception
        raise



def main() -> None:
    """Main function to run the scraper."""
    try:
        args = parse_arguments()

        # Use the optimized scraping function
        results = scrape_business_data(args)

        # Print the results as JSON
        print(json.dumps(results))

    except Exception as e:
        print(json.dumps({"error": str(e)}))
        sys.exit(1)

if __name__ == "__main__":
    main()
