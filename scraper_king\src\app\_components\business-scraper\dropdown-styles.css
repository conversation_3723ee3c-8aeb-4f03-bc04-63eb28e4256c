/* Custom styles for dropdown menus */
select {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

select::-webkit-scrollbar {
  width: 8px;
}

select::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

select::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

/* Ensure dropdown options are visible and styled properly */
select option {
  background-color: #2e026d;
  color: white;
  padding: 8px;
}

/* Add some height to the dropdowns to make scrolling more obvious */
.scrollable-select {
  max-height: 200px;
  overflow-y: auto;
}
