(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2490:(e,t,s)=>{"use strict";s.d(t,{useIsFetching:()=>u});var r=s(2115),n=s(7165),i=s(6715);function u(e,t){let s=(0,i.useQueryClient)(t),u=s.getQueryCache();return r.useSyncExternalStore(r.useCallback(e=>u.subscribe(n.jG.batchCalls(e)),[u]),()=>s.isFetching(e),()=>s.isFetching(e))}},3215:(e,t,s)=>{Promise.resolve().then(s.bind(s,7573)),Promise.resolve().then(s.bind(s,1581)),Promise.resolve().then(s.bind(s,6715)),Promise.resolve().then(s.bind(s,382)),Promise.resolve().then(s.bind(s,8822)),Promise.resolve().then(s.bind(s,2490)),Promise.resolve().then(s.bind(s,5041)),Promise.resolve().then(s.bind(s,9138)),Promise.resolve().then(s.bind(s,1610)),Promise.resolve().then(s.bind(s,5838)),Promise.resolve().then(s.bind(s,5490)),Promise.resolve().then(s.bind(s,1142)),Promise.resolve().then(s.bind(s,3666)),Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.bind(s,4221))},3964:(e,t,s)=>{"use strict";let r;s.d(t,{TRPCReactProvider:()=>m,F:()=>f});var n=s(5155),i=s(6715),u=s(1213),l=s(7566),a=s(2115),o=s(9177),d=s(2775),c=s(1451);let h=()=>new d.E({defaultOptions:{queries:{staleTime:3e4},dehydrate:{serializeData:o.Ay.serialize,shouldDehydrateQuery:e=>(0,c.XS)(e)||"pending"===e.state.status},hydrate:{deserializeData:o.Ay.deserialize}}});s(9509);let p=()=>(null!=r||(r=h()),r),f=(0,l.pY)();function m(e){let t=p(),[s]=(0,a.useState)(()=>f.createClient({links:[(0,u.$H)({enabled:e=>"down"===e.direction&&e.result instanceof Error}),(0,u.N9)({transformer:o.Ay,url:window.location.origin+"/api/trpc",headers:()=>{let e=new Headers;return e.set("x-trpc-source","nextjs-react"),e}})]}));return(0,n.jsx)(i.QueryClientProvider,{client:t,children:(0,n.jsx)(f.Provider,{client:s,queryClient:t,children:e.children})})}},4221:(e,t,s)=>{"use strict";s.d(t,{LatestPost:()=>u});var r=s(5155),n=s(2115),i=s(3964);function u(){let[e]=i.F.post.getLatest.useSuspenseQuery(),t=i.F.useUtils(),[s,u]=(0,n.useState)(""),l=i.F.post.create.useMutation({onSuccess:async()=>{await t.post.invalidate(),u("")}});return(0,r.jsxs)("div",{className:"w-full max-w-xs",children:[e?(0,r.jsxs)("p",{className:"truncate",children:["Your most recent post: ",e.name]}):(0,r.jsx)("p",{children:"You have no posts yet."}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),l.mutate({name:s})},className:"flex flex-col gap-2",children:[(0,r.jsx)("input",{type:"text",placeholder:"Title",value:s,onChange:e=>u(e.target.value),className:"w-full rounded-full bg-white/10 px-4 py-2 text-white"}),(0,r.jsx)("button",{type:"submit",className:"rounded-full bg-white/10 px-10 py-3 font-semibold transition hover:bg-white/20",disabled:l.isPending,children:l.isPending?"Submitting...":"Submit"})]})]})}},7573:(e,t,s)=>{"use strict";s.d(t,{HydrationBoundary:()=>l});var r=s(2115),n=s(1451),i=s(6715),u=(e,t)=>"object"==typeof e&&null!==e&&t in e,l=e=>{let{children:t,options:s={},state:l,queryClient:a}=e,o=(0,i.useQueryClient)(a),[d,c]=r.useState(),h=r.useRef(s);return h.current=s,r.useMemo(()=>{if(l){if("object"!=typeof l)return;let e=o.getQueryCache(),t=l.queries||[],s=[],r=[];for(let n of t){let t=e.get(n.queryHash);if(t){let e=n.state.dataUpdatedAt>t.state.dataUpdatedAt||u(n.promise,"status")&&u(t.promise,"status")&&n.promise.status!==t.promise.status,s=null==d?void 0:d.find(e=>e.queryHash===n.queryHash);e&&(!s||n.state.dataUpdatedAt>s.state.dataUpdatedAt)&&r.push(n)}else s.push(n)}s.length>0&&(0,n.Qv)(o,{queries:s},h.current),r.length>0&&c(e=>e?[...e,...r]:r)}},[o,d,l]),r.useEffect(()=>{d&&((0,n.Qv)(o,{queries:d},h.current),c(void 0))},[o,d]),t}},9138:(e,t,s)=>{"use strict";s.d(t,{useIsMutating:()=>l,useMutationState:()=>o});var r=s(2115),n=s(2020),i=s(7165),u=s(6715);function l(e,t){let s=(0,u.useQueryClient)(t);return o({filters:{...e,status:"pending"}},s).length}function a(e,t){return e.findAll(t.filters).map(e=>t.select?t.select(e):e.state)}function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,s=(0,u.useQueryClient)(t).getMutationCache(),l=r.useRef(e),o=r.useRef(null);return o.current||(o.current=a(s,e)),r.useEffect(()=>{l.current=e}),r.useSyncExternalStore(r.useCallback(e=>s.subscribe(()=>{let t=(0,n.BH)(o.current,a(s,l.current));o.current!==t&&(o.current=t,i.jG.schedule(e))}),[s]),()=>o.current,()=>o.current)}}},e=>{var t=t=>e(e.s=t);e.O(0,[874,616,308,441,684,358],()=>t(3215)),_N_E=e.O()}]);