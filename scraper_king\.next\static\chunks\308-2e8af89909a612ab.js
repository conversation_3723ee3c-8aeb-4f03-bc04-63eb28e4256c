"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[308],{503:(e,t,r)=>{new WeakMap,Symbol.toStringTag},1213:(e,t,r)=>{function n(e){let t={subscribe(t){let r=null,n=!1,i=!1,s=!1;function o(){if(null===r){s=!0;return}!i&&(i=!0,"function"==typeof r?r():r&&r.unsubscribe())}return r=e({next(e){n||t.next?.(e)},error(e){n||(n=!0,t.error?.(e),o())},complete(){n||(n=!0,t.complete?.(),o())}}),s&&o(),{unsubscribe:o}},pipe:(...e)=>e.reduce(i,t)};return t}function i(e,t){return t(e)}r.d(t,{Ke:()=>l,XT:()=>h,Xq:()=>d,n2:()=>y,N9:()=>q,$H:()=>Q}),Symbol();var s=r(9343);function o(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class a extends Error{static from(e,t={}){return e instanceof a||e instanceof Error&&"TRPCClientError"===e.name?(t.meta&&(e.meta={...e.meta,...t.meta}),e):(0,s.Gv)(e)&&(0,s.Gv)(e.error)&&"number"==typeof e.error.code&&"string"==typeof e.error.message?new a(e.error.message,{...t,result:e}):new a("string"==typeof e?e:(0,s.Gv)(e)&&"string"==typeof e.message?e.message:"Unknown error",{...t,cause:e})}constructor(e,t){let r=t?.cause;super(e,{cause:r}),o(this,"cause",void 0),o(this,"shape",void 0),o(this,"data",void 0),o(this,"meta",void 0),this.meta=t?.meta,this.cause=r,this.shape=t?.result?.error,this.data=t?.result?.error.data,this.name="TRPCClientError",Object.setPrototypeOf(this,a.prototype)}}function u(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class l{$request(e){var t;return(t={links:this.links,op:{...e,context:e.context??{},id:++this.requestId}},n(e=>(function e(r=0,n=t.op){let i=t.links[r];if(!i)throw Error("No more links to execute - did you forget to add an ending link?");return i({op:n,next:t=>e(r+1,t)})})().subscribe(e))).pipe(e=>{let t=0,r=null,i=[];return n(n=>(t++,i.push(n),r||(r=e.subscribe({next(e){for(let t of i)t.next?.(e)},error(e){for(let t of i)t.error?.(e)},complete(){for(let e of i)e.complete?.()}})),{unsubscribe(){if(0==--t&&r){let e=r;r=null,e.unsubscribe()}let e=i.findIndex(e=>e===n);e>-1&&i.splice(e,1)}}))})}async requestAsPromise(e){try{let t=this.$request(e);return(await function(e){let t=new AbortController;return new Promise((r,n)=>{let i=!1;function s(){i||(i=!0,o.unsubscribe())}t.signal.addEventListener("abort",()=>{n(t.signal.reason)});let o=e.subscribe({next(e){i=!0,r(e),s()},error(e){n(e)},complete(){t.abort(),s()}})})}(t)).result.data}catch(e){throw a.from(e)}}query(e,t,r){return this.requestAsPromise({type:"query",path:e,input:t,context:r?.context,signal:r?.signal})}mutation(e,t,r){return this.requestAsPromise({type:"mutation",path:e,input:t,context:r?.context,signal:r?.signal})}subscription(e,t,r){return this.$request({type:"subscription",path:e,input:t,context:r.context,signal:r.signal}).subscribe({next(e){switch(e.result.type){case"state":r.onConnectionStateChange?.(e.result);break;case"started":r.onStarted?.({context:e.context});break;case"stopped":r.onStopped?.();break;case"data":case void 0:r.onData?.(e.result.data)}},error(e){r.onError?.(e)},complete(){r.onComplete?.()}})}constructor(e){u(this,"links",void 0),u(this,"runtime",void 0),u(this,"requestId",void 0),this.requestId=0,this.runtime={},this.links=e.links.map(e=>e(this.runtime))}}let c=Symbol.for("trpc_untypedClient"),f={query:"query",mutate:"mutation",subscribe:"subscription"},p=e=>f[e];function d(e){let t=(0,s.vX)(({path:t,args:r})=>{let n=[...t],i=p(n.pop()),s=n.join(".");return e[i](s,...r)});return(0,s.U6)(r=>r===c?e:t[r])}function h(e){return d(new l(e))}function y(e){return e[c]}let m=e=>"function"==typeof e,g={query:"GET",mutation:"POST",subscription:"PATCH"};function b(e){return"input"in e?e.transformer.input.serialize(e.input):function(e){let t={};for(let r=0;r<e.length;r++){let n=e[r];t[r]=n}return t}(e.inputs.map(t=>e.transformer.input.serialize(t)))}let w=e=>{let t=e.url.split("?"),r=t[0].replace(/\/$/,"")+"/"+e.path,n=[];if(t[1]&&n.push(t[1]),"inputs"in e&&n.push("batch=1"),"query"===e.type||"subscription"===e.type){let t=b(e);void 0!==t&&"POST"!==e.methodOverride&&n.push(`input=${encodeURIComponent(JSON.stringify(t))}`)}return n.length&&(r+="?"+n.join("&")),r},v=e=>{if("query"===e.type&&"POST"!==e.methodOverride)return;let t=b(e);return void 0!==t?JSON.stringify(t):void 0};class E extends Error{constructor(){let e="AbortError";super(e),this.name=e,this.message=e}}let O=e=>{if(e?.aborted){if(e.throwIfAborted?.(),"undefined"!=typeof DOMException)throw new DOMException("AbortError","AbortError");throw new E}};async function S(e){O(e.signal);let t=e.getUrl(e),r=e.getBody(e),{type:n}=e,i=await (async()=>{let t=await e.headers();return Symbol.iterator in t?Object.fromEntries(t):t})(),s={...e.contentTypeHeader?{"content-type":e.contentTypeHeader}:{},...e.trpcAcceptHeader?{"trpc-accept":e.trpcAcceptHeader}:void 0,...i};return(function(e){if(e)return e;if("undefined"!=typeof window&&m(window.fetch))return window.fetch;if("undefined"!=typeof globalThis&&m(globalThis.fetch))return globalThis.fetch;throw Error("No fetch implementation found")})(e.fetch)(t,{method:e.methodOverride??g[n],signal:e.signal,body:r,headers:s})}let T=()=>{throw Error("Something went wrong. Please submit an issue at https://github.com/trpc/trpc/issues/new")};function P(e){let t=null,r=null,n=()=>{clearTimeout(r),r=null,t=null};function i(){let r=function(t){let r=[[]],n=0;for(;;){let i=t[n];if(!i)break;let s=r[r.length-1];if(i.aborted){i.reject?.(Error("Aborted")),n++;continue}if(e.validate(s.concat(i).map(e=>e.key))){s.push(i),n++;continue}if(0===s.length){i.reject?.(Error("Input is too big for a single dispatch")),n++;continue}r.push([])}return r}(t);for(let t of(n(),r)){if(!t.length)continue;let r={items:t};for(let e of t)e.batch=r;e.fetch(r.items.map(e=>e.key)).then(async e=>{for(let t of(await Promise.all(e.map(async(e,t)=>{let n=r.items[t];try{let t=await Promise.resolve(e);n.resolve?.(t)}catch(e){n.reject?.(e)}n.batch=null,n.reject=null,n.resolve=null})),r.items))t.reject?.(Error("Missing result")),t.batch=null}).catch(e=>{for(let t of r.items)t.reject?.(e),t.batch=null})}}return{load:function(e){let n={aborted:!1,key:e,batch:null,resolve:T,reject:T},s=new Promise((e,r)=>{n.reject=r,n.resolve=e,t||(t=[]),t.push(n)});return r||(r=setTimeout(i)),s}}}function q(e){var t;let r={url:e.url.toString(),fetch:e.fetch,transformer:(t=e.transformer)?"input"in t?t:{input:t,output:t}:{input:{serialize:e=>e,deserialize:e=>e},output:{serialize:e=>e,deserialize:e=>e}},methodOverride:e.methodOverride},i=e.maxURLLength??1/0,o=e.maxItems??1/0;return()=>{let t=t=>({validate(e){if(i===1/0&&o===1/0)return!0;if(e.length>o)return!1;let n=e.map(e=>e.path).join(","),s=e.map(e=>e.input);return w({...r,type:t,path:n,inputs:s,signal:null}).length<=i},async fetch(n){let i=n.map(e=>e.path).join(","),o=n.map(e=>e.input),u=function(...e){let t=new AbortController,r=e.length,n=0,i=()=>{++n===r&&t.abort()};for(let t of e)t?.aborted?i():t?.addEventListener("abort",i,{once:!0});return t.signal}(...n.map(e=>e.signal)),l=new AbortController,c=S({...r,signal:function(...e){let t=new AbortController;for(let r of e)r?.aborted?t.abort():r?.addEventListener("abort",()=>t.abort(),{once:!0});return t.signal}(u,l.signal),type:t,contentTypeHeader:"application/json",trpcAcceptHeader:"application/jsonl",getUrl:w,getBody:v,inputs:o,path:i,headers:()=>e.headers?"function"==typeof e.headers?e.headers({opList:n}):e.headers:{}}),f=await c,[p]=await (0,s.le)({from:f.body,deserialize:r.transformer.output.deserialize,formatError(e){let t=e.error;return a.from({error:t})},abortController:l});return Object.keys(n).map(async e=>{let t=await Promise.resolve(p[e]);if("result"in t){let e=await Promise.resolve(t.result);t={result:{data:await Promise.resolve(e.data)}}}return{json:t,meta:{response:f}}})}}),u={query:P(t("query")),mutation:P(t("mutation"))};return({op:e})=>n(t=>{let r;if("subscription"===e.type)throw Error("Subscriptions are unsupported by `httpBatchStreamLink` - use `httpSubscriptionLink` or `wsLink`");return u[e.type].load(e).then(e=>{if(r=e,"error"in e.json)return void t.error(a.from(e.json,{meta:e.meta}));if("result"in e.json){t.next({context:e.meta,result:e.json.result}),t.complete();return}t.complete()}).catch(e=>{t.error(a.from(e,{meta:r?.meta}))}),()=>{}})}}let D={css:{query:["72e3ff","3fb0d8"],mutation:["c5a3fc","904dfc"],subscription:["ff49e1","d83fbe"]},ansi:{regular:{query:["\x1b[30;46m","\x1b[97;46m"],mutation:["\x1b[30;45m","\x1b[97;45m"],subscription:["\x1b[30;42m","\x1b[97;42m"]},bold:{query:["\x1b[1;30;46m","\x1b[1;97;46m"],mutation:["\x1b[1;30;45m","\x1b[1;97;45m"],subscription:["\x1b[1;30;42m","\x1b[1;97;42m"]}}},_=({c:e=console,colorMode:t="css",withContext:r})=>n=>{let i=n.input,s="undefined"!=typeof FormData&&i instanceof FormData?Object.fromEntries(i):i,{parts:o,args:a}=function(e){let{direction:t,type:r,withContext:n,path:i,id:s,input:o}=e,a=[],u=[];if("none"===e.colorMode)a.push("up"===t?">>":"<<",r,`#${s}`,i);else if("ansi"===e.colorMode){let[e,n]=D.ansi.regular[r],[o,u]=D.ansi.bold[r];a.push("up"===t?e:n,"up"===t?">>":"<<",r,"up"===t?o:u,`#${s}`,i,"\x1b[0m")}else{let[e,n]=D.css[r],o=`
    background-color: #${"up"===t?e:n};
    color: ${"up"===t?"black":"white"};
    padding: 2px;
  `;a.push("%c","up"===t?">>":"<<",r,`#${s}`,`%c${i}%c`,"%O"),u.push(o,`${o}; font-weight: bold;`,`${o}; font-weight: normal;`)}return"up"===t?u.push(n?{input:o,context:e.context}:{input:o}):u.push({input:o,result:e.result,elapsedMs:e.elapsedMs,...n&&{context:e.context}}),{parts:a,args:u}}({...n,colorMode:t,input:s,withContext:r});e["down"===n.direction&&n.result&&(n.result instanceof Error||"error"in n.result.result&&n.result.result.error)?"error":"log"].apply(null,[o.join(" ")].concat(a))};function Q(e={}){let{enabled:t=()=>!0}=e,r=e.colorMode??("undefined"==typeof window?"ansi":"css"),i=e.withContext??"css"===r,{logger:s=_({c:e.console,colorMode:r,withContext:i})}=e;return()=>({op:e,next:r})=>n(i=>{var o;t({...e,direction:"up"})&&s({...e,direction:"up"});let a=Date.now();function u(r){let n=Date.now()-a;t({...e,direction:"down",result:r})&&s({...e,direction:"down",elapsedMs:n,result:r})}return r(e).pipe((o={next(e){u(e)},error(e){u(e)}},e=>n(t=>e.subscribe({next(e){o.next?.(e),t.next(e)},error(e){o.error?.(e),t.error(e)},complete(){o.complete?.(),t.complete()}})))).subscribe(i)})}let R=(e,...t)=>"function"==typeof e?e(...t):e;async function A(e){let t=await R(e.url);if(!e.connectionParams)return t;let r=t.includes("?")?"&":"?";return t+`${r}connectionParams=1`}async function C(e){return JSON.stringify({method:"connectionParams",data:await R(e)})}function I(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class k{get ws(){return this.wsObservable.get()}set ws(e){this.wsObservable.next(e)}isOpen(){return!!this.ws&&this.ws.readyState===this.WebSocketPonyfill.OPEN&&!this.openPromise}isClosed(){return!!this.ws&&(this.ws.readyState===this.WebSocketPonyfill.CLOSING||this.ws.readyState===this.WebSocketPonyfill.CLOSED)}async open(){if(this.openPromise)return this.openPromise;this.id=++k.connectCount;let e=A(this.urlOptions).then(e=>new this.WebSocketPonyfill(e));this.openPromise=e.then(async e=>{this.ws=e,e.addEventListener("message",function({data:e}){"PING"===e&&this.send("PONG")}),this.keepAliveOpts.enabled&&function(e,{intervalMs:t,pongTimeoutMs:r}){let n,i;function s(){n=setTimeout(()=>{e.send("PING"),i=setTimeout(()=>{e.close()},r)},t)}e.addEventListener("open",s),e.addEventListener("message",({data:e})=>{clearTimeout(n),s(),"PONG"===e&&(clearTimeout(i),clearTimeout(n),s())}),e.addEventListener("close",()=>{clearTimeout(n),clearTimeout(i)})}(e,this.keepAliveOpts),e.addEventListener("close",()=>{this.ws===e&&(this.ws=null)}),await function(e){let t,r,{promise:n,resolve:i,reject:s}={promise:new Promise((e,n)=>{t=e,r=n}),resolve:t,reject:r};return e.addEventListener("open",()=>{e.removeEventListener("error",s),i()}),e.addEventListener("error",s),n}(e),this.urlOptions.connectionParams&&e.send(await C(this.urlOptions.connectionParams))});try{await this.openPromise}finally{this.openPromise=null}}async close(){try{await this.openPromise}finally{this.ws?.close()}}constructor(e){if(I(this,"id",++k.connectCount),I(this,"WebSocketPonyfill",void 0),I(this,"urlOptions",void 0),I(this,"keepAliveOpts",void 0),I(this,"wsObservable",function(e){let t=null,r=[],i=e=>{void 0!==t&&e.next(t),r.push(e)},s=e=>{r.splice(r.indexOf(e),1)},o=n(e=>(i(e),()=>{s(e)}));return o.next=e=>{if(t!==e)for(let n of(t=e,r))n.next(e)},o.get=()=>t,o}(0)),I(this,"openPromise",null),this.WebSocketPonyfill=e.WebSocketPonyfill??WebSocket,!this.WebSocketPonyfill)throw Error("No WebSocket implementation found - you probably don't want to use this on the server, but if you do you need to pass a `WebSocket`-ponyfill");this.urlOptions=e.urlOptions,this.keepAliveOpts=e.keepAlive}}I(k,"connectCount",0);var j=r(6090);r(6823),r(503),r(3223),r(4868),j.Y.BAD_GATEWAY,j.Y.SERVICE_UNAVAILABLE,j.Y.GATEWAY_TIMEOUT,j.Y.INTERNAL_SERVER_ERROR},2775:(e,t,r)=>{r.d(t,{E:()=>h});var n=r(2020),i=r(9853),s=r(7165),o=r(5910),a=class extends o.Q{constructor(e={}){super(),this.config=e,this.#e=new Map}#e;build(e,t,r){let s=t.queryKey,o=t.queryHash??(0,n.F$)(s,t),a=this.get(o);return a||(a=new i.X({client:e,queryKey:s,queryHash:o,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(s)}),this.add(a)),a}add(e){this.#e.has(e.queryHash)||(this.#e.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#e.get(e.queryHash);t&&(e.destroy(),t===e&&this.#e.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){s.jG.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#e.get(e)}getAll(){return[...this.#e.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,n.MK)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,n.MK)(e,t)):t}notify(e){s.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){s.jG.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){s.jG.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},u=r(4560),l=class extends o.Q{constructor(e={}){super(),this.config=e,this.#t=new Set,this.#r=new Map,this.#n=0}#t;#r;#n;build(e,t,r){let n=new u.s({mutationCache:this,mutationId:++this.#n,options:e.defaultMutationOptions(t),state:r});return this.add(n),n}add(e){this.#t.add(e);let t=c(e);if("string"==typeof t){let r=this.#r.get(t);r?r.push(e):this.#r.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#t.delete(e)){let t=c(e);if("string"==typeof t){let r=this.#r.get(t);if(r)if(r.length>1){let t=r.indexOf(e);-1!==t&&r.splice(t,1)}else r[0]===e&&this.#r.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=c(e);if("string"!=typeof t)return!0;{let r=this.#r.get(t),n=r?.find(e=>"pending"===e.state.status);return!n||n===e}}runNext(e){let t=c(e);if("string"!=typeof t)return Promise.resolve();{let r=this.#r.get(t)?.find(t=>t!==e&&t.state.isPaused);return r?.continue()??Promise.resolve()}}clear(){s.jG.batch(()=>{this.#t.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#t.clear(),this.#r.clear()})}getAll(){return Array.from(this.#t)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,n.nJ)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,n.nJ)(e,t))}notify(e){s.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return s.jG.batch(()=>Promise.all(e.map(e=>e.continue().catch(n.lQ))))}};function c(e){return e.options.scope?.id}var f=r(920),p=r(1239),d=r(4275),h=class{#i;#s;#o;#a;#u;#l;#c;#f;constructor(e={}){this.#i=e.queryCache||new a,this.#s=e.mutationCache||new l,this.#o=e.defaultOptions||{},this.#a=new Map,this.#u=new Map,this.#l=0}mount(){this.#l++,1===this.#l&&(this.#c=f.m.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#i.onFocus())}),this.#f=p.t.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#i.onOnline())}))}unmount(){this.#l--,0===this.#l&&(this.#c?.(),this.#c=void 0,this.#f?.(),this.#f=void 0)}isFetching(e){return this.#i.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#s.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#i.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),r=this.#i.build(this,t),i=r.state.data;return void 0===i?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime((0,n.d2)(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(i))}getQueriesData(e){return this.#i.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,r){let i=this.defaultQueryOptions({queryKey:e}),s=this.#i.get(i.queryHash),o=s?.state.data,a=(0,n.Zw)(t,o);if(void 0!==a)return this.#i.build(this,i).setData(a,{...r,manual:!0})}setQueriesData(e,t,r){return s.jG.batch(()=>this.#i.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,r)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#i.get(t.queryHash)?.state}removeQueries(e){let t=this.#i;s.jG.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let r=this.#i;return s.jG.batch(()=>(r.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let r={revert:!0,...t};return Promise.all(s.jG.batch(()=>this.#i.findAll(e).map(e=>e.cancel(r)))).then(n.lQ).catch(n.lQ)}invalidateQueries(e,t={}){return s.jG.batch(()=>(this.#i.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let r={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(s.jG.batch(()=>this.#i.findAll(e).filter(e=>!e.isDisabled()).map(e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(n.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(n.lQ)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let r=this.#i.build(this,t);return r.isStaleByTime((0,n.d2)(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(n.lQ).catch(n.lQ)}fetchInfiniteQuery(e){return e.behavior=(0,d.PL)(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(n.lQ).catch(n.lQ)}ensureInfiniteQueryData(e){return e.behavior=(0,d.PL)(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return p.t.isOnline()?this.#s.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#i}getMutationCache(){return this.#s}getDefaultOptions(){return this.#o}setDefaultOptions(e){this.#o=e}setQueryDefaults(e,t){this.#a.set((0,n.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#a.values()],r={};return t.forEach(t=>{(0,n.Cp)(e,t.queryKey)&&Object.assign(r,t.defaultOptions)}),r}setMutationDefaults(e,t){this.#u.set((0,n.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#u.values()],r={};return t.forEach(t=>{(0,n.Cp)(e,t.mutationKey)&&Object.assign(r,t.defaultOptions)}),r}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#o.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,n.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===n.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#o.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#i.clear(),this.#s.clear()}}},2927:(e,t,r)=>{r.d(t,{Gv:()=>s,IT:()=>n,QQ:()=>a,Td:()=>l,Tn:()=>o,eF:()=>c,uf:()=>i});let n=Symbol();function i(e,...t){let r=Object.assign(Object.create(null),e);for(let e of t)for(let t in e){if(t in r&&r[t]!==e[t])throw Error(`Duplicate key ${t}`);r[t]=e[t]}return r}function s(e){return!!e&&!Array.isArray(e)&&"object"==typeof e}function o(e){return"function"==typeof e}function a(e){return Object.assign(Object.create(null),e)}let u="function"==typeof Symbol&&!!Symbol.asyncIterator;function l(e){return u&&s(e)&&Symbol.asyncIterator in e}let c=e=>e()},3223:(e,t,r)=>{var n,i;function s(e,t){let r=e[Symbol.dispose];return e[Symbol.dispose]=()=>{t(),r?.()},e}r.d(t,{T:()=>s}),(n=Symbol).dispose??(n.dispose=Symbol()),(i=Symbol).asyncDispose??(i.asyncDispose=Symbol())},4868:(e,t,r)=>{r.d(t,{N:()=>n});let n="undefined"==typeof window||"Deno"in window||globalThis.process?.env?.NODE_ENV==="test"||!!globalThis.process?.env?.JEST_WORKER_ID||!!globalThis.process?.env?.VITEST_WORKER_ID},6090:(e,t,r)=>{r.d(t,{Y:()=>n});let n={PARSE_ERROR:-32700,BAD_REQUEST:-32600,INTERNAL_SERVER_ERROR:-32603,NOT_IMPLEMENTED:-32603,BAD_GATEWAY:-32603,SERVICE_UNAVAILABLE:-32603,GATEWAY_TIMEOUT:-32603,UNAUTHORIZED:-32001,FORBIDDEN:-32003,NOT_FOUND:-32004,METHOD_NOT_SUPPORTED:-32005,TIMEOUT:-32008,CONFLICT:-32009,PRECONDITION_FAILED:-32012,PAYLOAD_TOO_LARGE:-32013,UNSUPPORTED_MEDIA_TYPE:-32015,UNPROCESSABLE_CONTENT:-32022,TOO_MANY_REQUESTS:-32029,CLIENT_CLOSED_REQUEST:-32099}},6823:(e,t,r)=>{r(2927)},7566:(e,t,r)=>{r.d(t,{pY:()=>q});var n=r(1213),i=r(2020),s=r(9343);function o(e,t,r){let n=e.flatMap(e=>e.split("."));if(!t&&(!r||"any"===r))return n.length?[n]:[];if("infinite"===r&&(0,s.Gv)(t)&&("direction"in t||"cursor"in t)){let{cursor:e,direction:r,...i}=t;return[n,{input:i,type:"infinite"}]}return[n,{...void 0!==t&&t!==i.hT&&{input:t},...r&&"any"!==r&&{type:r}}]}function a(e){return o(e,void 0,"any")}var u=r(2115);let l=["client","ssrContext","ssrState","abortOnUnmount"],c=u.createContext?.(null),f=e=>{switch(e){case"queryOptions":case"fetch":case"ensureData":case"prefetch":case"getData":case"setData":case"setQueriesData":return"query";case"infiniteQueryOptions":case"fetchInfinite":case"prefetchInfinite":case"getInfiniteData":case"setInfiniteData":return"infinite";case"setMutationDefaults":case"getMutationDefaults":case"isMutating":case"cancel":case"invalidate":case"refetch":case"reset":return"any"}};var p=r(5838),d=r(6715),h=r(3666),y=r(5041),m=r(8822),g=r(5490),b=r(1610),w=r(1142);function v(e,t,r){let n=e[0],i=e[1]?.input;return r&&(i={...i??{},...r.pageParam?{cursor:r.pageParam}:{},direction:r.direction}),[n.join("."),i,t?.trpc]}function E(e){return{path:e.path.join(".")}}function O(e){let t=E(e);return u.useMemo(()=>t,[t])}async function S(e,t,r){let n=t.getQueryCache().build(t,{queryKey:r});n.setState({data:[],status:"success"});let i=[];for await(let t of e)i.push(t),n.setState({data:[...i]});return i}function T(e){let t=e instanceof n.Ke?e:(0,n.n2)(e);return(0,s.vX)(e=>{let r=e.path,n=r.join("."),[i,s]=e.args;return{queryKey:o(r,i,"query"),queryFn:()=>t.query(n,i,s?.trpc),...s}})}let P=(e,t)=>new Proxy(e,{get:(e,r)=>(t(r),e[r])});function q(e){return function(e){let t=(0,s.vX)(({path:t,args:r})=>{let n=[...t],i=n.pop();if("useMutation"===i)return e[i](n,...r);if("_def"===i)return{path:n};let[s,...o]=r,a=o[0]||{};return e[i](n,s,a)});return(0,s.U6)(r=>"useContext"===r||"useUtils"===r?()=>{let t=e.useUtils();return u.useMemo(()=>(function(e){var t;let r=(0,n.Xq)(e.client),i=(t=e,(0,s.vX)(e=>{let r=[...e.path],n=r.pop(),i=[...e.args],s=i.shift(),u=o(r,s,f(n));return({infiniteQueryOptions:()=>t.infiniteQueryOptions(r,u,i[0]),queryOptions:()=>t.queryOptions(r,u,...i),fetch:()=>t.fetchQuery(u,...i),fetchInfinite:()=>t.fetchInfiniteQuery(u,i[0]),prefetch:()=>t.prefetchQuery(u,...i),prefetchInfinite:()=>t.prefetchInfiniteQuery(u,i[0]),ensureData:()=>t.ensureQueryData(u,...i),invalidate:()=>t.invalidateQueries(u,...i),reset:()=>t.resetQueries(u,...i),refetch:()=>t.refetchQueries(u,...i),cancel:()=>t.cancelQuery(u,...i),setData:()=>{t.setQueryData(u,i[0],i[1])},setQueriesData:()=>t.setQueriesData(u,i[0],i[1],i[2]),setInfiniteData:()=>{t.setInfiniteQueryData(u,i[0],i[1])},getData:()=>t.getQueryData(u),getInfiniteData:()=>t.getInfiniteQueryData(u),setMutationDefaults:()=>t.setMutationDefaults(a(r),s),getMutationDefaults:()=>t.getMutationDefaults(a(r)),isMutating:()=>t.isMutating({mutationKey:a(r)})})[n]()}));return(0,s.U6)(t=>"client"===t?r:l.includes(t)?e[t]:i[t])})(t),[t])}:e.hasOwnProperty(r)?e[r]:t[r])}(function(e){let t=e?.overrides?.useMutation?.onSuccess??(e=>e.originalFn()),r=e?.context??c,l=n.XT;function f(){let e=u.useContext(r);if(!e)throw Error("Unable to find tRPC Context. Did you forget to wrap your App inside `withTRPC` HoC?");return e}function q(e,t){let{queryClient:r,ssrState:n}=f();return n&&"mounted"!==n&&r.getQueryCache().find({queryKey:e})?.state.status==="error"?{retryOnMount:!1,...t}:t}let D={data:void 0,error:null,status:"idle"},_={data:void 0,error:null,status:"connecting"};return{Provider:e=>{let{abortOnUnmount:t=!1,queryClient:o,ssrContext:a}=e,[l,c]=u.useState(e.ssrState??!1),f=e.client instanceof n.Ke?e.client:(0,n.n2)(e.client),p=u.useMemo(()=>(function(e){let{client:t,queryClient:r}=e,o=t instanceof n.Ke?t:(0,n.n2)(t);return{infiniteQueryOptions:(e,t,r)=>{let n=t[1]?.input===i.hT,s=async e=>{let n={...r,trpc:{...r?.trpc,...r?.trpc?.abortOnUnmount?{signal:e.signal}:{signal:null}}};return await o.query(...v(t,n,{direction:e.direction,pageParam:e.pageParam}))};return Object.assign({...r,initialData:r?.initialData,queryKey:t,queryFn:n?i.hT:s,initialPageParam:r?.initialCursor??null},{trpc:E({path:e})})},queryOptions:(e,t,n)=>{let a=t[1]?.input===i.hT,u=async e=>{let i={...n,trpc:{...n?.trpc,...n?.trpc?.abortOnUnmount?{signal:e.signal}:{signal:null}}},a=await o.query(...v(t,i));return(0,s.Td)(a)?S(a,r,t):a};return Object.assign({...n,initialData:n?.initialData,queryKey:t,queryFn:a?i.hT:u},{trpc:E({path:e})})},fetchQuery:(e,t)=>r.fetchQuery({...t,queryKey:e,queryFn:()=>o.query(...v(e,t))}),fetchInfiniteQuery:(e,t)=>r.fetchInfiniteQuery({...t,queryKey:e,queryFn:({pageParam:r,direction:n})=>o.query(...v(e,t,{pageParam:r,direction:n})),initialPageParam:t?.initialCursor??null}),prefetchQuery:(e,t)=>r.prefetchQuery({...t,queryKey:e,queryFn:()=>o.query(...v(e,t))}),prefetchInfiniteQuery:(e,t)=>r.prefetchInfiniteQuery({...t,queryKey:e,queryFn:({pageParam:r,direction:n})=>o.query(...v(e,t,{pageParam:r,direction:n})),initialPageParam:t?.initialCursor??null}),ensureQueryData:(e,t)=>r.ensureQueryData({...t,queryKey:e,queryFn:()=>o.query(...v(e,t))}),invalidateQueries:(e,t,n)=>r.invalidateQueries({...t,queryKey:e},n),resetQueries:(e,t,n)=>r.resetQueries({...t,queryKey:e},n),refetchQueries:(e,t,n)=>r.refetchQueries({...t,queryKey:e},n),cancelQuery:(e,t)=>r.cancelQueries({queryKey:e},t),setQueryData:(e,t,n)=>r.setQueryData(e,t,n),setQueriesData:(e,t,n,i)=>r.setQueriesData({...t,queryKey:e},n,i),getQueryData:e=>r.getQueryData(e),setInfiniteQueryData:(e,t,n)=>r.setQueryData(e,t,n),getInfiniteQueryData:e=>r.getQueryData(e),setMutationDefaults:(t,n)=>{let i=t[0];return r.setMutationDefaults(t,"function"==typeof n?n({canonicalMutationFn:t=>o.mutation(...v([i,{input:t}],e))}):n)},getMutationDefaults:e=>r.getMutationDefaults(e),isMutating:e=>r.isMutating({...e,exact:!0})}})({client:f,queryClient:o}),[f,o]),d=u.useMemo(()=>({abortOnUnmount:t,queryClient:o,client:f,ssrContext:a??null,ssrState:l,...p}),[t,f,p,o,a,l]);return u.useEffect(()=>{c(e=>!!e&&"mounted")},[]),u.createElement(r.Provider,{value:d},e.children)},createClient:l,useContext:f,useUtils:f,useQuery:function(t,r,n){let{abortOnUnmount:a,client:u,ssrState:l,queryClient:c,prefetchQuery:d}=f(),h=o(t,r,"query"),y=c.getQueryDefaults(h),m=r===i.hT;"undefined"!=typeof window||"prepass"!==l||n?.trpc?.ssr===!1||(n?.enabled??y?.enabled)===!1||m||c.getQueryCache().find({queryKey:h})||d(h,n);let g=q(h,{...y,...n}),b=n?.trpc?.abortOnUnmount??e?.abortOnUnmount??a,w=(0,p.useQuery)({...g,queryKey:h,queryFn:m?r:async e=>{let t={...g,trpc:{...g?.trpc,...b?{signal:e.signal}:{signal:null}}},r=await u.query(...v(h,t));return(0,s.Td)(r)?S(r,c,h):r}},c);return w.trpc=O({path:t}),w},usePrefetchQuery:function(t,r,n){let s=f(),a=o(t,r,"query"),u=r===i.hT,l=n?.trpc?.abortOnUnmount??e?.abortOnUnmount??s.abortOnUnmount;!function(e,t){let r=(0,d.useQueryClient)(void 0);r.getQueryState(e.queryKey)||r.prefetchQuery(e)}({...n,queryKey:a,queryFn:u?r:e=>{let t={trpc:{...n?.trpc,...l?{signal:e.signal}:{}}};return s.client.query(...v(a,t))}})},useSuspenseQuery:function(t,r,n){let i=f(),s=o(t,r,"query"),a=n?.trpc?.abortOnUnmount??e?.abortOnUnmount??i.abortOnUnmount,u=(0,h.useSuspenseQuery)({...n,queryKey:s,queryFn:e=>{let t={...n,trpc:{...n?.trpc,...a?{signal:e.signal}:{signal:null}}};return i.client.query(...v(s,t))}},i.queryClient);return u.trpc=O({path:t}),[u.data,u]},useQueries:(e,t)=>{let{ssrState:r,queryClient:n,prefetchQuery:i,client:s}=f(),o=e(T(s));if("undefined"==typeof window&&"prepass"===r)for(let e of o)e.trpc?.ssr===!1||n.getQueryCache().find({queryKey:e.queryKey})||i(e.queryKey,e);return(0,b.useQueries)({queries:o.map(e=>({...e,queryKey:e.queryKey})),combine:t?.combine},n)},useSuspenseQueries:e=>{let{queryClient:t,client:r}=f(),n=e(T(r)),i=(0,w.useSuspenseQueries)({queries:n.map(e=>({...e,queryFn:e.queryFn,queryKey:e.queryKey}))},t);return[i.map(e=>e.data),i]},useMutation:function(e,r){let{client:n,queryClient:i}=f(),s=a(e),o=i.defaultMutationOptions(i.getMutationDefaults(s)),u=(0,y.useMutation)({...r,mutationKey:s,mutationFn:t=>n.mutation(...v([e,{input:t}],r)),onSuccess:(...e)=>t({originalFn:()=>r?.onSuccess?.(...e)??o?.onSuccess?.(...e),queryClient:i,meta:r?.meta??o?.meta??{}})},i);return u.trpc=O({path:e}),u},useSubscription:function(e,t,r){let n=r?.enabled??t!==i.hT,s=(0,i.EN)(o(e,t,"any")),{client:a}=f(),l=u.useRef(r);u.useEffect(()=>{l.current=r});let[c]=u.useState(new Set([])),p=u.useCallback(e=>{c.add(e)},[c]),d=u.useRef(null),h=u.useCallback(e=>{let t=m.current,r=m.current=e(t),n=!1;for(let e of c)if(t[e]!==r[e]){n=!0;break}n&&b(P(r,p))},[p,c]),y=u.useCallback(()=>{if(d.current?.unsubscribe(),!n)return void h(()=>({...D,reset:y}));h(()=>({..._,reset:y})),d.current=a.subscription(e.join("."),t??void 0,{onStarted:()=>{l.current.onStarted?.(),h(e=>({...e,status:"pending",error:null}))},onData:e=>{l.current.onData?.(e),h(t=>({...t,status:"pending",data:e,error:null}))},onError:e=>{l.current.onError?.(e),h(t=>({...t,status:"error",error:e}))},onConnectionStateChange:e=>{h(t=>{switch(e.state){case"idle":return{...t,status:e.state,error:null,data:void 0};case"connecting":return{...t,error:e.error,status:e.state};case"pending":return t}})},onComplete:()=>{l.current.onComplete?.(),h(e=>({...e,status:"idle",error:null,data:void 0}))}})},[a,s,n,h]);u.useEffect(()=>(y(),()=>{d.current?.unsubscribe()}),[y]);let m=u.useRef(n?{..._,reset:y}:{...D,reset:y}),[g,b]=u.useState(P(m.current,p));return g},useInfiniteQuery:function(e,t,r){let{client:n,ssrState:s,prefetchInfiniteQuery:a,queryClient:u,abortOnUnmount:l}=f(),c=o(e,t,"infinite"),p=u.getQueryDefaults(c),d=t===i.hT;"undefined"!=typeof window||"prepass"!==s||r?.trpc?.ssr===!1||(r?.enabled??p?.enabled)===!1||d||u.getQueryCache().find({queryKey:c})||a(c,{...p,...r});let h=q(c,{...p,...r}),y=r?.trpc?.abortOnUnmount??l,g=(0,m.useInfiniteQuery)({...h,initialPageParam:r.initialCursor??null,persister:r.persister,queryKey:c,queryFn:d?t:e=>{let t={...h,trpc:{...h?.trpc,...y?{signal:e.signal}:{signal:null}}};return n.query(...v(c,t,{pageParam:e.pageParam??r.initialCursor,direction:e.direction}))}},u);return g.trpc=O({path:e}),g},usePrefetchInfiniteQuery:function(e,t,r){let n=f(),s=o(e,t,"infinite"),a=n.queryClient.getQueryDefaults(s),u=t===i.hT,l=q(s,{...a,...r}),c=r?.trpc?.abortOnUnmount??n.abortOnUnmount;!function(e,t){let r=(0,d.useQueryClient)(void 0);r.getQueryState(e.queryKey)||r.prefetchInfiniteQuery(e)}({...r,initialPageParam:r.initialCursor??null,queryKey:s,queryFn:u?t:e=>{let t={...l,trpc:{...l?.trpc,...c?{signal:e.signal}:{}}};return n.client.query(...v(s,t,{pageParam:e.pageParam??r.initialCursor,direction:e.direction}))}})},useSuspenseInfiniteQuery:function(e,t,r){let n=f(),i=o(e,t,"infinite"),s=n.queryClient.getQueryDefaults(i),a=q(i,{...s,...r}),u=r?.trpc?.abortOnUnmount??n.abortOnUnmount,l=(0,g.useSuspenseInfiniteQuery)({...r,initialPageParam:r.initialCursor??null,queryKey:i,queryFn:e=>{let t={...a,trpc:{...a?.trpc,...u?{signal:e.signal}:{}}};return n.client.query(...v(i,t,{pageParam:e.pageParam??r.initialCursor,direction:e.direction}))}},n.queryClient);return l.trpc=O({path:e}),[l.data,l]}}}(e))}},9177:(e,t,r)=>{var n,i,s,o,a;r.d(t,{Ay:()=>ee});class u{constructor(){this.keyToValue=new Map,this.valueToKey=new Map}set(e,t){this.keyToValue.set(e,t),this.valueToKey.set(t,e)}getByKey(e){return this.keyToValue.get(e)}getByValue(e){return this.valueToKey.get(e)}clear(){this.keyToValue.clear(),this.valueToKey.clear()}}class l{constructor(e){this.generateIdentifier=e,this.kv=new u}register(e,t){this.kv.getByValue(e)||(t||(t=this.generateIdentifier(e)),this.kv.set(t,e))}clear(){this.kv.clear()}getIdentifier(e){return this.kv.getByValue(e)}getValue(e){return this.kv.getByKey(e)}}class c extends l{constructor(){super(e=>e.name),this.classToAllowedProps=new Map}register(e,t){"object"==typeof t?(t.allowProps&&this.classToAllowedProps.set(e,t.allowProps),super.register(e,t.identifier)):super.register(e,t)}getAllowedProps(e){return this.classToAllowedProps.get(e)}}function f(e,t){Object.entries(e).forEach(([e,r])=>t(r,e))}function p(e,t){return -1!==e.indexOf(t)}function d(e,t){for(let r=0;r<e.length;r++){let n=e[r];if(t(n))return n}}class h{constructor(){this.transfomers={}}register(e){this.transfomers[e.name]=e}findApplicable(e){return function(e,t){let r=function(e){if("values"in Object)return Object.values(e);let t=[];for(let r in e)e.hasOwnProperty(r)&&t.push(e[r]);return t}(e);if("find"in r)return r.find(t);for(let e=0;e<r.length;e++){let n=r[e];if(t(n))return n}}(this.transfomers,t=>t.isApplicable(e))}findByName(e){return this.transfomers[e]}}let y=e=>Object.prototype.toString.call(e).slice(8,-1),m=e=>void 0===e,g=e=>null===e,b=e=>"object"==typeof e&&null!==e&&e!==Object.prototype&&(null===Object.getPrototypeOf(e)||Object.getPrototypeOf(e)===Object.prototype),w=e=>b(e)&&0===Object.keys(e).length,v=e=>Array.isArray(e),E=e=>"string"==typeof e,O=e=>"number"==typeof e&&!isNaN(e),S=e=>"boolean"==typeof e,T=e=>e instanceof Map,P=e=>e instanceof Set,q=e=>"Symbol"===y(e),D=e=>"number"==typeof e&&isNaN(e),_=e=>S(e)||g(e)||m(e)||O(e)||E(e)||q(e),Q=e=>e===1/0||e===-1/0,R=e=>e.replace(/\./g,"\\."),A=e=>e.map(String).map(R).join("."),C=e=>{let t=[],r="";for(let n=0;n<e.length;n++){let i=e.charAt(n);if("\\"===i&&"."===e.charAt(n+1)){r+=".",n++;continue}if("."===i){t.push(r),r="";continue}r+=i}let n=r;return t.push(n),t};function I(e,t,r,n){return{isApplicable:e,annotation:t,transform:r,untransform:n}}let k=[I(m,"undefined",()=>null,()=>void 0),I(e=>"bigint"==typeof e,"bigint",e=>e.toString(),e=>"undefined"!=typeof BigInt?BigInt(e):(console.error("Please add a BigInt polyfill."),e)),I(e=>e instanceof Date&&!isNaN(e.valueOf()),"Date",e=>e.toISOString(),e=>new Date(e)),I(e=>e instanceof Error,"Error",(e,t)=>{let r={name:e.name,message:e.message};return t.allowedErrorProps.forEach(t=>{r[t]=e[t]}),r},(e,t)=>{let r=Error(e.message);return r.name=e.name,r.stack=e.stack,t.allowedErrorProps.forEach(t=>{r[t]=e[t]}),r}),I(e=>e instanceof RegExp,"regexp",e=>""+e,e=>new RegExp(e.slice(1,e.lastIndexOf("/")),e.slice(e.lastIndexOf("/")+1))),I(P,"set",e=>[...e.values()],e=>new Set(e)),I(T,"map",e=>[...e.entries()],e=>new Map(e)),I(e=>D(e)||Q(e),"number",e=>D(e)?"NaN":e>0?"Infinity":"-Infinity",Number),I(e=>0===e&&1/e==-1/0,"number",()=>"-0",Number),I(e=>e instanceof URL,"URL",e=>e.toString(),e=>new URL(e))];function j(e,t,r,n){return{isApplicable:e,annotation:t,transform:r,untransform:n}}let x=j((e,t)=>!!q(e)&&!!t.symbolRegistry.getIdentifier(e),(e,t)=>["symbol",t.symbolRegistry.getIdentifier(e)],e=>e.description,(e,t,r)=>{let n=r.symbolRegistry.getValue(t[1]);if(!n)throw Error("Trying to deserialize unknown symbol");return n}),N=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,Uint8ClampedArray].reduce((e,t)=>(e[t.name]=t,e),{}),M=j(e=>ArrayBuffer.isView(e)&&!(e instanceof DataView),e=>["typed-array",e.constructor.name],e=>[...e],(e,t)=>{let r=N[t[1]];if(!r)throw Error("Trying to deserialize unknown typed array");return new r(e)});function U(e,t){return!!e?.constructor&&!!t.classRegistry.getIdentifier(e.constructor)}let F=j(U,(e,t)=>["class",t.classRegistry.getIdentifier(e.constructor)],(e,t)=>{let r=t.classRegistry.getAllowedProps(e.constructor);if(!r)return{...e};let n={};return r.forEach(t=>{n[t]=e[t]}),n},(e,t,r)=>{let n=r.classRegistry.getValue(t[1]);if(!n)throw Error(`Trying to deserialize unknown class '${t[1]}' - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564`);return Object.assign(Object.create(n.prototype),e)}),L=j((e,t)=>!!t.customTransformerRegistry.findApplicable(e),(e,t)=>["custom",t.customTransformerRegistry.findApplicable(e).name],(e,t)=>t.customTransformerRegistry.findApplicable(e).serialize(e),(e,t,r)=>{let n=r.customTransformerRegistry.findByName(t[1]);if(!n)throw Error("Trying to deserialize unknown custom value");return n.deserialize(e)}),G=[F,x,L,M],z=(e,t)=>{let r=d(G,r=>r.isApplicable(e,t));if(r)return{value:r.transform(e,t),type:r.annotation(e,t)};let n=d(k,r=>r.isApplicable(e,t));if(n)return{value:n.transform(e,t),type:n.annotation}},K={};k.forEach(e=>{K[e.annotation]=e});let V=(e,t,r)=>{if(v(t))switch(t[0]){case"symbol":return x.untransform(e,t,r);case"class":return F.untransform(e,t,r);case"custom":return L.untransform(e,t,r);case"typed-array":return M.untransform(e,t,r);default:throw Error("Unknown transformation: "+t)}{let n=K[t];if(!n)throw Error("Unknown transformation: "+t);return n.untransform(e,r)}},B=(e,t)=>{if(t>e.size)throw Error("index out of bounds");let r=e.keys();for(;t>0;)r.next(),t--;return r.next().value};function $(e){if(p(e,"__proto__"))throw Error("__proto__ is not allowed as a property");if(p(e,"prototype"))throw Error("prototype is not allowed as a property");if(p(e,"constructor"))throw Error("constructor is not allowed as a property")}let H=(e,t)=>{$(t);for(let r=0;r<t.length;r++){let n=t[r];if(P(e))e=B(e,+n);else if(T(e)){let i=+n,s=0==+t[++r]?"key":"value",o=B(e,i);switch(s){case"key":e=o;break;case"value":e=e.get(o)}}else e=e[n]}return e},W=(e,t,r)=>{if($(t),0===t.length)return r(e);let n=e;for(let e=0;e<t.length-1;e++){let r=t[e];if(v(n))n=n[+r];else if(b(n))n=n[r];else if(P(n))n=B(n,+r);else if(T(n)){if(e===t.length-2)break;let i=+r,s=0==+t[++e]?"key":"value",o=B(n,i);switch(s){case"key":n=o;break;case"value":n=n.get(o)}}}let i=t[t.length-1];if(v(n)?n[+i]=r(n[+i]):b(n)&&(n[i]=r(n[i])),P(n)){let e=B(n,+i),t=r(e);e!==t&&(n.delete(e),n.add(t))}if(T(n)){let e=B(n,+t[t.length-2]);switch(0==+i?"key":"value"){case"key":{let t=r(e);n.set(t,n.get(e)),t!==e&&n.delete(e);break}case"value":n.set(e,r(n.get(e)))}}return e},Y=(e,t)=>b(e)||v(e)||T(e)||P(e)||U(e,t),J=(e,t,r,n,i=[],s=[],o=new Map)=>{let a=_(e);if(!a){!function(e,t,r){let n=r.get(e);n?n.push(t):r.set(e,[t])}(e,i,t);let r=o.get(e);if(r)return n?{transformedValue:null}:r}if(!Y(e,r)){let t=z(e,r),n=t?{transformedValue:t.value,annotations:[t.type]}:{transformedValue:e};return a||o.set(e,n),n}if(p(s,e))return{transformedValue:null};let u=z(e,r),l=u?.value??e,c=v(l)?[]:{},d={};f(l,(a,u)=>{if("__proto__"===u||"constructor"===u||"prototype"===u)throw Error(`Detected property ${u}. This is a prototype pollution risk, please remove it from your object.`);let l=J(a,t,r,n,[...i,u],[...s,e],o);c[u]=l.transformedValue,v(l.annotations)?d[u]=l.annotations:b(l.annotations)&&f(l.annotations,(e,t)=>{d[R(u)+"."+t]=e})});let h=w(d)?{transformedValue:c,annotations:u?[u.type]:void 0}:{transformedValue:c,annotations:u?[u.type,d]:d};return a||o.set(e,h),h};function X(e){return Object.prototype.toString.call(e).slice(8,-1)}function Z(e){return"Array"===X(e)}n=function(e){return"Null"===X(e)},i=function(e){return"Undefined"===X(e)};class ee{constructor({dedupe:e=!1}={}){this.classRegistry=new c,this.symbolRegistry=new l(e=>e.description??""),this.customTransformerRegistry=new h,this.allowedErrorProps=[],this.dedupe=e}serialize(e){let t=new Map,r=J(e,t,this,this.dedupe),n={json:r.transformedValue};r.annotations&&(n.meta={...n.meta,values:r.annotations});let i=function(e,t){let r,n={};return(e.forEach(e=>{if(e.length<=1)return;t||(e=e.map(e=>e.map(String)).sort((e,t)=>e.length-t.length));let[i,...s]=e;0===i.length?r=s.map(A):n[A(i)]=s.map(A)}),r)?w(n)?[r]:[r,n]:w(n)?void 0:n}(t,this.dedupe);return i&&(n.meta={...n.meta,referentialEqualities:i}),n}deserialize(e){var t,r,n;let{json:i,meta:s}=e,o=function e(t,r={}){return Z(t)?t.map(t=>e(t,r)):!function(e){if("Object"!==X(e))return!1;let t=Object.getPrototypeOf(e);return!!t&&t.constructor===Object&&t===Object.prototype}(t)?t:[...Object.getOwnPropertyNames(t),...Object.getOwnPropertySymbols(t)].reduce((n,i)=>{if(Z(r.props)&&!r.props.includes(i))return n;let s=e(t[i],r);var o=r.nonenumerable;let a=({}).propertyIsEnumerable.call(t,i)?"enumerable":"nonenumerable";return"enumerable"===a&&(n[i]=s),o&&"nonenumerable"===a&&Object.defineProperty(n,i,{value:s,enumerable:!1,writable:!0,configurable:!0}),n},{})}(i);return s?.values&&(t=o,r=s.values,n=this,function e(t,r,n=[]){if(!t)return;if(!v(t))return void f(t,(t,i)=>e(t,r,[...n,...C(i)]));let[i,s]=t;s&&f(s,(t,i)=>{e(t,r,[...n,...C(i)])}),r(i,n)}(r,(e,r)=>{t=W(t,r,t=>V(t,e,n))}),o=t),s?.referentialEqualities&&(o=function(e,t){function r(t,r){let n=H(e,C(r));t.map(C).forEach(t=>{e=W(e,t,()=>n)})}if(v(t)){let[n,i]=t;n.forEach(t=>{e=W(e,C(t),()=>e)}),i&&f(i,r)}else f(t,r);return e}(o,s.referentialEqualities)),o}stringify(e){return JSON.stringify(this.serialize(e))}parse(e){return this.deserialize(JSON.parse(e))}registerClass(e,t){this.classRegistry.register(e,t)}registerSymbol(e,t){this.symbolRegistry.register(e,t)}registerCustom(e,t){this.customTransformerRegistry.register({name:t,...e})}allowErrorProps(...e){this.allowedErrorProps.push(...e)}}ee.defaultInstance=new ee,ee.serialize=ee.defaultInstance.serialize.bind(ee.defaultInstance),ee.deserialize=ee.defaultInstance.deserialize.bind(ee.defaultInstance),ee.stringify=ee.defaultInstance.stringify.bind(ee.defaultInstance),ee.parse=ee.defaultInstance.parse.bind(ee.defaultInstance),ee.registerClass=ee.defaultInstance.registerClass.bind(ee.defaultInstance),ee.registerSymbol=ee.defaultInstance.registerSymbol.bind(ee.defaultInstance),ee.registerCustom=ee.defaultInstance.registerCustom.bind(ee.defaultInstance),ee.allowErrorProps=ee.defaultInstance.allowErrorProps.bind(ee.defaultInstance),ee.serialize,ee.deserialize,ee.stringify,ee.parse,ee.registerClass,ee.registerCustom,ee.registerSymbol,ee.allowErrorProps},9343:(e,t,r)=>{r.d(t,{U6:()=>o,vX:()=>s,Td:()=>a.Td,Gv:()=>a.Gv,le:()=>R});let n=()=>{},i=e=>{Object.freeze&&Object.freeze(e)},s=e=>(function e(t,r,s){let o=r.join(".");return s[o]??(s[o]=new Proxy(n,{get(n,i){if("string"==typeof i&&"then"!==i)return e(t,[...r,i],s)},apply(e,n,s){let o=r[r.length-1],a={args:s,path:r};return"call"===o?a={args:s.length>=2?[s[1]]:[],path:r.slice(0,-1)}:"apply"===o&&(a={args:s.length>=2?s[1]:[],path:r.slice(0,-1)}),i(a.args),i(a.path),t(a)}})),s[o]})(e,[],Object.create(null)),o=e=>new Proxy(n,{get(t,r){if("then"!==r)return e(r)}});r(6090);var a=r(2927);let u={PARSE_ERROR:400,BAD_REQUEST:400,UNAUTHORIZED:401,FORBIDDEN:403,NOT_FOUND:404,METHOD_NOT_SUPPORTED:405,TIMEOUT:408,CONFLICT:409,PRECONDITION_FAILED:412,PAYLOAD_TOO_LARGE:413,UNSUPPORTED_MEDIA_TYPE:415,UNPROCESSABLE_CONTENT:422,TOO_MANY_REQUESTS:429,CLIENT_CLOSED_REQUEST:499,INTERNAL_SERVER_ERROR:500,NOT_IMPLEMENTED:501,BAD_GATEWAY:502,SERVICE_UNAVAILABLE:503,GATEWAY_TIMEOUT:504};function l(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class c extends Error{}function f(e){if(e instanceof p||e instanceof Error&&"TRPCError"===e.name)return e;let t=new p({code:"INTERNAL_SERVER_ERROR",cause:e});return e instanceof Error&&e.stack&&(t.stack=e.stack),t}class p extends Error{constructor(e){let t=function(e){if(e instanceof Error)return e;let t=typeof e;if("undefined"!==t&&"function"!==t&&null!==e){if("object"!==t)return Error(String(e));if((0,a.Gv)(e)){let t=new c;for(let r in e)t[r]=e[r];return t}}}(e.cause);super(e.message??t?.message??e.code,{cause:t}),l(this,"cause",void 0),l(this,"code",void 0),this.code=e.code,this.name="TRPCError",this.cause||(this.cause=t)}}let d=({shape:e})=>e,h={input:{serialize:e=>e,deserialize:e=>e},output:{serialize:e=>e,deserialize:e=>e}},y=Symbol("lazy"),m={_ctx:null,_errorShape:null,_meta:null,queries:{},mutations:{},subscriptions:{},errorFormatter:d,transformer:h},g=["then","call","apply"];function b(e){return function(t){let r=new Set(Object.keys(t).filter(e=>g.includes(e)));if(r.size>0)throw Error("Reserved words used in `router({})` call: "+Array.from(r).join(", "));let n=(0,a.QQ)({}),i=(0,a.QQ)({}),s=function e(t,r=[]){let s=(0,a.QQ)({});for(let[u,l]of Object.entries(t??{})){var o;if("function"==typeof l&&y in l){i[[...r,u].join(".")]=function t(r){return{ref:r.ref,load:function(e){let t=Symbol(),r=t;return()=>(r===t&&(r=e()),r)}(async()=>{let n=await r.ref(),s=[...r.path,r.key],o=s.join(".");for(let[a,u]of(r.aggregate[r.key]=e(n._def.record,s),delete i[o],Object.entries(n._def.lazy)))i[[...s,a].join(".")]=t({ref:u.ref,path:s,key:a,aggregate:r.aggregate[r.key]})})}}({path:r,ref:l,key:u,aggregate:s});continue}if(o=l,(0,a.Gv)(o)&&(0,a.Gv)(o._def)&&"router"in o._def){s[u]=e(l._def.record,[...r,u]);continue}if(!function(e){return"function"==typeof e}(l)){s[u]=e(l,[...r,u]);continue}let t=[...r,u].join(".");if(n[t])throw Error(`Duplicate key: ${t}`);n[t]=l,s[u]=l}return s}(t),o={_config:e,router:!0,procedures:n,lazy:i,...m,record:s};return{...s,_def:o,createCaller:v()({_def:o})}}}async function w(e,t){let{_def:r}=e,n=r.procedures[t];for(;!n;){let e=Object.keys(r.lazy).find(e=>t.startsWith(e));if(!e)return null;let i=r.lazy[e];await i.load(),n=r.procedures[t]}return n}function v(){return function(e){let{_def:t}=e;return function(r,n){return s(async({path:i,args:s})=>{let o,u=i.join(".");if(1===i.length&&"_def"===i[0])return t;let l=await w(e,u);try{if(!l)throw new p({code:"NOT_FOUND",message:`No procedure found on path "${i}"`});return o=(0,a.Tn)(r)?await Promise.resolve(r()):r,await l({path:u,getRawInput:async()=>s[0],ctx:o,type:l._def.type,signal:n?.signal})}catch(e){throw n?.onError?.({ctx:o,error:f(e),input:s[0],path:u,type:l?._def.type??"unknown"}),e}})}}}function E(...e){let t=(0,a.uf)({},...e.map(e=>e._def.record));return b({errorFormatter:e.reduce((e,t)=>{if(t._def._config.errorFormatter&&t._def._config.errorFormatter!==d){if(e!==d&&e!==t._def._config.errorFormatter)throw Error("You seem to have several error formatters");return t._def._config.errorFormatter}return e},d),transformer:e.reduce((e,t)=>{if(t._def._config.transformer&&t._def._config.transformer!==h){if(e!==h&&e!==t._def._config.transformer)throw Error("You seem to have several transformers");return t._def._config.transformer}return e},h),isDev:e.every(e=>e._def._config.isDev),allowOutsideOfServer:e.every(e=>e._def._config.allowOutsideOfServer),isServer:e.every(e=>e._def._config.isServer),$types:e[0]?._def._config.$types})(t)}function O(e){let t=null,r=a.IT;return{read:async()=>(r!==a.IT||(null===t&&(t=e().catch(e=>{if(e instanceof p)throw e;throw new p({code:"BAD_REQUEST",message:e instanceof Error?e.message:"Invalid input",cause:e})})),r=await t,t=null),r),result:()=>r!==a.IT?r:void 0}}r(503);var S=r(3223);Symbol();function T(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function P(e,t,r){if(null!=t){var n,i;if("object"!=typeof t&&"function"!=typeof t)throw TypeError("Object expected.");if(r){if(!Symbol.asyncDispose)throw TypeError("Symbol.asyncDispose is not defined.");n=t[Symbol.asyncDispose]}if(void 0===n){if(!Symbol.dispose)throw TypeError("Symbol.dispose is not defined.");n=t[Symbol.dispose],r&&(i=n)}if("function"!=typeof n)throw TypeError("Object not disposable.");i&&(n=function(){try{i.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:t,dispose:n,async:r})}else r&&e.stack.push({async:!0});return t}function q(e){var t="function"==typeof SuppressedError?SuppressedError:function(e,t,r){var n=Error(r);return n.name="SuppressedError",n.error=e,n.suppressed=t,n};return(q=function(e){function r(r){e.error=e.hasError?new t(r,e.error,"An error was suppressed during disposal."):r,e.hasError=!0}var n,i=0;return function t(){for(;n=e.stack.pop();)try{if(!n.async&&1===i)return i=0,e.stack.push(n),Promise.resolve().then(t);if(n.dispose){var s=n.dispose.call(n.value);if(n.async)return i|=2,Promise.resolve(s).then(t,function(e){return r(e),t()})}else i|=1}catch(e){r(e)}if(1===i)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()})(e)}Symbol("ping");class D extends Error{constructor(e){super("Max depth reached at path: "+e.join(".")),T(this,"path",void 0),this.path=e}}class _ extends Error{constructor(e){super("Received error from server"),T(this,"data",void 0),this.data=e}}let Q=e=>({getReader:()=>new ReadableStream({start(t){e.on("data",e=>{t.enqueue(e)}),e.on("end",()=>{t.close()}),e.on("error",e=>{t.error(e)})}}).getReader()});async function R(e){let t,r,{deserialize:n=e=>e}=e,i=function(e){let t=function(e){let t="getReader"in e?e.getReader():Q(e).getReader(),r="";return new ReadableStream({async pull(e){let{done:r,value:n}=await t.read();r?e.close():e.enqueue(n)},cancel:()=>t.cancel()}).pipeThrough(new TextDecoderStream).pipeThrough(new TransformStream({transform(e,t){let n=(r+=e).split("\n");for(let e of(r=n.pop()??"",n))t.enqueue(e)}}))}(e),r=!1;return t.pipeThrough(new TransformStream({transform(e,t){if(r){let r=JSON.parse(e);t.enqueue(r)}else{let n=JSON.parse(e);t.enqueue(n),r=!0}}}))}(e.from);n&&(i=i.pipeThrough(new TransformStream({transform(e,t){t.enqueue(n(e))}})));let s={promise:new Promise((e,n)=>{t=e,r=n}),resolve:t,reject:r},o=function(e){let t=new Map;function r(){return Array.from(t.values()).every(e=>e.closed)}return{getOrCreate:function(n){let i=t.get(n);return i||(i=function(){let t,n=new ReadableStream({start(e){t=e}}),i={enqueue:e=>t.enqueue(e),close:()=>{t.close(),s(),r()&&e.abort()},closed:!1,getReaderResource:()=>{let e=n.getReader();return(0,S.T)(e,()=>{e.releaseLock(),i.close()})},error:e=>{t.error(e),s()}};function s(){Object.assign(i,{closed:!0,close:()=>{},enqueue:()=>{},getReaderResource:null,error:()=>{}})}return i}(),t.set(n,i)),i},isEmpty:r,cancelAll:function(e){for(let r of t.values())r.error(e)}}}(e.abortController),u=e=>{s?.reject(e),o.cancelAll(e)};return i.pipeTo(new WritableStream({write(t){if(s){for(let[r,n]of Object.entries(t)){let i=function t(r){let[[n],...i]=r;for(let r of i){let[i]=r,s=function(r){let[n,i,s]=r,u=o.getOrCreate(s);switch(i){case 0:return(0,a.eF)(async()=>{let r={stack:[],error:void 0,hasError:!1};try{let n=P(r,u.getReaderResource(),!1),{value:i}=await n.read(),[s,o,a]=i;switch(o){case 0:return t(a);case 1:throw e.formatError?.({error:a})??new _(a)}}catch(e){r.error=e,r.hasError=!0}finally{q(r)}});case 1:return(0,a.eF)(async function*(){let r={stack:[],error:void 0,hasError:!1};try{let n=P(r,u.getReaderResource(),!1);for(;;){let{value:r}=await n.read(),[i,s,o]=r;switch(s){case 1:yield t(o);break;case 0:return t(o);case 2:throw e.formatError?.({error:o})??new _(o)}}}catch(e){r.error=e,r.hasError=!0}finally{q(r)}})}}(r);if(null===i)return s;n[i]=s}return n}(n);t[r]=i}s.resolve(t),s=null;return}let[r]=t;o.getOrCreate(r).enqueue(t)},close:()=>u(Error("Stream closed")),abort:u}),{signal:e.abortController.signal}).catch(t=>{e.onError?.({error:t}),u(t)}),[await s.promise,o]}Symbol();function A(e){var t="function"==typeof SuppressedError?SuppressedError:function(e,t,r){var n=Error(r);return n.name="SuppressedError",n.error=e,n.suppressed=t,n};return(A=function(e){function r(r){e.error=e.hasError?new t(r,e.error,"An error was suppressed during disposal."):r,e.hasError=!0}var n,i=0;return function t(){for(;n=e.stack.pop();)try{if(!n.async&&1===i)return i=0,e.stack.push(n),Promise.resolve().then(t);if(n.dispose){var s=n.dispose.call(n.value);if(n.async)return i|=2,Promise.resolve(s).then(t,function(e){return r(e),t()})}else i|=1}catch(e){r(e)}if(1===i)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()})(e)}let C="middlewareMarker";class I extends Error{constructor(e){super(e[0]?.message),function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(this,"issues",void 0),this.name="SchemaError",this.issues=e}}function k(e){let t="~standard"in e;if("function"==typeof e&&"function"==typeof e.assert)return e.assert.bind(e);if("function"==typeof e&&!t)return e;if("function"==typeof e.parseAsync)return e.parseAsync.bind(e);if("function"==typeof e.parse)return e.parse.bind(e);if("function"==typeof e.validateSync)return e.validateSync.bind(e);if("function"==typeof e.create)return e.create.bind(e);if("function"==typeof e.assert)return t=>(e.assert(t),t);if(t)return async t=>{let r=await e["~standard"].validate(t);if(r.issues)throw new I(r.issues);return r.value};throw Error("Could not find a validator fn")}function j(e,t){let{middlewares:r=[],inputs:n,meta:i,...s}=t;return x({...(0,a.uf)(e,s),inputs:[...e.inputs,...n??[]],middlewares:[...e.middlewares,...r],meta:e.meta&&i?{...e.meta,...i}:i??e.meta})}function x(e={}){let t={procedure:!0,inputs:[],middlewares:[],...e};return{_def:t,input(e){let r=k(e);return j(t,{inputs:[e],middlewares:[function(e){let t=async function(t){let r,n=await t.getRawInput();try{r=await e(n)}catch(e){throw new p({code:"BAD_REQUEST",cause:e})}let i=(0,a.Gv)(t.input)&&(0,a.Gv)(r)?{...t.input,...r}:r;return t.next({input:i})};return t._type="input",t}(r)]})},output(e){let r=k(e);return j(t,{output:e,middlewares:[function(e){let t=async function({next:t}){let r=await t();if(!r.ok)return r;try{let t=await e(r.data);return{...r,data:t}}catch(e){throw new p({message:"Output validation failed",code:"INTERNAL_SERVER_ERROR",cause:e})}};return t._type="output",t}(r)]})},meta:e=>j(t,{meta:e}),use:e=>j(t,{middlewares:"_middlewares"in e?e._middlewares:[e]}),unstable_concat:e=>j(t,e._def),concat:e=>j(t,e._def),query:e=>N({...t,type:"query"},e),mutation:e=>N({...t,type:"mutation"},e),subscription:e=>N({...t,type:"subscription"},e),experimental_caller:e=>j(t,{caller:e})}}function N(e,t){let r=j(e,{resolver:t,middlewares:[async function(e){return{marker:C,ok:!0,data:await t(e),ctx:e.ctx}}]}),n={...r._def,type:e.type,experimental_caller:!!r._def.caller,meta:r._def.meta,$types:null},i=function(e){async function t(t){if(!t||!("getRawInput"in t))throw Error(M);let r=await U(0,e,t);if(!r)throw new p({code:"INTERNAL_SERVER_ERROR",message:"No result from middlewares - did you forget to `return next()`?"});if(!r.ok)throw r.error;return r.data}return t._def=e,t.procedure=!0,t}(r._def),s=r._def.caller;if(!s)return i;let o=async(...e)=>await s({args:e,invoke:i,_def:n});return o._def=n,o}let M=`
This is a client-only function.
If you want to call this function on the server, see https://trpc.io/docs/v11/server/server-side-calls
`.trim();async function U(e,t,r){try{let n=t.middlewares[e];return await n({...r,meta:t.meta,input:r.input,next:n=>U(e+1,t,{...r,ctx:n?.ctx?{...r.ctx,...n.ctx}:r.ctx,input:n&&"input"in n?n.input:r.input,getRawInput:n?.getRawInput??r.getRawInput})})}catch(e){return{ok:!1,error:f(e),marker:C}}}var F=r(4868);class L{context(){return new L}meta(){return new L}create(e){var t;let r={...e,transformer:"input"in(t=e?.transformer??h)?t:{input:t,output:t},isDev:e?.isDev??globalThis.process?.env.NODE_ENV!=="production",allowOutsideOfServer:e?.allowOutsideOfServer??!1,errorFormatter:e?.errorFormatter??d,isServer:e?.isServer??F.N,$types:null};if(!(e?.isServer??F.N)&&e?.allowOutsideOfServer!==!0)throw Error("You're trying to use @trpc/server in a non-server environment. This is not supported by default.");return{_config:r,procedure:x({meta:e?.defaultMeta}),middleware:function(e){return function e(t){return{_middlewares:t,unstable_pipe:r=>e([...t,..."_middlewares"in r?r._middlewares:[r]])}}([e])},router:b(r),mergeRouters:E,createCallerFactory:v()}}}new L,r(6823)}}]);