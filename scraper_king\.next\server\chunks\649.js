exports.id=649,exports.ids=[649],exports.modules={4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var s=r(49384),a=r(82348);function o(...e){return(0,a.QP)((0,s.$)(e))}},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>m,eb:()=>h,gC:()=>b,l6:()=>c,yv:()=>f});var s=r(60687),a=r(43210),o=r(25911),n=r(78272),i=r(3589),d=r(13964),l=r(4780);let c=o.bL;o.YJ;let f=o.WT,m=a.forwardRef(({className:e,children:t,...r},a)=>(0,s.jsxs)(o.l9,{ref:a,className:(0,l.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[t,(0,s.jsx)(o.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=o.l9.displayName;let p=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(o.PP,{ref:r,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})}));p.displayName=o.PP.displayName;let u=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(o.wn,{ref:r,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(n.A,{className:"h-4 w-4"})}));u.displayName=o.wn.displayName;let b=a.forwardRef(({className:e,children:t,position:r="popper",...a},n)=>(0,s.jsx)(o.ZL,{children:(0,s.jsxs)(o.UC,{ref:n,className:(0,l.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...a,children:[(0,s.jsx)(p,{}),(0,s.jsx)(o.LM,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,s.jsx)(u,{})]})}));b.displayName=o.UC.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(o.JU,{ref:r,className:(0,l.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=o.JU.displayName;let h=a.forwardRef(({className:e,children:t,...r},a)=>(0,s.jsxs)(o.q7,{ref:a,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(o.VF,{children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),(0,s.jsx)(o.p4,{children:t})]}));h.displayName=o.q7.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(o.wv,{ref:r,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=o.wv.displayName},18116:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(60687),a=r(43210),o=r(24851),n=r(4780);let i=a.forwardRef(({className:e,...t},r)=>(0,s.jsxs)(o.bL,{ref:r,className:(0,n.cn)("relative flex w-full touch-none select-none items-center",e),...t,children:[(0,s.jsx)(o.CC,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,s.jsx)(o.Q6,{className:"absolute h-full bg-primary"})}),(0,s.jsx)(o.zi,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));i.displayName=o.bL.displayName},21342:(e,t,r)=>{"use strict";r.d(t,{SQ:()=>m,_2:()=>p,rI:()=>c,ty:()=>f});var s=r(60687),a=r(43210),o=r(45347),n=r(14952),i=r(13964),d=r(65822),l=r(4780);let c=o.bL,f=o.l9;o.YJ,o.ZL,o.Pb,o.z6,a.forwardRef(({className:e,inset:t,children:r,...a},i)=>(0,s.jsxs)(o.ZP,{ref:i,className:(0,l.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...a,children:[r,(0,s.jsx)(n.A,{className:"ml-auto h-4 w-4"})]})).displayName=o.ZP.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(o.G5,{ref:r,className:(0,l.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=o.G5.displayName;let m=a.forwardRef(({className:e,sideOffset:t=4,...r},a)=>(0,s.jsx)(o.ZL,{children:(0,s.jsx)(o.UC,{ref:a,sideOffset:t,className:(0,l.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})}));m.displayName=o.UC.displayName;let p=a.forwardRef(({className:e,inset:t,...r},a)=>(0,s.jsx)(o.q7,{ref:a,className:(0,l.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...r}));p.displayName=o.q7.displayName,a.forwardRef(({className:e,children:t,checked:r,...a},n)=>(0,s.jsxs)(o.H_,{ref:n,className:(0,l.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:r,...a,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(o.VF,{children:(0,s.jsx)(i.A,{className:"h-4 w-4"})})}),t]})).displayName=o.H_.displayName,a.forwardRef(({className:e,children:t,...r},a)=>(0,s.jsxs)(o.hN,{ref:a,className:(0,l.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(o.VF,{children:(0,s.jsx)(d.A,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=o.hN.displayName,a.forwardRef(({className:e,inset:t,...r},a)=>(0,s.jsx)(o.JU,{ref:a,className:(0,l.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...r})).displayName=o.JU.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(o.wv,{ref:r,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=o.wv.displayName},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(60687),a=r(43210),o=r(24224),n=r(4780);let i=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...o},d)=>(0,s.jsx)("button",{className:(0,n.cn)(i({variant:t,size:r,className:e})),ref:d,...o}));d.displayName="Button"},35692:()=>{},44493:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>i,Zp:()=>n});var s=r(60687),a=r(43210),o=r(4780);let n=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("h3",{ref:r,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("p",{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let i=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("p-6 pt-0",e),...t}));i.displayName="CardContent",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},47702:(e,t,r)=>{Promise.resolve().then(r.bind(r,89544))},54987:(e,t,r)=>{"use strict";r.d(t,{d:()=>i});var s=r(60687),a=r(43210),o=r(90270),n=r(4780);let i=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(o.bL,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:r,children:(0,s.jsx)(o.zi,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));i.displayName=o.bL.displayName},77093:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},79755:(e,t,r)=>{"use strict";r.d(t,{TRPCReactProvider:()=>u,F:()=>p});var s=r(60687),a=r(8693),o=r(68357),n=r(63839),i=r(43210),d=r(558),l=r(62087),c=r(72083);let f=()=>new l.E({defaultOptions:{queries:{staleTime:3e4},dehydrate:{serializeData:d.Ay.serialize,shouldDehydrateQuery:e=>(0,c.XS)(e)||"pending"===e.state.status},hydrate:{deserializeData:d.Ay.deserialize}}}),m=()=>f(),p=(0,n.pY)();function u(e){let t=m(),[r]=(0,i.useState)(()=>p.createClient({links:[(0,o.$H)({enabled:e=>"down"===e.direction&&e.result instanceof Error}),(0,o.N9)({transformer:d.Ay,url:(process.env.VERCEL_URL?`https://${process.env.VERCEL_URL}`:`http://localhost:${process.env.PORT??3e3}`)+"/api/trpc",headers:()=>{let e=new Headers;return e.set("x-trpc-source","nextjs-react"),e}})]}));return(0,s.jsx)(a.QueryClientProvider,{client:t,children:(0,s.jsx)(p.Provider,{client:r,queryClient:t,children:e.children})})}},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>d});var s=r(60687),a=r(43210),o=r(24224),n=r(4780);let i=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("label",{ref:r,className:(0,n.cn)(i(),e),...t}));d.displayName="Label"},89544:(e,t,r)=>{"use strict";r.d(t,{TRPCReactProvider:()=>a});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call api() from the server but api is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\trpc\\react.tsx","api");let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call TRPCReactProvider() from the server but TRPCReactProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\trpc\\react.tsx","TRPCReactProvider")},90245:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>i});var s=r(37413);r(35692);var a=r(67799),o=r.n(a),n=r(89544);let i={title:"Create T3 App",description:"Generated by create-t3-app",icons:[{rel:"icon",url:"/favicon.ico"}]};function d({children:e}){return(0,s.jsx)("html",{lang:"en",className:`${o().variable}`,children:(0,s.jsx)("body",{children:(0,s.jsx)(n.TRPCReactProvider,{children:e})})})}},94558:(e,t,r)=>{Promise.resolve().then(r.bind(r,79755))}};