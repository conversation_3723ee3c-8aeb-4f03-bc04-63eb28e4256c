(()=>{var e={};e.id=147,e.ids=[147],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11633:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=r(65239),n=r(48088),i=r(88170),l=r.n(i),o=r(30893),a={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>o[e]);r.d(s,a);let d={children:["",{children:["scraper",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,32541)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\scraper\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,55782)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\scraper\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\scraper\\page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/scraper/page",pathname:"/scraper",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31094:(e,s,r)=>{"use strict";r.d(s,{FileUpload:()=>a});var t=r(60687),n=r(43210);function i(){return(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-8",children:[(0,t.jsx)("div",{className:"mb-4 h-12 w-12 animate-spin rounded-full border-4 border-white/20 border-t-[hsl(280,100%,70%)]"}),(0,t.jsx)("p",{className:"text-lg font-medium",children:"Processing your CSV file..."}),(0,t.jsx)("p",{className:"mt-2 text-sm text-white/70",children:"This may take a few moments depending on the size of your file."})]})}function l({data:e}){let[s,r]=(0,n.useState)(1),i=Math.ceil(e.length/10),l=(s-1)*10,o=Math.min(l+10,e.length),a=e.slice(l,o);return 0===e.length?(0,t.jsx)("div",{className:"rounded-lg bg-white/10 p-6 text-center",children:(0,t.jsx)("p",{className:"text-lg",children:"No data found"})}):(0,t.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:"Scraped Results"}),(0,t.jsxs)("p",{className:"text-white/70",children:["Found ",e.length," results from your CSV file"]}),(0,t.jsx)("div",{className:"overflow-x-auto rounded-lg bg-white/5",children:(0,t.jsxs)("table",{className:"w-full border-collapse",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b border-white/10 bg-white/10 text-left",children:[(0,t.jsx)("th",{className:"p-4 font-semibold",children:"URL"}),(0,t.jsx)("th",{className:"p-4 font-semibold",children:"Name"}),(0,t.jsx)("th",{className:"p-4 font-semibold",children:"Address"}),(0,t.jsx)("th",{className:"p-4 font-semibold",children:"Phone"}),(0,t.jsx)("th",{className:"p-4 font-semibold",children:"Email"})]})}),(0,t.jsx)("tbody",{children:a.map((e,s)=>(0,t.jsxs)("tr",{className:"border-b border-white/10 transition hover:bg-white/5",children:[(0,t.jsx)("td",{className:"p-4",children:(0,t.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-[hsl(280,100%,80%)] hover:underline",children:e.url})}),(0,t.jsx)("td",{className:"p-4",children:e.name}),(0,t.jsx)("td",{className:"max-w-xs p-4 truncate",children:e.address}),(0,t.jsx)("td",{className:"p-4",children:e.phone||"N/A"}),(0,t.jsx)("td",{className:"p-4",children:e.email?(0,t.jsx)("a",{href:`mailto:${e.email}`,className:"text-[hsl(280,100%,80%)] hover:underline",children:e.email}):"N/A"})]},s))})]})}),i>1&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"text-sm text-white/70",children:["Showing ",l+1,"-",o," of ",e.length," results"]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)("button",{onClick:()=>{r(e=>Math.max(e-1,1))},disabled:1===s,className:"rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50",children:"Previous"}),(0,t.jsxs)("div",{className:"flex items-center justify-center rounded-lg bg-white/10 px-4 py-2",children:[s," / ",i]}),(0,t.jsx)("button",{onClick:()=>{r(e=>Math.min(e+1,i))},disabled:s===i,className:"rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50",children:"Next"})]})]})]})}var o=r(79755);function a(){let[e,s]=(0,n.useState)(null),[r,a]=(0,n.useState)(!1),[d,c]=(0,n.useState)(null),[h,m]=(0,n.useState)(null),p=(0,n.useRef)(null),u=o.F.scraper.uploadCsv.useMutation({onSuccess:e=>{m(e)},onError:e=>{c(e.message)}}),x=e=>{if(c(null),e){if(!e.name.endsWith(".csv"))return void c("Please upload a CSV file");s(e)}},b=async()=>{if(!e)return void c("Please select a file first");try{new FormData().append("file",e);let s=await e.arrayBuffer(),r=Buffer.from(s).toString("base64");u.mutate({fileName:e.name,fileContent:r})}catch(e){c("An error occurred while uploading the file")}},f=()=>{s(null),c(null),m(null),p.current&&(p.current.value="")};return(0,t.jsxs)("div",{className:"flex flex-col gap-6",children:[!h&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:`flex min-h-40 cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors ${r?"border-[hsl(280,100%,70%)] bg-[hsl(280,100%,70%)]/10":"border-white/30 hover:border-white/50"}`,onDragOver:e=>{e.preventDefault(),a(!0)},onDragLeave:()=>{a(!1)},onDrop:e=>{e.preventDefault(),a(!1),x(e.dataTransfer.files?.[0]??null)},onClick:()=>p.current?.click(),children:[(0,t.jsx)("input",{type:"file",accept:".csv",className:"hidden",onChange:e=>{x(e.target.files?.[0]??null)},ref:p}),(0,t.jsx)("svg",{className:"mb-4 h-10 w-10 text-white/70",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})}),(0,t.jsx)("p",{className:"mb-2 text-center text-lg font-medium",children:e?e.name:"Click to select or drag and drop a CSV file"}),e&&(0,t.jsxs)("p",{className:"text-sm text-white/70",children:[(e.size/1024).toFixed(2)," KB"]})]}),d&&(0,t.jsx)("div",{className:"rounded-lg bg-red-500/20 p-4 text-red-200",children:(0,t.jsx)("p",{children:d})}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("button",{onClick:b,disabled:!e||u.isPending,className:"flex-1 rounded-lg bg-[hsl(280,100%,70%)] px-6 py-3 font-semibold text-white transition hover:bg-[hsl(280,100%,60%)] disabled:cursor-not-allowed disabled:opacity-50",children:u.isPending?"Processing...":"Submit"}),e&&(0,t.jsx)("button",{onClick:f,className:"rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20",children:"Reset"})]})]}),u.isPending&&(0,t.jsx)(i,{}),h&&(0,t.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,t.jsx)(l,{data:h}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("button",{onClick:()=>{if(!h)return;let e=new Blob([["URL,Name,Address,Phone,Email",...h.map(e=>[e.url,e.name,e.address,e.phone,e.email]).map(e=>e.map(e=>`"${String(e).replace(/"/g,'""')}"`).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),s=URL.createObjectURL(e),r=document.createElement("a");r.setAttribute("href",s),r.setAttribute("download","business_data.csv"),document.body.appendChild(r),r.click(),document.body.removeChild(r)},className:"flex-1 rounded-lg bg-[hsl(280,100%,70%)] px-6 py-3 font-semibold text-white transition hover:bg-[hsl(280,100%,60%)]",children:"Download CSV"}),(0,t.jsx)("button",{onClick:f,className:"rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20",children:"Upload Another File"})]})]})]})}},32541:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(37413),n=r(4536),i=r.n(n),l=r(55651),o=r(60250);function a(){return(0,t.jsx)(o.d,{children:(0,t.jsxs)("main",{className:"flex flex-col gap-8",children:[(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)(i(),{href:"/",className:"rounded-full bg-white/10 px-4 py-2 font-medium transition hover:bg-white/20",children:"← Back to Home"})}),(0,t.jsxs)("div",{className:"rounded-xl bg-white/10 p-6 shadow-lg",children:[(0,t.jsx)("h2",{className:"mb-6 text-2xl font-bold",children:"Upload CSV File"}),(0,t.jsx)(l.FileUpload,{})]})]})})}},33873:e=>{"use strict";e.exports=require("path")},55651:(e,s,r)=>{"use strict";r.d(s,{FileUpload:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call FileUpload() from the server but FileUpload is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\_components\\scraper\\FileUpload.tsx","FileUpload")},55782:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i,metadata:()=>n});var t=r(37413);let n={title:"Scraper King - CSV Upload & Scraping",description:"Upload CSV files and scrape data from websites"};function i({children:e}){return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-b from-[#2e026d] to-[#15162c] text-white",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("header",{className:"mb-8",children:[(0,t.jsxs)("h1",{className:"text-4xl font-extrabold tracking-tight sm:text-5xl",children:["Scraper ",(0,t.jsx)("span",{className:"text-[hsl(280,100%,70%)]",children:"King"})]}),(0,t.jsx)("p",{className:"mt-2 text-lg text-white/70",children:"Upload CSV files and scrape data from websites"})]}),e]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79646:e=>{"use strict";e.exports=require("child_process")},83152:(e,s,r)=>{Promise.resolve().then(r.bind(r,20665)),Promise.resolve().then(r.bind(r,55465)),Promise.resolve().then(r.bind(r,51503)),Promise.resolve().then(r.bind(r,82246)),Promise.resolve().then(r.bind(r,58920)),Promise.resolve().then(r.bind(r,93250)),Promise.resolve().then(r.bind(r,35917)),Promise.resolve().then(r.bind(r,74052)),Promise.resolve().then(r.bind(r,64480)),Promise.resolve().then(r.bind(r,25080)),Promise.resolve().then(r.bind(r,25480)),Promise.resolve().then(r.bind(r,10240)),Promise.resolve().then(r.bind(r,7512)),Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,55651))},92880:(e,s,r)=>{Promise.resolve().then(r.bind(r,39295)),Promise.resolve().then(r.bind(r,24903)),Promise.resolve().then(r.bind(r,8693)),Promise.resolve().then(r.bind(r,18228)),Promise.resolve().then(r.bind(r,87394)),Promise.resolve().then(r.bind(r,19100)),Promise.resolve().then(r.bind(r,54050)),Promise.resolve().then(r.bind(r,97322)),Promise.resolve().then(r.bind(r,93425)),Promise.resolve().then(r.bind(r,12030)),Promise.resolve().then(r.bind(r,46674)),Promise.resolve().then(r.bind(r,35522)),Promise.resolve().then(r.bind(r,45806)),Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,31094))},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[719,338,814,788,923,240],()=>r(11633));module.exports=t})();