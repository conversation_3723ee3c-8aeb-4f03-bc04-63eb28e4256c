(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[82],{1119:()=>{},1615:(e,t,s)=>{Promise.resolve().then(s.bind(s,7573)),Promise.resolve().then(s.bind(s,1581)),Promise.resolve().then(s.bind(s,6715)),Promise.resolve().then(s.bind(s,382)),Promise.resolve().then(s.bind(s,8822)),Promise.resolve().then(s.bind(s,2490)),Promise.resolve().then(s.bind(s,5041)),Promise.resolve().then(s.bind(s,9138)),Promise.resolve().then(s.bind(s,1610)),Promise.resolve().then(s.bind(s,5838)),Promise.resolve().then(s.bind(s,5490)),Promise.resolve().then(s.bind(s,1142)),Promise.resolve().then(s.bind(s,3666)),Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.bind(s,2480))},2480:(e,t,s)=>{"use strict";s.d(t,{BusinessScraperForm:()=>g});var a={};s.r(a),s.d(a,{checkApiHealth:()=>u,scrapeBusiness:()=>i,simpleUploadCsv:()=>m,standaloneUploadCsv:()=>p,testUploadCsv:()=>h,uploadCsv:()=>d});var r=s(5155),o=s(2115);function l(e){let{data:t}=e,[s,a]=(0,o.useState)(1),[l,n]=(0,o.useState)("name"),[c,i]=(0,o.useState)("asc"),d=e=>{e===l?i("asc"===c?"desc":"asc"):(n(e),i("asc"))},u=(0,o.useMemo)(()=>[...t].sort((e,t)=>{let s=0;switch(l){case"name":s=e.name.localeCompare(t.name);break;case"address":s=e.address.localeCompare(t.address);break;case"phone":s=e.phone.localeCompare(t.phone);break;case"url":s=e.url.localeCompare(t.url);break;case"social_links":s=(e.social_links||"N/A").localeCompare(t.social_links||"N/A");break;case"is_shopify":s=e.is_shopify===t.is_shopify?0:e.is_shopify?-1:1;break;case"is_active":s=e.is_active===t.is_active?0:e.is_active?-1:1}return"asc"===c?s:-s}),[t,l,c]),h=Math.ceil(u.length/10),m=(s-1)*10,p=Math.min(m+10,u.length),x=u.slice(m,p);return 0===t.length?(0,r.jsx)("div",{className:"rounded-lg bg-white/10 p-6 text-center",children:(0,r.jsx)("p",{className:"text-lg",children:"No results found"})}):(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold",children:"Scraped Results"}),(0,r.jsx)("button",{onClick:()=>{let e=new Blob([["Name,Address,Phone,URL,Category,Social Links,Shopify Site,Active Domain,Status",...u.map(e=>[e.name,e.address,e.phone,e.url,e.category||"",e.social_links||"",e.is_shopify?"Yes":"No",e.is_active?"Yes":"No",e.status||""]).map(e=>e.map(e=>'"'.concat(String(e).replace(/"/g,'""'),'"')).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),t=URL.createObjectURL(e),s=document.createElement("a");s.setAttribute("href",t),s.setAttribute("download","business_data.csv"),document.body.appendChild(s),s.click(),document.body.removeChild(s)},className:"rounded-lg bg-[hsl(280,100%,70%)] px-4 py-2 font-medium text-white transition hover:bg-[hsl(280,100%,60%)]",children:"Download CSV"})]}),(0,r.jsx)("div",{className:"overflow-x-auto rounded-lg bg-white/5",children:(0,r.jsxs)("table",{className:"w-full border-collapse",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b border-white/10 bg-white/10 text-left",children:[(0,r.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>d("name"),children:["Name ","name"===l&&("asc"===c?"↑":"↓")]}),(0,r.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>d("address"),children:["Address ","address"===l&&("asc"===c?"↑":"↓")]}),(0,r.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>d("phone"),children:["Phone ","phone"===l&&("asc"===c?"↑":"↓")]}),(0,r.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>d("url"),children:["URL ","url"===l&&("asc"===c?"↑":"↓")]}),(0,r.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>d("social_links"),children:["Social Links ","social_links"===l&&("asc"===c?"↑":"↓")]}),(0,r.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>d("is_shopify"),children:["Shopify ","is_shopify"===l&&("asc"===c?"↑":"↓")]}),(0,r.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>d("is_active"),children:["Active ","is_active"===l&&("asc"===c?"↑":"↓")]})]})}),(0,r.jsx)("tbody",{children:x.map((e,t)=>(0,r.jsxs)("tr",{className:"border-b border-white/10 transition hover:bg-white/5",children:[(0,r.jsx)("td",{className:"p-4",children:e.name}),(0,r.jsx)("td",{className:"p-4",children:e.address}),(0,r.jsx)("td",{className:"p-4",children:e.phone||"N/A"}),(0,r.jsx)("td",{className:"p-4",children:(0,r.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-[hsl(280,100%,80%)] hover:underline",children:e.url})}),(0,r.jsx)("td",{className:"p-4",children:e.social_links&&"N/A"!==e.social_links?(0,r.jsx)("div",{className:"flex flex-col gap-1",children:e.social_links.split("; ").map((e,t)=>(0,r.jsx)("a",{href:e,target:"_blank",rel:"noopener noreferrer",className:"text-[hsl(280,100%,80%)] hover:underline",children:e.includes("facebook.com")?"Facebook":e.includes("twitter.com")?"Twitter":e.includes("instagram.com")?"Instagram":e.includes("linkedin.com")?"LinkedIn":e.includes("youtube.com")?"YouTube":e.includes("pinterest.com")?"Pinterest":e.includes("tiktok.com")?"TikTok":e.includes("x.com")?"X":new URL(e).hostname},t))}):"N/A"}),(0,r.jsx)("td",{className:"p-4",children:(0,r.jsx)("span",{className:e.is_shopify?"text-green-400":"text-white/50",children:e.is_shopify?"Yes":"No"})}),(0,r.jsx)("td",{className:"p-4",children:(0,r.jsx)("span",{className:e.is_active?"text-green-400":"text-white/50",children:e.is_active?"Yes":"No"})})]},t))})]})}),h>1&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"text-sm text-white/70",children:["Showing ",m+1,"-",p," of ",u.length," results"]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("button",{onClick:()=>{a(e=>Math.max(e-1,1))},disabled:1===s,className:"rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50",children:"Previous"}),(0,r.jsxs)("div",{className:"flex items-center justify-center rounded-lg bg-white/10 px-4 py-2",children:[s," / ",h]}),(0,r.jsx)("button",{onClick:()=>{a(e=>Math.min(e+1,h))},disabled:s===h,className:"rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50",children:"Next"})]})]})]})}function n(e){let{isProcessingMultiple:t,progress:s}=e;return(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-8",children:[(0,r.jsx)("div",{className:"mb-4 h-12 w-12 animate-spin rounded-full border-4 border-white/20 border-t-[hsl(280,100%,70%)]"}),t&&s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("p",{className:"text-lg font-medium",children:["Scraping businesses... (",s.current+1,"/",s.total,")"]}),(0,r.jsx)("div",{className:"mt-3 h-2 w-64 overflow-hidden rounded-full bg-white/10",children:(0,r.jsx)("div",{className:"h-full bg-[hsl(280,100%,70%)]",style:{width:"".concat(Math.round(s.current/s.total*100),"%")}})}),(0,r.jsx)("p",{className:"mt-2 text-sm text-white/70",children:"Processing multiple queries. Please wait..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{className:"text-lg font-medium",children:"Scraping business data..."}),(0,r.jsx)("p",{className:"mt-2 text-sm text-white/70",children:"This may take a few moments depending on the number of results."})]})]})}let c="http://localhost:5000/api";async function i(e){try{let t=await fetch("".concat(c,"/scrape"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)try{let e=await t.json();throw Error(e.error||"Failed to scrape business data")}catch(e){throw Error("Failed to scrape business data: ".concat(t.status," ").concat(t.statusText))}try{return await t.json()}catch(e){throw console.error("Error parsing response JSON:",e),Error("Failed to parse response from server")}}catch(e){throw console.error("Error scraping business data:",e),e}}async function d(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{console.log("Starting CSV upload: ".concat(e.name,", size: ").concat(e.size,", type: ").concat(e.type));let s=new FormData;s.append("file",e);let a=t?"".concat(c,"/upload-csv-with-uploader"):"".concat(c,"/upload-csv");console.log("Uploading CSV to endpoint: ".concat(a));let r=new AbortController,o=setTimeout(()=>r.abort(),6e4);try{let e=await fetch(a,{method:"POST",body:s,mode:"cors",credentials:"same-origin",headers:{Accept:"application/json"},signal:r.signal});if(clearTimeout(o),console.log("Upload response status: ".concat(e.status," ").concat(e.statusText)),!e.ok){let t=await e.text();console.error("Error response body: ".concat(t));try{let e=JSON.parse(t),s=e.error||"Unknown server error",a=e.traceback?"\n\nDetails: ".concat(e.traceback):"";throw Error("".concat(s).concat(a))}catch(s){throw Error("Failed to upload CSV file: ".concat(e.status," ").concat(e.statusText,"\n\nResponse: ").concat(t.substring(0,500)))}}try{let t=await e.json();return console.log("Upload successful, received ".concat(t.length," results")),t}catch(e){throw console.error("Error parsing response JSON:",e),Error("Failed to parse response from server")}}catch(e){if(clearTimeout(o),e instanceof Error&&"AbortError"===e.name)throw Error("Request timed out after 60 seconds");throw e}}catch(e){throw console.error("Error uploading CSV:",e),e}}async function u(){try{let e=await fetch("".concat(c,"/health"));if(!e.ok)throw Error("API health check failed");return await e.json()}catch(e){throw console.error("API health check error:",e),e}}async function h(e){try{console.log("Testing CSV upload for file: ".concat(e.name,", size: ").concat(e.size,", type: ").concat(e.type));let t=new FormData;t.append("file",e);let s="".concat(c,"/test-upload");console.log("Uploading CSV to test endpoint: ".concat(s));let a=await fetch(s,{method:"POST",body:t,mode:"cors",credentials:"same-origin",headers:{Accept:"application/json"}});if(console.log("Test upload response status: ".concat(a.status," ").concat(a.statusText)),!a.ok)try{let e=await a.text();console.error("Error response body:",e);try{let t=JSON.parse(e);throw Error(t.error||"Test upload failed: ".concat(a.status," ").concat(a.statusText))}catch(t){throw Error("Test upload failed: ".concat(a.status," ").concat(a.statusText,". Response: ").concat(e.substring(0,200)))}}catch(e){throw Error("Test upload failed: ".concat(a.status," ").concat(a.statusText))}let r=await a.json();return console.log("Test upload successful:",r),r}catch(e){throw console.error("Error in testUploadCsv function:",e),e}}async function m(e){try{console.log("Simple CSV upload for file: ".concat(e.name,", size: ").concat(e.size,", type: ").concat(e.type));let t=new FormData;t.append("file",e);let s="".concat(c,"/simple-csv-upload");console.log("Uploading CSV to simple endpoint: ".concat(s));let a=await fetch(s,{method:"POST",body:t,mode:"cors",credentials:"same-origin",headers:{Accept:"application/json"}});if(console.log("Simple upload response status: ".concat(a.status," ").concat(a.statusText)),!a.ok){let e=await a.text();console.error("Error response body:",e);try{let t=JSON.parse(e);throw Error(t.error||"Simple upload failed: ".concat(a.status," ").concat(a.statusText))}catch(t){throw Error("Simple upload failed: ".concat(a.status," ").concat(a.statusText,". Response: ").concat(e.substring(0,200)))}}let r=await a.json();return console.log("Simple upload successful:",r),r}catch(e){throw console.error("Error in simpleUploadCsv function:",e),e}}async function p(e){try{console.log("Standalone CSV upload for file: ".concat(e.name,", size: ").concat(e.size,", type: ").concat(e.type));let t=new FormData;t.append("file",e);let s="".concat(c,"/standalone-csv-upload");console.log("Uploading CSV to standalone endpoint: ".concat(s));let a=await fetch(s,{method:"POST",body:t,mode:"cors",headers:{Accept:"application/json"}});if(console.log("Standalone upload response status: ".concat(a.status," ").concat(a.statusText)),!a.ok){let e=await a.text();console.error("Error response body:",e);try{let t=JSON.parse(e);throw Error(t.error||"Standalone upload failed: ".concat(a.status," ").concat(a.statusText))}catch(t){throw Error("Standalone upload failed: ".concat(a.status," ").concat(a.statusText,". Response: ").concat(e.substring(0,200)))}}let r=await a.json();return console.log("Standalone upload successful:",r),r}catch(e){throw console.error("Error in standaloneUploadCsv function:",e),e}}function x(e){let{onResults:t,onError:s,onLoading:a}=e,[l,n]=(0,o.useState)(null),[c,i]=(0,o.useState)(!1),[m,p]=(0,o.useState)(!0),x=(0,o.useRef)(null),f=async()=>{if(!l)return void s("Please select a CSV file first");if(0===l.size)return void s("The selected file is empty");if(!l.name.toLowerCase().endsWith(".csv"))return void s("Please select a valid CSV file");try{a(!0),s(""),console.log("Uploading CSV file:",l.name,"Size:",l.size),console.log("Using ".concat(m?"advanced":"standard"," CSV uploader"));try{let e=new File([l.size>1024?await l.slice(0,1024).text():await l.text()],l.name,{type:l.type});console.log("Testing with smaller file first:",e.size,"bytes");try{let e=await d(l,m);console.log("CSV upload results:",e),e&&e.length>0?t(e):s("No results found in the CSV file")}catch(a){console.error("CSV upload error:",a);let e=a instanceof Error?a.message:"Unknown error",t=e.includes("traceback")?"Server error processing the CSV file. Please check the file format and try again.":e;s(t),a instanceof Error&&console.error("Full error details:",a)}}catch(e){console.error("Error preparing file:",e),s("Error preparing file: ".concat(e instanceof Error?e.message:"Unknown error"))}}catch(e){console.error("CSV upload error:",e),e instanceof Error&&(console.error("Error name:",e.name),console.error("Error message:",e.message),console.error("Error stack:",e.stack)),s("Error: ".concat(e instanceof Error?e.message:"Failed to upload CSV file"))}finally{a(!1)}},g=async()=>{if(!l)return void s("Please select a CSV file first");try{a(!0),s(""),console.log("Testing CSV upload with file:",l.name);try{let e=await h(l);console.log("Test upload result:",e),s("Test successful! File info: ".concat(JSON.stringify(e)))}catch(e){console.error("Test upload error:",e),s("Test upload error: ".concat(e instanceof Error?e.message:String(e)))}}catch(e){console.error("Test error:",e),s("Error: ".concat(e instanceof Error?e.message:"Test failed"))}finally{a(!1)}},b=async()=>{try{a(!0),s(""),console.log("Checking API health");try{let e=await u();console.log("Health check result:",e),s("API health check: ".concat(JSON.stringify(e)))}catch(e){console.error("Health check error:",e),s("Health check error: ".concat(e instanceof Error?e.message:"Unknown error"))}}catch(e){console.error("Health check error:",e),s("Error: ".concat(e instanceof Error?e.message:"Health check failed"))}finally{a(!1)}};return(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)("label",{className:"font-medium",children:"Upload CSV File"}),(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition ".concat(c?"border-[hsl(280,100%,70%)] bg-[hsl(280,100%,70%)]/10":"border-white/30 hover:border-white/50"),onDragOver:e=>{e.preventDefault(),i(!0)},onDragLeave:e=>{e.preventDefault(),i(!1)},onDrop:e=>{e.preventDefault(),i(!1);let t=e.dataTransfer.files;if(t&&t.length>0){let e=t[0];if(!e)return void s("No file selected");if(!("text/csv"===e.type||e.name.toLowerCase().endsWith(".csv")||"application/vnd.ms-excel"===e.type||"application/csv"===e.type))return void s("Please select a CSV file");if(0===e.size)return void s("The selected file is empty");console.log("File dropped:",e.name,"Type:",e.type,"Size:",e.size),n(e)}},children:[(0,r.jsx)("input",{type:"file",accept:".csv",onChange:e=>{let t=e.target.files;if(t&&t.length>0){let e=t[0];if(!e)return void s("No file selected");if(!("text/csv"===e.type||e.name.toLowerCase().endsWith(".csv")||"application/vnd.ms-excel"===e.type||"application/csv"===e.type))return void s("Please select a CSV file");if(0===e.size)return void s("The selected file is empty");console.log("File selected:",e.name,"Type:",e.type,"Size:",e.size),n(e)}},className:"hidden",ref:x}),(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"mb-2 h-10 w-10 text-white/70",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})}),(0,r.jsx)("p",{className:"mb-1 text-center text-lg font-medium",children:"Drag & Drop your CSV file here"}),(0,r.jsxs)("p",{className:"text-center text-sm text-white/70",children:["or"," ",(0,r.jsx)("button",{type:"button",onClick:()=>{x.current&&x.current.click()},className:"text-[hsl(280,100%,80%)] hover:underline",children:"browse files"})]}),l&&(0,r.jsxs)("div",{className:"mt-4 flex items-center gap-2 rounded-lg bg-white/10 px-4 py-2",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),(0,r.jsx)("span",{className:"flex-1 truncate",children:l.name}),(0,r.jsx)("button",{type:"button",onClick:()=>{n(null),x.current&&(x.current.value="")},className:"text-white/70 hover:text-white",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsxs)("p",{className:"text-sm text-white/70",children:["CSV must include columns: name, location, country.",(0,r.jsx)("a",{href:"/example.csv",download:!0,className:"ml-1 text-[hsl(280,100%,80%)] hover:underline",children:"Download example CSV"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:m,onChange:e=>p(e.target.checked),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-white/20 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[hsl(280,100%,70%)]"}),(0,r.jsx)("span",{className:"ml-3 text-sm font-medium text-white/70",children:"Use Advanced CSV Uploader"})]}),(0,r.jsx)("div",{className:"text-xs text-white/50 ml-2",children:m?"Uses csv_uploader.py for better extraction":"Uses standard extraction"})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-center",children:[(0,r.jsx)("button",{type:"button",onClick:f,disabled:!l,className:"rounded-lg px-6 py-3 font-semibold transition ".concat(l?"bg-[hsl(280,100%,70%)] text-white hover:bg-[hsl(280,100%,60%)]":"bg-white/10 text-white/50 cursor-not-allowed"),children:"Upload & Process CSV"}),(0,r.jsx)("button",{onClick:g,className:"ml-2 rounded bg-gray-600 px-4 py-2 text-white hover:bg-gray-700",disabled:!l,children:"Test Upload"}),(0,r.jsx)("button",{onClick:b,className:"ml-2 rounded bg-purple-600 px-4 py-2 text-white hover:bg-purple-700",children:"Check API"})]}),(0,r.jsxs)("div",{className:"mt-2 rounded-lg bg-white/5 p-4",children:[(0,r.jsx)("h3",{className:"mb-2 font-medium",children:"CSV Format Example:"}),(0,r.jsx)("pre",{className:"overflow-x-auto whitespace-pre-wrap text-sm text-white/70",children:"name,location,country Starbucks,New York,us Apple Store,San Francisco,us Microsoft Office,Seattle,us"}),(0,r.jsx)("p",{className:"mt-2 text-sm text-white/70",children:"The CSV file should have the following columns:"}),(0,r.jsxs)("ul",{className:"mt-1 list-disc pl-5 text-sm text-white/70",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"name"}),": Business name or search term"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"location"}),": City, address, or general location"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"country"}),': Country code (e.g., "us", "uk", "in")']}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"url"})," (optional): Website URL if known"]})]}),(0,r.jsx)("p",{className:"mt-2 text-sm text-white/70",children:"The system will automatically detect column headers and supports various CSV formats, including comma, semicolon, and tab-separated files."})]})]})}s(1119);let f=[{code:"us",name:"United States",states:[{code:"al",name:"Alabama",cities:["Birmingham","Montgomery","Mobile","Huntsville"]},{code:"ak",name:"Alaska",cities:["Anchorage","Fairbanks","Juneau","Sitka"]},{code:"az",name:"Arizona",cities:["Phoenix","Tucson","Mesa","Chandler"]},{code:"ca",name:"California",cities:["Los Angeles","San Francisco","San Diego","Sacramento"]},{code:"co",name:"Colorado",cities:["Denver","Colorado Springs","Aurora","Fort Collins"]},{code:"fl",name:"Florida",cities:["Miami","Orlando","Tampa","Jacksonville"]},{code:"ga",name:"Georgia",cities:["Atlanta","Savannah","Augusta","Columbus"]},{code:"ny",name:"New York",cities:["New York City","Buffalo","Rochester","Syracuse"]},{code:"tx",name:"Texas",cities:["Houston","Dallas","Austin","San Antonio"]}]},{code:"uk",name:"United Kingdom",states:[{code:"eng",name:"England",cities:["London","Manchester","Birmingham","Liverpool"]},{code:"sct",name:"Scotland",cities:["Edinburgh","Glasgow","Aberdeen","Dundee"]},{code:"wls",name:"Wales",cities:["Cardiff","Swansea","Newport","Bangor"]},{code:"nir",name:"Northern Ireland",cities:["Belfast","Derry","Lisburn","Newry"]}]},{code:"ca",name:"Canada",states:[{code:"on",name:"Ontario",cities:["Toronto","Ottawa","Hamilton","London"]},{code:"qc",name:"Quebec",cities:["Montreal","Quebec City","Laval","Gatineau"]},{code:"bc",name:"British Columbia",cities:["Vancouver","Victoria","Surrey","Burnaby"]},{code:"ab",name:"Alberta",cities:["Calgary","Edmonton","Red Deer","Lethbridge"]}]},{code:"au",name:"Australia",states:[{code:"nsw",name:"New South Wales",cities:["Sydney","Newcastle","Wollongong","Wagga Wagga"]},{code:"vic",name:"Victoria",cities:["Melbourne","Geelong","Ballarat","Bendigo"]},{code:"qld",name:"Queensland",cities:["Brisbane","Gold Coast","Cairns","Townsville"]},{code:"wa",name:"Western Australia",cities:["Perth","Fremantle","Bunbury","Geraldton"]}]},{code:"in",name:"India",states:[{code:"mh",name:"Maharashtra",cities:["Mumbai","Pune","Nagpur","Nashik"]},{code:"dl",name:"Delhi",cities:["New Delhi","Delhi","Noida","Gurgaon"]},{code:"ka",name:"Karnataka",cities:["Bangalore","Mysore","Hubli","Mangalore"]},{code:"tn",name:"Tamil Nadu",cities:["Chennai","Coimbatore","Madurai","Salem"]}]},{code:"de",name:"Germany",states:[{code:"by",name:"Bavaria",cities:["Munich","Nuremberg","Augsburg","Regensburg"]},{code:"nw",name:"North Rhine-Westphalia",cities:["Cologne","D\xfcsseldorf","Dortmund","Essen"]},{code:"be",name:"Berlin",cities:["Berlin"]},{code:"hh",name:"Hamburg",cities:["Hamburg"]}]}];function g(){let[e,t]=(0,o.useState)(""),[s,a]=(0,o.useState)("us"),[c,d]=(0,o.useState)(""),[h,m]=(0,o.useState)(""),[p,g]=(0,o.useState)(""),[b,v]=(0,o.useState)(5),[y,w]=(0,o.useState)(!1),[j,N]=(0,o.useState)(!1),[S,C]=(0,o.useState)(null),[k,E]=(0,o.useState)(null),[T,A]=(0,o.useState)(null),[P,F]=(0,o.useState)(!1),[U,V]=(0,o.useState)(null),[_,L]=(0,o.useState)("form"),M=f.find(e=>e.code===s),D=(null==M?void 0:M.states)||[],R=D.find(e=>e.code===c),O=(null==R?void 0:R.cities)||[];(0,o.useEffect)(()=>{(async()=>{try{let e=await u();V(e)}catch(e){console.error("API health check failed:",e),V({status:"error",message:"Flask API is not available"})}})()},[]);let B=async t=>{t.preventDefault(),C(null);let a="";if(h){if(a=h,c){let e=(null==R?void 0:R.name)||"";a+=", ".concat(e)}}else{if(!p.trim())return void C("Please select a city or enter a custom location");a=p.trim()}if(!e.trim())return void C("Search term is required");E(null),F(!0),A({current:0,total:b});try{let t=await i({location:a,category:e.trim(),country:s,maxResults:b,filterShopify:y,filterActive:j});t&&t.length>0?E(t):C("No results found. Please try a different search query.")}catch(e){console.error("Scraping error:",e),C("Error: ".concat(e instanceof Error?e.message:"Failed to scrape business data. Please try again."))}finally{F(!1),A(null)}},z=()=>{t(""),a("us"),d(""),m(""),g(""),v(5),w(!1),N(!1),C(null),E(null),A(null),F(!1)};return(0,r.jsxs)("div",{className:"flex flex-col gap-6",children:[U&&"error"===U.status&&(0,r.jsxs)("div",{className:"rounded-lg bg-red-500/20 p-4 text-red-200",children:[(0,r.jsx)("p",{className:"font-semibold",children:"API Error:"}),(0,r.jsx)("p",{children:U.message}),(0,r.jsx)("p",{className:"mt-2 text-sm",children:"Please make sure the Flask backend is running on http://localhost:5000"})]}),!P&&!k&&(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex border-b border-white/10",children:[(0,r.jsx)("button",{type:"button",className:"px-4 py-2 font-medium transition ".concat("form"===_?"border-b-2 border-[hsl(280,100%,70%)] text-[hsl(280,100%,80%)]":"text-white/70 hover:text-white"),onClick:()=>L("form"),children:"Search Form"}),(0,r.jsx)("button",{type:"button",className:"px-4 py-2 font-medium transition ".concat("csv"===_?"border-b-2 border-[hsl(280,100%,70%)] text-[hsl(280,100%,80%)]":"text-white/70 hover:text-white"),onClick:()=>L("csv"),children:"CSV Upload"})]}),"form"===_&&(0,r.jsxs)("form",{onSubmit:B,className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"font-medium",children:["Country ",(0,r.jsx)("span",{className:"text-red-400",children:"*"})]}),(0,r.jsx)("select",{id:"country",value:s,onChange:e=>{a(e.target.value),d(""),m("")},className:"rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)] scrollable-select",children:f.map(e=>(0,r.jsx)("option",{value:e.code,children:e.name},e.code))})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)("label",{htmlFor:"state",className:"font-medium",children:"State/Region"}),(0,r.jsxs)("select",{id:"state",value:c,onChange:e=>{d(e.target.value),m("")},className:"rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)] scrollable-select",disabled:0===D.length,children:[(0,r.jsx)("option",{value:"",children:"Select a state/region"}),D.map(e=>(0,r.jsx)("option",{value:e.code,children:e.name},e.code))]})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsx)("label",{htmlFor:"city",className:"font-medium",children:"City"}),(0,r.jsxs)("select",{id:"city",value:h,onChange:e=>m(e.target.value),className:"rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)] scrollable-select",disabled:0===O.length,children:[(0,r.jsx)("option",{value:"",children:"Select a city"}),O.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,r.jsxs)("label",{htmlFor:"customLocation",className:"font-medium",children:["Custom Location",(0,r.jsx)("span",{className:"ml-2 text-sm text-white/70",children:"(Optional, if city not listed)"})]}),(0,r.jsx)("input",{id:"customLocation",type:"text",value:p,onChange:e=>g(e.target.value),placeholder:"Enter specific location",className:"rounded-lg bg-white/10 px-4 py-2 text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]"})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2 md:col-span-2",children:[(0,r.jsxs)("label",{htmlFor:"category",className:"font-medium",children:["Search Term ",(0,r.jsx)("span",{className:"text-red-400",children:"*"})]}),(0,r.jsx)("input",{id:"category",type:"text",value:e,onChange:e=>t(e.target.value),placeholder:"Business name, type, or any search term (e.g., Starbucks, restaurants, plumbers)",className:"rounded-lg bg-white/10 px-4 py-2 text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]",required:!0}),(0,r.jsx)("p",{className:"mt-1 text-sm text-white/70",children:"Enter any business name, type, or search term related to the businesses you want to find"})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2 md:col-span-2",children:[(0,r.jsx)("label",{htmlFor:"maxResults",className:"font-medium",children:"Number of Searches to Scrape"}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("input",{id:"maxResults",type:"range",min:"1",max:"20",value:b,onChange:e=>v(parseInt(e.target.value)),className:"w-full"}),(0,r.jsx)("span",{className:"min-w-[2rem] text-center",children:b})]}),(0,r.jsx)("p",{className:"mt-1 text-sm text-white/70",children:"Higher values may take longer but provide more results"})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2 md:col-span-2",children:[(0,r.jsx)("label",{className:"font-medium",children:"Filter Options"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("input",{id:"filterShopify",type:"checkbox",checked:y,onChange:e=>w(e.target.checked),className:"h-4 w-4 rounded border-white/30 bg-white/10 text-[hsl(280,100%,70%)]"}),(0,r.jsx)("label",{htmlFor:"filterShopify",className:"text-sm",children:"Shopify Sites Only"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("input",{id:"filterActive",type:"checkbox",checked:j,onChange:e=>N(e.target.checked),className:"h-4 w-4 rounded border-white/30 bg-white/10 text-[hsl(280,100%,70%)]"}),(0,r.jsx)("label",{htmlFor:"filterActive",className:"text-sm",children:"Active Domains Only"})]})]}),(0,r.jsx)("p",{className:"mt-1 text-sm text-white/70",children:"Filter results to include only specific types of websites"})]})]}),S&&(0,r.jsxs)("div",{className:"rounded-lg bg-red-500/20 p-4 text-red-200",children:[(0,r.jsx)("p",{className:"font-semibold",children:"Error:"}),(0,r.jsx)("p",{children:S.replace(/^Error: /,"")}),(0,r.jsx)("p",{className:"mt-2 text-sm",children:"Please check your search parameters and try again. Make sure you've entered a valid location and category."})]}),(0,r.jsxs)("div",{className:"mt-4 flex gap-4",children:[(0,r.jsx)("button",{type:"submit",className:"flex-1 rounded-lg bg-[hsl(280,100%,70%)] px-6 py-3 font-semibold text-white transition hover:bg-[hsl(280,100%,60%)]",children:"Start Scraping"}),(0,r.jsx)("button",{type:"button",onClick:z,className:"rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20",children:"Reset"})]})]}),"csv"===_&&(0,r.jsx)(x,{onResults:E,onError:C,onLoading:F})]}),P&&(0,r.jsx)(n,{isProcessingMultiple:!0,progress:T||{current:0,total:5}}),k&&(0,r.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,r.jsx)(l,{data:k}),(0,r.jsx)("div",{className:"flex gap-4",children:(0,r.jsx)("button",{onClick:z,className:"rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20",children:"New Search"})})]})]})}},2490:(e,t,s)=>{"use strict";s.d(t,{useIsFetching:()=>l});var a=s(2115),r=s(7165),o=s(6715);function l(e,t){let s=(0,o.useQueryClient)(t),l=s.getQueryCache();return a.useSyncExternalStore(a.useCallback(e=>l.subscribe(r.jG.batchCalls(e)),[l]),()=>s.isFetching(e),()=>s.isFetching(e))}},7573:(e,t,s)=>{"use strict";s.d(t,{HydrationBoundary:()=>n});var a=s(2115),r=s(1451),o=s(6715),l=(e,t)=>"object"==typeof e&&null!==e&&t in e,n=e=>{let{children:t,options:s={},state:n,queryClient:c}=e,i=(0,o.useQueryClient)(c),[d,u]=a.useState(),h=a.useRef(s);return h.current=s,a.useMemo(()=>{if(n){if("object"!=typeof n)return;let e=i.getQueryCache(),t=n.queries||[],s=[],a=[];for(let r of t){let t=e.get(r.queryHash);if(t){let e=r.state.dataUpdatedAt>t.state.dataUpdatedAt||l(r.promise,"status")&&l(t.promise,"status")&&r.promise.status!==t.promise.status,s=null==d?void 0:d.find(e=>e.queryHash===r.queryHash);e&&(!s||r.state.dataUpdatedAt>s.state.dataUpdatedAt)&&a.push(r)}else s.push(r)}s.length>0&&(0,r.Qv)(i,{queries:s},h.current),a.length>0&&u(e=>e?[...e,...a]:a)}},[i,d,n]),a.useEffect(()=>{d&&((0,r.Qv)(i,{queries:d},h.current),u(void 0))},[i,d]),t}},9138:(e,t,s)=>{"use strict";s.d(t,{useIsMutating:()=>n,useMutationState:()=>i});var a=s(2115),r=s(2020),o=s(7165),l=s(6715);function n(e,t){let s=(0,l.useQueryClient)(t);return i({filters:{...e,status:"pending"}},s).length}function c(e,t){return e.findAll(t.filters).map(e=>t.select?t.select(e):e.state)}function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,s=(0,l.useQueryClient)(t).getMutationCache(),n=a.useRef(e),i=a.useRef(null);return i.current||(i.current=c(s,e)),a.useEffect(()=>{n.current=e}),a.useSyncExternalStore(a.useCallback(e=>s.subscribe(()=>{let t=(0,r.BH)(i.current,c(s,n.current));i.current!==t&&(i.current=t,o.jG.schedule(e))}),[s]),()=>i.current,()=>i.current)}}},e=>{var t=t=>e(e.s=t);e.O(0,[590,874,616,441,684,358],()=>t(1615)),_N_E=e.O()}]);