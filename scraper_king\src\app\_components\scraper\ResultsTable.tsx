"use client";

import { useState } from "react";

type ScrapedData = {
  url: string;
  name: string;
  address: string;
  phone: string;
  email: string;
};

interface ResultsTableProps {
  data: ScrapedData[];
}

export function ResultsTable({ data }: ResultsTableProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Calculate pagination
  const totalPages = Math.ceil(data.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, data.length);
  const currentData = data.slice(startIndex, endIndex);

  // Handle pagination
  const handlePrevPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  if (data.length === 0) {
    return (
      <div className="rounded-lg bg-white/10 p-6 text-center">
        <p className="text-lg">No data found</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <h2 className="text-2xl font-bold">Scraped Results</h2>
      <p className="text-white/70">
        Found {data.length} results from your CSV file
      </p>

      <div className="overflow-x-auto rounded-lg bg-white/5">
        <table className="w-full border-collapse">
          <thead>
            <tr className="border-b border-white/10 bg-white/10 text-left">
              <th className="p-4 font-semibold">URL</th>
              <th className="p-4 font-semibold">Name</th>
              <th className="p-4 font-semibold">Address</th>
              <th className="p-4 font-semibold">Phone</th>
              <th className="p-4 font-semibold">Email</th>
            </tr>
          </thead>
          <tbody>
            {currentData.map((item, index) => (
              <tr
                key={index}
                className="border-b border-white/10 transition hover:bg-white/5"
              >
                <td className="p-4">
                  <a
                    href={item.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[hsl(280,100%,80%)] hover:underline"
                  >
                    {item.url}
                  </a>
                </td>
                <td className="p-4">{item.name}</td>
                <td className="max-w-xs p-4 truncate">{item.address}</td>
                <td className="p-4">{item.phone || "N/A"}</td>
                <td className="p-4">
                  {item.email ? (
                    <a
                      href={`mailto:${item.email}`}
                      className="text-[hsl(280,100%,80%)] hover:underline"
                    >
                      {item.email}
                    </a>
                  ) : (
                    "N/A"
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-white/70">
            Showing {startIndex + 1}-{endIndex} of {data.length} results
          </div>
          <div className="flex gap-2">
            <button
              onClick={handlePrevPage}
              disabled={currentPage === 1}
              className="rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50"
            >
              Previous
            </button>
            <div className="flex items-center justify-center rounded-lg bg-white/10 px-4 py-2">
              {currentPage} / {totalPages}
            </div>
            <button
              onClick={handleNextPage}
              disabled={currentPage === totalPages}
              className="rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
