(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[147],{3964:(e,s,l)=>{"use strict";let t;l.d(s,{TRPCReactProvider:()=>b,F:()=>x});var r=l(5155),n=l(6715),i=l(1213),a=l(7566),d=l(2115),o=l(9177),c=l(2775),h=l(1451);let m=()=>new c.E({defaultOptions:{queries:{staleTime:3e4},dehydrate:{serializeData:o.Ay.serialize,shouldDehydrateQuery:e=>(0,h.XS)(e)||"pending"===e.state.status},hydrate:{deserializeData:o.Ay.deserialize}}});l(9509);let u=()=>(null!=t||(t=m()),t),x=(0,a.pY)();function b(e){let s=u(),[l]=(0,d.useState)(()=>x.createClient({links:[(0,i.$H)({enabled:e=>"down"===e.direction&&e.result instanceof Error}),(0,i.N9)({transformer:o.Ay,url:window.location.origin+"/api/trpc",headers:()=>{let e=new Headers;return e.set("x-trpc-source","nextjs-react"),e}})]}));return(0,r.jsx)(n.QueryClientProvider,{client:s,children:(0,r.jsx)(x.Provider,{client:l,queryClient:s,children:e.children})})}},6808:(e,s,l)=>{Promise.resolve().then(l.bind(l,7573)),Promise.resolve().then(l.bind(l,1581)),Promise.resolve().then(l.bind(l,6715)),Promise.resolve().then(l.bind(l,382)),Promise.resolve().then(l.bind(l,8822)),Promise.resolve().then(l.bind(l,2490)),Promise.resolve().then(l.bind(l,5041)),Promise.resolve().then(l.bind(l,9138)),Promise.resolve().then(l.bind(l,1610)),Promise.resolve().then(l.bind(l,5838)),Promise.resolve().then(l.bind(l,5490)),Promise.resolve().then(l.bind(l,1142)),Promise.resolve().then(l.bind(l,3666)),Promise.resolve().then(l.t.bind(l,6874,23)),Promise.resolve().then(l.bind(l,6824))},6824:(e,s,l)=>{"use strict";l.d(s,{FileUpload:()=>o});var t=l(5155),r=l(2115);function n(){return(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-8",children:[(0,t.jsx)("div",{className:"mb-4 h-12 w-12 animate-spin rounded-full border-4 border-white/20 border-t-[hsl(280,100%,70%)]"}),(0,t.jsx)("p",{className:"text-lg font-medium",children:"Processing your CSV file..."}),(0,t.jsx)("p",{className:"mt-2 text-sm text-white/70",children:"This may take a few moments depending on the size of your file."})]})}function i(e){let{data:s}=e,[l,n]=(0,r.useState)(1),i=Math.ceil(s.length/10),a=(l-1)*10,d=Math.min(a+10,s.length),o=s.slice(a,d);return 0===s.length?(0,t.jsx)("div",{className:"rounded-lg bg-white/10 p-6 text-center",children:(0,t.jsx)("p",{className:"text-lg",children:"No data found"})}):(0,t.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:"Scraped Results"}),(0,t.jsxs)("p",{className:"text-white/70",children:["Found ",s.length," results from your CSV file"]}),(0,t.jsx)("div",{className:"overflow-x-auto rounded-lg bg-white/5",children:(0,t.jsxs)("table",{className:"w-full border-collapse",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b border-white/10 bg-white/10 text-left",children:[(0,t.jsx)("th",{className:"p-4 font-semibold",children:"URL"}),(0,t.jsx)("th",{className:"p-4 font-semibold",children:"Name"}),(0,t.jsx)("th",{className:"p-4 font-semibold",children:"Address"}),(0,t.jsx)("th",{className:"p-4 font-semibold",children:"Phone"}),(0,t.jsx)("th",{className:"p-4 font-semibold",children:"Email"})]})}),(0,t.jsx)("tbody",{children:o.map((e,s)=>(0,t.jsxs)("tr",{className:"border-b border-white/10 transition hover:bg-white/5",children:[(0,t.jsx)("td",{className:"p-4",children:(0,t.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-[hsl(280,100%,80%)] hover:underline",children:e.url})}),(0,t.jsx)("td",{className:"p-4",children:e.name}),(0,t.jsx)("td",{className:"max-w-xs p-4 truncate",children:e.address}),(0,t.jsx)("td",{className:"p-4",children:e.phone||"N/A"}),(0,t.jsx)("td",{className:"p-4",children:e.email?(0,t.jsx)("a",{href:"mailto:".concat(e.email),className:"text-[hsl(280,100%,80%)] hover:underline",children:e.email}):"N/A"})]},s))})]})}),i>1&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"text-sm text-white/70",children:["Showing ",a+1,"-",d," of ",s.length," results"]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)("button",{onClick:()=>{n(e=>Math.max(e-1,1))},disabled:1===l,className:"rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50",children:"Previous"}),(0,t.jsxs)("div",{className:"flex items-center justify-center rounded-lg bg-white/10 px-4 py-2",children:[l," / ",i]}),(0,t.jsx)("button",{onClick:()=>{n(e=>Math.min(e+1,i))},disabled:l===i,className:"rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50",children:"Next"})]})]})]})}var a=l(3964),d=l(9641).Buffer;function o(){let[e,s]=(0,r.useState)(null),[l,o]=(0,r.useState)(!1),[c,h]=(0,r.useState)(null),[m,u]=(0,r.useState)(null),x=(0,r.useRef)(null),b=a.F.scraper.uploadCsv.useMutation({onSuccess:e=>{u(e)},onError:e=>{h(e.message)}}),p=e=>{if(h(null),e){if(!e.name.endsWith(".csv"))return void h("Please upload a CSV file");s(e)}},f=async()=>{if(!e)return void h("Please select a file first");try{new FormData().append("file",e);let s=await e.arrayBuffer(),l=d.from(s).toString("base64");b.mutate({fileName:e.name,fileContent:l})}catch(e){h("An error occurred while uploading the file")}},v=()=>{s(null),h(null),u(null),x.current&&(x.current.value="")};return(0,t.jsxs)("div",{className:"flex flex-col gap-6",children:[!m&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex min-h-40 cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors ".concat(l?"border-[hsl(280,100%,70%)] bg-[hsl(280,100%,70%)]/10":"border-white/30 hover:border-white/50"),onDragOver:e=>{e.preventDefault(),o(!0)},onDragLeave:()=>{o(!1)},onDrop:e=>{var s,l;e.preventDefault(),o(!1),p(null!=(l=null==(s=e.dataTransfer.files)?void 0:s[0])?l:null)},onClick:()=>{var e;return null==(e=x.current)?void 0:e.click()},children:[(0,t.jsx)("input",{type:"file",accept:".csv",className:"hidden",onChange:e=>{var s,l;p(null!=(l=null==(s=e.target.files)?void 0:s[0])?l:null)},ref:x}),(0,t.jsx)("svg",{className:"mb-4 h-10 w-10 text-white/70",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})}),(0,t.jsx)("p",{className:"mb-2 text-center text-lg font-medium",children:e?e.name:"Click to select or drag and drop a CSV file"}),e&&(0,t.jsxs)("p",{className:"text-sm text-white/70",children:[(e.size/1024).toFixed(2)," KB"]})]}),c&&(0,t.jsx)("div",{className:"rounded-lg bg-red-500/20 p-4 text-red-200",children:(0,t.jsx)("p",{children:c})}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("button",{onClick:f,disabled:!e||b.isPending,className:"flex-1 rounded-lg bg-[hsl(280,100%,70%)] px-6 py-3 font-semibold text-white transition hover:bg-[hsl(280,100%,60%)] disabled:cursor-not-allowed disabled:opacity-50",children:b.isPending?"Processing...":"Submit"}),e&&(0,t.jsx)("button",{onClick:v,className:"rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20",children:"Reset"})]})]}),b.isPending&&(0,t.jsx)(n,{}),m&&(0,t.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,t.jsx)(i,{data:m}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("button",{onClick:()=>{if(!m)return;let e=new Blob([["URL,Name,Address,Phone,Email",...m.map(e=>[e.url,e.name,e.address,e.phone,e.email]).map(e=>e.map(e=>'"'.concat(String(e).replace(/"/g,'""'),'"')).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),s=URL.createObjectURL(e),l=document.createElement("a");l.setAttribute("href",s),l.setAttribute("download","business_data.csv"),document.body.appendChild(l),l.click(),document.body.removeChild(l)},className:"flex-1 rounded-lg bg-[hsl(280,100%,70%)] px-6 py-3 font-semibold text-white transition hover:bg-[hsl(280,100%,60%)]",children:"Download CSV"}),(0,t.jsx)("button",{onClick:v,className:"rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20",children:"Upload Another File"})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[874,616,308,457,441,684,358],()=>s(6808)),_N_E=e.O()}]);