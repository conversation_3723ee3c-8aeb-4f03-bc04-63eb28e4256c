(()=>{var e={};e.id=841,e.ids=[841],e.modules={8516:(e,r,t)=>{"use strict";t.d(r,{_:()=>n});var s=t(32871),o=t(70762);let n=(0,s.w)({server:{DATABASE_URL:o.z.string().url(),NODE_ENV:o.z.enum(["development","test","production"]).default("development")},client:{},runtimeEnv:{DATABASE_URL:process.env.DATABASE_URL,NODE_ENV:"production"},skipValidation:!!process.env.SKIP_ENV_VALIDATION,emptyStringAsUndefined:!0})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38545:(e,r,t)=>{"use strict";t.d(r,{OA:()=>d,dW:()=>p,LO:()=>h,JI:()=>g});var s=t(50469),o=t(23041),n=t(70762),a=t(96330),i=t(8516);let c=globalThis,l=c.prisma??new a.PrismaClient({log:"development"===i._.NODE_ENV?["query","error","warn"]:["error"]});"production"!==i._.NODE_ENV&&(c.prisma=l);let p=async e=>({db:l,...e}),u=s.Al.context().create({transformer:o.Ay,errorFormatter:({shape:e,error:r})=>({...e,data:{...e.data,zodError:r.cause instanceof n.G?r.cause.flatten():null}})}),d=u.createCallerFactory,h=u.router,m=u.middleware(async({next:e,path:r})=>{let t=Date.now();if(u._config.isDev){let e=Math.floor(400*Math.random())+100;await new Promise(r=>setTimeout(r,e))}let s=await e(),o=Date.now();return console.log(`[TRPC] ${r} took ${o-t}ms to execute`),s}),g=u.procedure.use(m)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54547:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>g,serverHooks:()=>v,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>y});var s={};t.r(s),t.d(s,{GET:()=>m,POST:()=>m});var o=t(96559),n=t(48088),a=t(37719),i=t(43949);t(58218),t(30785),t(35355);let c=e=>e=(e=e.startsWith("/")?e.slice(1):e).endsWith("/")?e.slice(0,-1):e;async function l(e){let r=new Headers,t=async t=>e.createContext?.({req:e.req,resHeaders:r,...t}),s=c(new URL(e.req.url).pathname),o=c(e.endpoint),n=c(s.slice(o.length));return await (0,i.S)({...e,req:e.req,createContext:t,path:n,error:null,onError(r){e?.onError?.({...r,req:e.req})},responseMeta(t){let s=e.responseMeta?.(t);if(s?.headers)if(s.headers instanceof Headers)for(let[e,t]of s.headers.entries())r.append(e,t);else for(let[e,t]of Object.entries(s.headers))if(Array.isArray(t))for(let s of t)r.append(e,s);else"string"==typeof t&&r.set(e,t);return{headers:r,status:s?.status}}})}var p=t(8516),u=t(88948),d=t(38545);let h=async e=>(0,d.dW)({headers:e.headers}),m=e=>l({endpoint:"/api/trpc",req:e,router:u.P,createContext:()=>h(e),onError:"development"===p._.NODE_ENV?({path:e,error:r})=>{console.error(`❌ tRPC failed on ${e??"<no-path>"}: ${r.message}`)}:void 0}),g=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/trpc/[trpc]/route",pathname:"/api/trpc/[trpc]",filename:"route",bundlePath:"app/api/trpc/[trpc]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\api\\trpc\\[trpc]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:y,serverHooks:v}=g;function w(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:y})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79646:e=>{"use strict";e.exports=require("child_process")},88948:(e,r,t)=>{"use strict";t.d(r,{P:()=>w,u:()=>E});var s=t(70762),o=t(38545);let n=(0,o.LO)({hello:o.JI.input(s.z.object({text:s.z.string()})).query(({input:e})=>({greeting:`Hello ${e.text}`})),create:o.JI.input(s.z.object({name:s.z.string().min(1)})).mutation(async({ctx:e,input:r})=>e.db.post.create({data:{name:r.name}})),getLatest:o.JI.query(async({ctx:e})=>await e.db.post.findFirst({orderBy:{createdAt:"desc"}})??null)});var a=t(79646),i=t(28354),c=t(33873),l=t.n(c),p=t(29021),u=t.n(p);let d=(0,i.promisify)(a.exec),h=e=>{let r=e.replace(/^https?:\/\//,"").replace(/\/$/,"").split("/")[0],t=r.split(".")[0].charAt(0).toUpperCase()+r.split(".")[0].slice(1),s=["New York","Los Angeles","Chicago","Houston","Phoenix","Philadelphia"],o=s[Math.floor(Math.random()*s.length)],n=Math.floor(1e3*Math.random())+1,a=["Main St","Broadway","Park Ave","Oak St","Maple Ave","Washington Blvd"],i=a[Math.floor(Math.random()*a.length)];return{url:e,name:t,address:`${n} ${i}, ${o}, NY`,phone:Math.random()>.2?`+1 (${Math.floor(900*Math.random())+100}) ${Math.floor(900*Math.random())+100}-${Math.floor(9e3*Math.random())+1e3}`:"",email:Math.random()>.3?`contact@${r}`:""}},m=e=>e.split(/\r?\n/).filter(e=>""!==e.trim()).map(e=>{if(e.startsWith('"')){let r=e.match(/"([^"]+)"/);return r?r[1]:""}return e.split(",")[0]}).filter(e=>e.startsWith("http")),g=s.z.object({location:s.z.string().min(1),category:s.z.string().min(1),country:s.z.string().min(1)}),f=s.z.array(s.z.object({name:s.z.string(),address:s.z.string(),phone:s.z.string(),email:s.z.string(),url:s.z.string(),category:s.z.string().optional(),social_links:s.z.string().optional()})),y=async e=>{try{let r=l().resolve(process.cwd(),"business_scraper.py");if(!u().existsSync(r)){console.error(`Business scraper script not found at ${r}`);let e=l().resolve(process.cwd(),"..","business_scraper.py");if(!u().existsSync(e))throw console.error(`Business scraper script not found at alternative path ${e} either`),Error("Business scraper script not found");console.log(`Found business scraper script at alternative path: ${e}`),r=e}console.log(`Using business scraper script at: ${r}`),console.log(`Running with parameters: location=${e.location}, search term=${e.category}, country=${e.country}`);let t=`python "${r}" --location "${e.location}" --category "${e.category}" --country "${e.country}"`;console.log(`Executing command: ${t}`);let{stdout:s,stderr:o}=await d(t);o&&!o.includes("DevTools listening")&&console.error(`Python script error: ${o}`),console.log("Python script executed successfully");try{return JSON.parse(s).map(e=>({...e,social_links:e.social_links||"N/A"}))}catch(e){throw console.error(`Error parsing JSON output: ${e}`),console.error(`Raw output: ${s}`),Error("Failed to parse Python script output")}}catch(e){throw console.error("Error running Python script:",e),Error(`Failed to scrape business data: ${e instanceof Error?e.message:String(e)}`)}},v=(0,o.LO)({uploadCsv:o.JI.input(s.z.object({fileName:s.z.string(),fileContent:s.z.string()})).mutation(async({input:e})=>{try{let r=Buffer.from(e.fileContent,"base64").toString("utf-8"),t=m(r);if(0===t.length)throw Error("No valid URLs found in the CSV file");return t.map(e=>h(e))}catch(e){throw console.error("Error processing CSV:",e),Error("Failed to process the CSV file")}}),scrapeBusiness:o.JI.input(g).mutation(async({input:e})=>{try{let r=await y(e);return f.parse(r)}catch(e){throw console.error("Error scraping business data:",e),Error("Failed to scrape business data")}})}),w=(0,o.LO)({post:n,scraper:v}),E=(0,o.OA)(w)},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},96559:(e,r,t)=>{"use strict";e.exports=t(44870)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[719,788],()=>t(54547));module.exports=s})();