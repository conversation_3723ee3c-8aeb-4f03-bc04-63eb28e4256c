import { z } from "zod";
import { createTR<PERSON><PERSON><PERSON>er, publicProcedure } from "~/server/api/trpc";
import { exec } from "child_process";
import { promisify } from "util";
import path from "path";
import fs from "fs";

const execPromise = promisify(exec);

// Mock function to simulate scraping data from URLs
// In a real implementation, this would make HTTP requests to the URLs
// and extract the required information using a library like cheerio
const mockScrapeUrl = (url: string) => {
  // Generate a business name from the URL
  const domain = url.replace(/^https?:\/\//, '').replace(/\/$/, '').split('/')[0];
  const businessName = domain.split('.')[0].charAt(0).toUpperCase() + domain.split('.')[0].slice(1);

  // Generate a random address
  const cities = ["New York", "Los Angeles", "Chicago", "Houston", "Phoenix", "Philadelphia"];
  const randomCity = cities[Math.floor(Math.random() * cities.length)];
  const randomStreetNumber = Math.floor(Math.random() * 1000) + 1;
  const streets = ["Main St", "Broadway", "Park Ave", "Oak St", "Maple Ave", "Washington Blvd"];
  const randomStreet = streets[Math.floor(Math.random() * streets.length)];
  const address = `${randomStreetNumber} ${randomStreet}, ${randomCity}, NY`;

  // Generate random phone and email
  const phone = Math.random() > 0.2 ? `+1 (${Math.floor(Math.random() * 900) + 100}) ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}` : '';
  const email = Math.random() > 0.3 ? `contact@${domain}` : '';

  return {
    url,
    name: businessName,
    address,
    phone,
    email
  };
};

// Parse CSV content (simple implementation)
const parseCsv = (csvContent: string): string[] => {
  // Split by newlines and filter out empty lines
  const lines = csvContent.split(/\r?\n/).filter(line => line.trim() !== '');

  // Extract URLs (assuming the first column contains URLs)
  // This is a simplified implementation - a real one would be more robust
  return lines.map(line => {
    // Handle quoted values properly
    if (line.startsWith('"')) {
      const match = line.match(/"([^"]+)"/);
      return match ? match[1] : '';
    }
    // Otherwise just take the first column
    return line.split(',')[0];
  }).filter(url => url.startsWith('http'));
};

// Define the schema for the business scraper input
const businessScrapeInputSchema = z.object({
  location: z.string().min(1),
  category: z.string().min(1), // 'category' is used as a generic search term
  country: z.string().min(1),
});

// Define the schema for the business scraper result
const businessScrapeResultSchema = z.array(
  z.object({
    name: z.string(),
    address: z.string(),
    phone: z.string(),
    email: z.string(),
    url: z.string(),
    category: z.string().optional(),
    social_links: z.string().optional(),
  })
);



// Function to run the Python script for business scraping with optimized performance
const runBusinessScraperScript = async (input: z.infer<typeof businessScrapeInputSchema>) => {
  try {
    // Get the path to the business_scraper.py script
    let scriptPath = path.resolve(process.cwd(), "business_scraper.py");

    // Check if the script exists
    if (!fs.existsSync(scriptPath)) {
      console.error(`Business scraper script not found at ${scriptPath}`);
      // Try alternative path
      const altScriptPath = path.resolve(process.cwd(), "..", "business_scraper.py");
      if (!fs.existsSync(altScriptPath)) {
        console.error(`Business scraper script not found at alternative path ${altScriptPath} either`);
        throw new Error(`Business scraper script not found`);
      }
      console.log(`Found business scraper script at alternative path: ${altScriptPath}`);
      // Use the alternative path
      scriptPath = altScriptPath;
    }

    console.log(`Using business scraper script at: ${scriptPath}`);
    console.log(`Running with parameters: location=${input.location}, search term=${input.category}, country=${input.country}`);

    // Prepare the command to run the Python script
    const command = `python "${scriptPath}" --location "${input.location}" --category "${input.category}" --country "${input.country}"`;

    // Execute the command
    console.log(`Executing command: ${command}`);
    const { stdout, stderr } = await execPromise(command);

    if (stderr && !stderr.includes('DevTools listening')) {
      console.error(`Python script error: ${stderr}`);
      // Don't throw an error here, as some stderr output might be normal
      // Only log it for debugging purposes
    }

    console.log(`Python script executed successfully`);

    // Parse the output as JSON
    try {
      const results = JSON.parse(stdout);

      // Ensure social_links field exists for all results
      const processedResults = results.map((result: any) => ({
        ...result,
        social_links: result.social_links || "N/A"
      }));

      return processedResults;
    } catch (parseError) {
      console.error(`Error parsing JSON output: ${parseError}`);
      console.error(`Raw output: ${stdout}`);
      throw new Error("Failed to parse Python script output");
    }
  } catch (error) {
    console.error("Error running Python script:", error);
    // Re-throw the error instead of falling back to mock data
    throw new Error(`Failed to scrape business data: ${error instanceof Error ? error.message : String(error)}`);
  }
};

export const scraperRouter = createTRPCRouter({
  uploadCsv: publicProcedure
    .input(
      z.object({
        fileName: z.string(),
        fileContent: z.string(), // Base64 encoded file content
      })
    )
    .mutation(async ({ input }) => {
      try {
        // Decode base64 content
        const buffer = Buffer.from(input.fileContent, 'base64');
        const csvContent = buffer.toString('utf-8');

        // Parse CSV to extract URLs
        const urls = parseCsv(csvContent);

        if (urls.length === 0) {
          throw new Error("No valid URLs found in the CSV file");
        }

        // Process each URL (in a real app, this would be done in batches or with a queue)
        const results = urls.map(url => mockScrapeUrl(url));

        return results;
      } catch (error) {
        console.error("Error processing CSV:", error);
        throw new Error("Failed to process the CSV file");
      }
    }),

  scrapeBusiness: publicProcedure
    .input(businessScrapeInputSchema)
    .mutation(async ({ input }) => {
      try {
        // Call the Python script for business scraping
        const results = await runBusinessScraperScript(input);

        // Validate the results
        return businessScrapeResultSchema.parse(results);
      } catch (error) {
        console.error("Error scraping business data:", error);
        throw new Error("Failed to scrape business data");
      }
    }),
});
