"use client";

import { useState, useMemo } from "react";

export type ScrapedResult = {
  name: string;
  address: string;
  phone: string;
  url: string;
  category?: string;
  social_links?: string;
  is_shopify?: boolean;
  is_active?: boolean;
  status?: string;
};

interface ResultsTableProps {
  data: ScrapedResult[];
}

type SortField = "name" | "address" | "phone" | "url" | "social_links" | "is_shopify" | "is_active";
type SortDirection = "asc" | "desc";

export function ResultsTable({ data }: ResultsTableProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [sortField, setSortField] = useState<SortField>("name");
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");
  const itemsPerPage = 10;

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (field === sortField) {
      // Toggle direction if same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // New field, default to ascending
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Sort and paginate data
  const sortedData = useMemo(() => {
    return [...data].sort((a, b) => {
      let comparison = 0;

      switch (sortField) {
        case "name":
          comparison = a.name.localeCompare(b.name);
          break;
        case "address":
          comparison = a.address.localeCompare(b.address);
          break;
        case "phone":
          comparison = a.phone.localeCompare(b.phone);
          break;
        case "url":
          comparison = a.url.localeCompare(b.url);
          break;
        case "social_links":
          comparison = (a.social_links || "N/A").localeCompare(b.social_links || "N/A");
          break;
        case "is_shopify":
          comparison = (a.is_shopify === b.is_shopify) ? 0 : a.is_shopify ? -1 : 1;
          break;
        case "is_active":
          comparison = (a.is_active === b.is_active) ? 0 : a.is_active ? -1 : 1;
          break;
      }

      return sortDirection === "asc" ? comparison : -comparison;
    });
  }, [data, sortField, sortDirection]);

  // Calculate pagination
  const totalPages = Math.ceil(sortedData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, sortedData.length);
  const currentData = sortedData.slice(startIndex, endIndex);

  // Handle pagination
  const handlePrevPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  // Download as CSV
  const downloadCsv = () => {
    // Create CSV content
    const headers = ["Name", "Address", "Phone", "URL", "Category", "Social Links", "Shopify Site", "Active Domain", "Status"];
    const rows = sortedData.map(item => [
      item.name,
      item.address,
      item.phone,
      item.url,
      item.category || "",
      item.social_links || "",
      item.is_shopify ? "Yes" : "No",
      item.is_active ? "Yes" : "No",
      item.status || ""
    ]);

    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
    ].join('\n');

    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'business_data.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (data.length === 0) {
    return (
      <div className="rounded-lg bg-white/10 p-6 text-center">
        <p className="text-lg">No results found</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Scraped Results</h2>
        <button
          onClick={downloadCsv}
          className="rounded-lg bg-[hsl(280,100%,70%)] px-4 py-2 font-medium text-white transition hover:bg-[hsl(280,100%,60%)]"
        >
          Download CSV
        </button>
      </div>

      <div className="overflow-x-auto rounded-lg bg-white/5">
        <table className="w-full border-collapse">
          <thead>
            <tr className="border-b border-white/10 bg-white/10 text-left">
              <th
                className="cursor-pointer p-4 font-semibold"
                onClick={() => handleSort("name")}
              >
                Name {sortField === "name" && (sortDirection === "asc" ? "↑" : "↓")}
              </th>
              <th
                className="cursor-pointer p-4 font-semibold"
                onClick={() => handleSort("address")}
              >
                Address {sortField === "address" && (sortDirection === "asc" ? "↑" : "↓")}
              </th>
              <th
                className="cursor-pointer p-4 font-semibold"
                onClick={() => handleSort("phone")}
              >
                Phone {sortField === "phone" && (sortDirection === "asc" ? "↑" : "↓")}
              </th>
              <th
                className="cursor-pointer p-4 font-semibold"
                onClick={() => handleSort("url")}
              >
                URL {sortField === "url" && (sortDirection === "asc" ? "↑" : "↓")}
              </th>
              <th
                className="cursor-pointer p-4 font-semibold"
                onClick={() => handleSort("social_links")}
              >
                Social Links {sortField === "social_links" && (sortDirection === "asc" ? "↑" : "↓")}
              </th>
              <th
                className="cursor-pointer p-4 font-semibold"
                onClick={() => handleSort("is_shopify")}
              >
                Shopify {sortField === "is_shopify" && (sortDirection === "asc" ? "↑" : "↓")}
              </th>
              <th
                className="cursor-pointer p-4 font-semibold"
                onClick={() => handleSort("is_active")}
              >
                Active {sortField === "is_active" && (sortDirection === "asc" ? "↑" : "↓")}
              </th>
            </tr>
          </thead>
          <tbody>
            {currentData.map((item, index) => (
              <tr
                key={index}
                className="border-b border-white/10 transition hover:bg-white/5"
              >
                <td className="p-4">{item.name}</td>
                <td className="p-4">{item.address}</td>
                <td className="p-4">{item.phone || "N/A"}</td>
                <td className="p-4">
                  <a
                    href={item.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[hsl(280,100%,80%)] hover:underline"
                  >
                    {item.url}
                  </a>
                </td>
                <td className="p-4">
                  {item.social_links && item.social_links !== "N/A" ? (
                    <div className="flex flex-col gap-1">
                      {item.social_links.split("; ").map((link, i) => (
                        <a
                          key={i}
                          href={link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-[hsl(280,100%,80%)] hover:underline"
                        >
                          {link.includes("facebook.com") ? "Facebook" :
                           link.includes("twitter.com") ? "Twitter" :
                           link.includes("instagram.com") ? "Instagram" :
                           link.includes("linkedin.com") ? "LinkedIn" :
                           link.includes("youtube.com") ? "YouTube" :
                           link.includes("pinterest.com") ? "Pinterest" :
                           link.includes("tiktok.com") ? "TikTok" :
                           link.includes("x.com") ? "X" :
                           new URL(link).hostname}
                        </a>
                      ))}
                    </div>
                  ) : (
                    "N/A"
                  )}
                </td>
                <td className="p-4">
                  <span className={item.is_shopify ? "text-green-400" : "text-white/50"}>
                    {item.is_shopify ? "Yes" : "No"}
                  </span>
                </td>
                <td className="p-4">
                  <span className={item.is_active ? "text-green-400" : "text-white/50"}>
                    {item.is_active ? "Yes" : "No"}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-white/70">
            Showing {startIndex + 1}-{endIndex} of {sortedData.length} results
          </div>
          <div className="flex gap-2">
            <button
              onClick={handlePrevPage}
              disabled={currentPage === 1}
              className="rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50"
            >
              Previous
            </button>
            <div className="flex items-center justify-center rounded-lg bg-white/10 px-4 py-2">
              {currentPage} / {totalPages}
            </div>
            <button
              onClick={handleNextPage}
              disabled={currentPage === totalPages}
              className="rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
