"use strict";exports.id=274,exports.ids=[274],exports.modules={43:(e,t,r)=>{r.d(t,{jH:()=>i});var n=r(43210);r(60687);var o=n.createContext(void 0);function i(e){let t=n.useContext(o);return e||t||"ltr"}},363:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},1359:(e,t,r)=>{r.d(t,{Oh:()=>i});var n=r(43210),o=0;function i(){n.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??l()),document.body.insertAdjacentElement("beforeend",e[1]??l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},3589:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},8730:(e,t,r)=>{r.d(t,{TL:()=>l});var n=r(43210),o=r(98599),i=r(60687);function l(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var l;let e,a,s=(l=r,(a=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(a=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),u=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(u.ref=t?(0,o.t)(t,s):s),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...l}=e,a=n.Children.toArray(o),u=a.find(s);if(u){let e=u.props.children,o=a.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...l,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var a=Symbol("radix.slottable");function s(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},9510:(e,t,r)=>{r.d(t,{N:()=>s});var n=r(43210),o=r(11273),i=r(98599),l=r(8730),a=r(60687);function s(e){let t=e+"CollectionProvider",[r,s]=(0,o.A)(t),[u,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,o=n.useRef(null),i=n.useRef(new Map).current;return(0,a.jsx)(u,{scope:t,itemMap:i,collectionRef:o,children:r})};d.displayName=t;let f=e+"CollectionSlot",p=(0,l.TL)(f),m=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=c(f,r),l=(0,i.s)(t,o.collectionRef);return(0,a.jsx)(p,{ref:l,children:n})});m.displayName=f;let h=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,l.TL)(h),y=n.forwardRef((e,t)=>{let{scope:r,children:o,...l}=e,s=n.useRef(null),u=(0,i.s)(t,s),d=c(h,r);return n.useEffect(()=>(d.itemMap.set(s,{ref:s,...l}),()=>void d.itemMap.delete(s))),(0,a.jsx)(g,{...{[v]:""},ref:u,children:o})});return y.displayName=h,[{Provider:d,Slot:m,ItemSlot:y},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},s]}var u=new WeakMap;function c(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=d(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function d(e){return e!=e||0===e?0:Math.trunc(e)}},11273:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(43210),o=r(60687);function i(e,t=[]){let r=[],l=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return l.scopeName=e,[function(t,i){let l=n.createContext(i),a=r.length;r=[...r,i];let s=t=>{let{scope:r,children:i,...s}=t,u=r?.[e]?.[a]||l,c=n.useMemo(()=>s,Object.values(s));return(0,o.jsx)(u.Provider,{value:c,children:i})};return s.displayName=t+"Provider",[s,function(r,o){let s=o?.[e]?.[a]||l,u=n.useContext(s);if(u)return u;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(l,...t)]}},12941:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},13964:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14163:(e,t,r)=>{r.d(t,{hO:()=>s,sG:()=>a});var n=r(43210),o=r(51215),i=r(8730),l=r(60687),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?r:t,{...i,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},14952:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},17313:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},18853:(e,t,r)=>{r.d(t,{X:()=>i});var n=r(43210),o=r(66156);function i(e){let[t,r]=n.useState(void 0);return(0,o.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},21134:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},24224:(e,t,r)=>{r.d(t,{F:()=>l});var n=r(49384);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$,l=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:a}=t,s=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],n=null==a?void 0:a[e];if(null===t)return null;let i=o(t)||o(n);return l[e][i]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,s,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...u}[t]):({...a,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},24851:(e,t,r)=>{r.d(t,{CC:()=>H,Q6:()=>K,bL:()=>G,zi:()=>$});var n=r(43210),o=r(67969),i=r(70569),l=r(98599),a=r(11273),s=r(65551),u=r(43),c=r(83721),d=r(18853),f=r(14163),p=r(9510),m=r(60687),h=["PageUp","PageDown"],v=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],g={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},y="Slider",[b,w,x]=(0,p.N)(y),[E,C]=(0,a.A)(y,[x]),[S,R]=E(y),k=n.forwardRef((e,t)=>{let{name:r,min:l=0,max:a=100,step:u=1,orientation:c="horizontal",disabled:d=!1,minStepsBetweenThumbs:f=0,defaultValue:p=[l],value:g,onValueChange:y=()=>{},onValueCommit:w=()=>{},inverted:x=!1,form:E,...C}=e,R=n.useRef(new Set),k=n.useRef(0),M="horizontal"===c,[j=[],N]=(0,s.i)({prop:g,defaultProp:p,onChange:e=>{let t=[...R.current];t[k.current]?.focus(),y(e)}}),T=n.useRef(j);function D(e,t,{commit:r}={commit:!1}){let n=(String(u).split(".")[1]||"").length,i=function(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}(Math.round((e-l)/u)*u+l,n),s=(0,o.q)(i,[l,a]);N((e=[])=>{let n=function(e=[],t,r){let n=[...e];return n[r]=t,n.sort((e,t)=>e-t)}(e,s,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,r)=>e[r+1]-t))>=t;return!0}(n,f*u))return e;{k.current=n.indexOf(s);let t=String(n)!==String(e);return t&&r&&w(n),t?n:e}})}return(0,m.jsx)(S,{scope:e.__scopeSlider,name:r,disabled:d,min:l,max:a,valueIndexToChangeRef:k,thumbs:R.current,values:j,orientation:c,form:E,children:(0,m.jsx)(b.Provider,{scope:e.__scopeSlider,children:(0,m.jsx)(b.Slot,{scope:e.__scopeSlider,children:(0,m.jsx)(M?A:P,{"aria-disabled":d,"data-disabled":d?"":void 0,...C,ref:t,onPointerDown:(0,i.m)(C.onPointerDown,()=>{d||(T.current=j)}),min:l,max:a,inverted:x,onSlideStart:d?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let r=e.map(e=>Math.abs(e-t)),n=Math.min(...r);return r.indexOf(n)}(j,e);D(e,t)},onSlideMove:d?void 0:function(e){D(e,k.current)},onSlideEnd:d?void 0:function(){let e=T.current[k.current];j[k.current]!==e&&w(j)},onHomeKeyDown:()=>!d&&D(l,0,{commit:!0}),onEndKeyDown:()=>!d&&D(a,j.length-1,{commit:!0}),onStepKeyDown:({event:e,direction:t})=>{if(!d){let r=h.includes(e.key)||e.shiftKey&&v.includes(e.key),n=k.current;D(j[n]+u*(r?10:1)*t,n,{commit:!0})}}})})})})});k.displayName=y;var[M,j]=E(y,{startEdge:"left",endEdge:"right",size:"width",direction:1}),A=n.forwardRef((e,t)=>{let{min:r,max:o,dir:i,inverted:a,onSlideStart:s,onSlideMove:c,onSlideEnd:d,onStepKeyDown:f,...p}=e,[h,v]=n.useState(null),y=(0,l.s)(t,e=>v(e)),b=n.useRef(void 0),w=(0,u.jH)(i),x="ltr"===w,E=x&&!a||!x&&a;function C(e){let t=b.current||h.getBoundingClientRect(),n=W([0,t.width],E?[r,o]:[o,r]);return b.current=t,n(e-t.left)}return(0,m.jsx)(M,{scope:e.__scopeSlider,startEdge:E?"left":"right",endEdge:E?"right":"left",direction:E?1:-1,size:"width",children:(0,m.jsx)(N,{dir:w,"data-orientation":"horizontal",...p,ref:y,style:{...p.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=C(e.clientX);s?.(t)},onSlideMove:e=>{let t=C(e.clientX);c?.(t)},onSlideEnd:()=>{b.current=void 0,d?.()},onStepKeyDown:e=>{let t=g[E?"from-left":"from-right"].includes(e.key);f?.({event:e,direction:t?-1:1})}})})}),P=n.forwardRef((e,t)=>{let{min:r,max:o,inverted:i,onSlideStart:a,onSlideMove:s,onSlideEnd:u,onStepKeyDown:c,...d}=e,f=n.useRef(null),p=(0,l.s)(t,f),h=n.useRef(void 0),v=!i;function y(e){let t=h.current||f.current.getBoundingClientRect(),n=W([0,t.height],v?[o,r]:[r,o]);return h.current=t,n(e-t.top)}return(0,m.jsx)(M,{scope:e.__scopeSlider,startEdge:v?"bottom":"top",endEdge:v?"top":"bottom",size:"height",direction:v?1:-1,children:(0,m.jsx)(N,{"data-orientation":"vertical",...d,ref:p,style:{...d.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=y(e.clientY);a?.(t)},onSlideMove:e=>{let t=y(e.clientY);s?.(t)},onSlideEnd:()=>{h.current=void 0,u?.()},onStepKeyDown:e=>{let t=g[v?"from-bottom":"from-top"].includes(e.key);c?.({event:e,direction:t?-1:1})}})})}),N=n.forwardRef((e,t)=>{let{__scopeSlider:r,onSlideStart:n,onSlideMove:o,onSlideEnd:l,onHomeKeyDown:a,onEndKeyDown:s,onStepKeyDown:u,...c}=e,d=R(y,r);return(0,m.jsx)(f.sG.span,{...c,ref:t,onKeyDown:(0,i.m)(e.onKeyDown,e=>{"Home"===e.key?(a(e),e.preventDefault()):"End"===e.key?(s(e),e.preventDefault()):h.concat(v).includes(e.key)&&(u(e),e.preventDefault())}),onPointerDown:(0,i.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),d.thumbs.has(t)?t.focus():n(e)}),onPointerMove:(0,i.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&o(e)}),onPointerUp:(0,i.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),l(e))})})}),T="SliderTrack",D=n.forwardRef((e,t)=>{let{__scopeSlider:r,...n}=e,o=R(T,r);return(0,m.jsx)(f.sG.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...n,ref:t})});D.displayName=T;var L="SliderRange",O=n.forwardRef((e,t)=>{let{__scopeSlider:r,...o}=e,i=R(L,r),a=j(L,r),s=n.useRef(null),u=(0,l.s)(t,s),c=i.values.length,d=i.values.map(e=>B(e,i.min,i.max)),p=c>1?Math.min(...d):0,h=100-Math.max(...d);return(0,m.jsx)(f.sG.span,{"data-orientation":i.orientation,"data-disabled":i.disabled?"":void 0,...o,ref:u,style:{...e.style,[a.startEdge]:p+"%",[a.endEdge]:h+"%"}})});O.displayName=L;var I="SliderThumb",_=n.forwardRef((e,t)=>{let r=w(e.__scopeSlider),[o,i]=n.useState(null),a=(0,l.s)(t,e=>i(e)),s=n.useMemo(()=>o?r().findIndex(e=>e.ref.current===o):-1,[r,o]);return(0,m.jsx)(F,{...e,ref:a,index:s})}),F=n.forwardRef((e,t)=>{let{__scopeSlider:r,index:o,name:a,...s}=e,u=R(I,r),c=j(I,r),[p,h]=n.useState(null),v=(0,l.s)(t,e=>h(e)),g=!p||u.form||!!p.closest("form"),y=(0,d.X)(p),w=u.values[o],x=void 0===w?0:B(w,u.min,u.max),E=function(e,t){return t>2?`Value ${e+1} of ${t}`:2===t?["Minimum","Maximum"][e]:void 0}(o,u.values.length),C=y?.[c.size],S=C?function(e,t,r){let n=e/2,o=W([0,50],[0,n]);return(n-o(t)*r)*r}(C,x,c.direction):0;return n.useEffect(()=>{if(p)return u.thumbs.add(p),()=>{u.thumbs.delete(p)}},[p,u.thumbs]),(0,m.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[c.startEdge]:`calc(${x}% + ${S}px)`},children:[(0,m.jsx)(b.ItemSlot,{scope:e.__scopeSlider,children:(0,m.jsx)(f.sG.span,{role:"slider","aria-label":e["aria-label"]||E,"aria-valuemin":u.min,"aria-valuenow":w,"aria-valuemax":u.max,"aria-orientation":u.orientation,"data-orientation":u.orientation,"data-disabled":u.disabled?"":void 0,tabIndex:u.disabled?void 0:0,...s,ref:v,style:void 0===w?{display:"none"}:e.style,onFocus:(0,i.m)(e.onFocus,()=>{u.valueIndexToChangeRef.current=o})})}),g&&(0,m.jsx)(z,{name:a??(u.name?u.name+(u.values.length>1?"[]":""):void 0),form:u.form,value:w},o)]})});_.displayName=I;var z=n.forwardRef(({__scopeSlider:e,value:t,...r},o)=>{let i=n.useRef(null),a=(0,l.s)(i,o),s=(0,c.Z)(t);return n.useEffect(()=>{let e=i.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(s!==t&&r){let n=new Event("input",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[s,t]),(0,m.jsx)(f.sG.input,{style:{display:"none"},...r,ref:a,defaultValue:t})});function B(e,t,r){return(0,o.q)(100/(r-t)*(e-t),[0,100])}function W(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}z.displayName="RadioBubbleInput";var G=k,H=D,K=O,$=_},25028:(e,t,r)=>{r.d(t,{Z:()=>s});var n=r(43210),o=r(51215),i=r(14163),l=r(66156),a=r(60687),s=n.forwardRef((e,t)=>{let{container:r,...s}=e,[u,c]=n.useState(!1);(0,l.N)(()=>c(!0),[]);let d=r||u&&globalThis?.document?.body;return d?o.createPortal((0,a.jsx)(i.sG.div,{...s,ref:t}),d):null});s.displayName="Portal"},25911:(e,t,r)=>{r.d(t,{UC:()=>eO,YJ:()=>e_,In:()=>eD,q7:()=>ez,VF:()=>eW,p4:()=>eB,JU:()=>eF,ZL:()=>eL,bL:()=>eP,wn:()=>eH,PP:()=>eG,wv:()=>eK,l9:()=>eN,WT:()=>eT,LM:()=>eI});var n=r(43210),o=r(51215),i=r(67969),l=r(70569),a=r(9510),s=r(98599),u=r(11273),c=r(43),d=r(31355),f=r(1359),p=r(32547),m=r(96963),h=r(55509),v=r(25028),g=r(14163),y=r(8730),b=r(35876),w=r(65551),x=r(66156),E=r(83721),C=r(60687),S=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});n.forwardRef((e,t)=>(0,C.jsx)(g.sG.span,{...e,ref:t,style:{...S,...e.style}})).displayName="VisuallyHidden";var R=r(63376),k=r(42247),M=[" ","Enter","ArrowUp","ArrowDown"],j=[" ","Enter"],A="Select",[P,N,T]=(0,a.N)(A),[D,L]=(0,u.A)(A,[T,h.Bk]),O=(0,h.Bk)(),[I,_]=D(A),[F,z]=D(A),B=e=>{let{__scopeSelect:t,children:r,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:s,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:v,required:g,form:y}=e,b=O(t),[x,E]=n.useState(null),[S,R]=n.useState(null),[k,M]=n.useState(!1),j=(0,c.jH)(d),[N,T]=(0,w.i)({prop:o,defaultProp:i??!1,onChange:l,caller:A}),[D,L]=(0,w.i)({prop:a,defaultProp:s,onChange:u,caller:A}),_=n.useRef(null),z=!x||y||!!x.closest("form"),[B,W]=n.useState(new Set),G=Array.from(B).map(e=>e.props.value).join(";");return(0,C.jsx)(h.bL,{...b,children:(0,C.jsxs)(I,{required:g,scope:t,trigger:x,onTriggerChange:E,valueNode:S,onValueNodeChange:R,valueNodeHasChildren:k,onValueNodeHasChildrenChange:M,contentId:(0,m.B)(),value:D,onValueChange:L,open:N,onOpenChange:T,dir:j,triggerPointerDownPosRef:_,disabled:v,children:[(0,C.jsx)(P.Provider,{scope:t,children:(0,C.jsx)(F,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{W(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{W(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),z?(0,C.jsxs)(ek,{"aria-hidden":!0,required:g,tabIndex:-1,name:f,autoComplete:p,value:D,onChange:e=>L(e.target.value),disabled:v,form:y,children:[void 0===D?(0,C.jsx)("option",{value:""}):null,Array.from(B)]},G):null]})})};B.displayName=A;var W="SelectTrigger",G=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:o=!1,...i}=e,a=O(r),u=_(W,r),c=u.disabled||o,d=(0,s.s)(t,u.onTriggerChange),f=N(r),p=n.useRef("touch"),[m,v,y]=ej(e=>{let t=f().filter(e=>!e.disabled),r=t.find(e=>e.value===u.value),n=eA(t,e,r);void 0!==n&&u.onValueChange(n.value)}),b=e=>{c||(u.onOpenChange(!0),y()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,C.jsx)(h.Mz,{asChild:!0,...a,children:(0,C.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":eM(u.value)?"":void 0,...i,ref:d,onClick:(0,l.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&b(e)}),onPointerDown:(0,l.m)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(b(e),e.preventDefault())}),onKeyDown:(0,l.m)(i.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&M.includes(e.key)&&(b(),e.preventDefault())})})})});G.displayName=W;var H="SelectValue",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,children:i,placeholder:l="",...a}=e,u=_(H,r),{onValueNodeHasChildrenChange:c}=u,d=void 0!==i,f=(0,s.s)(t,u.onValueNodeChange);return(0,x.N)(()=>{c(d)},[c,d]),(0,C.jsx)(g.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:eM(u.value)?(0,C.jsx)(C.Fragment,{children:l}):i})});K.displayName=H;var $=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...o}=e;return(0,C.jsx)(g.sG.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});$.displayName="SelectIcon";var U=e=>(0,C.jsx)(v.Z,{asChild:!0,...e});U.displayName="SelectPortal";var V="SelectContent",q=n.forwardRef((e,t)=>{let r=_(V,e.__scopeSelect),[i,l]=n.useState();return((0,x.N)(()=>{l(new DocumentFragment)},[]),r.open)?(0,C.jsx)(J,{...e,ref:t}):i?o.createPortal((0,C.jsx)(X,{scope:e.__scopeSelect,children:(0,C.jsx)(P.Slot,{scope:e.__scopeSelect,children:(0,C.jsx)("div",{children:e.children})})}),i):null});q.displayName=V;var[X,Y]=D(V),Z=(0,y.TL)("SelectContent.RemoveScroll"),J=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:u,side:c,sideOffset:m,align:h,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:b,sticky:w,hideWhenDetached:x,avoidCollisions:E,...S}=e,M=_(V,r),[j,A]=n.useState(null),[P,T]=n.useState(null),D=(0,s.s)(t,e=>A(e)),[L,O]=n.useState(null),[I,F]=n.useState(null),z=N(r),[B,W]=n.useState(!1),G=n.useRef(!1);n.useEffect(()=>{if(j)return(0,R.Eq)(j)},[j]),(0,f.Oh)();let H=n.useCallback(e=>{let[t,...r]=z().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(r?.scrollIntoView({block:"nearest"}),r===t&&P&&(P.scrollTop=0),r===n&&P&&(P.scrollTop=P.scrollHeight),r?.focus(),document.activeElement!==o))return},[z,P]),K=n.useCallback(()=>H([L,j]),[H,L,j]);n.useEffect(()=>{B&&K()},[B,K]);let{onOpenChange:$,triggerPointerDownPosRef:U}=M;n.useEffect(()=>{if(j){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(U.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(U.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():j.contains(r.target)||$(!1),document.removeEventListener("pointermove",t),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[j,$,U]),n.useEffect(()=>{let e=()=>$(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[$]);let[q,Y]=ej(e=>{let t=z().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eA(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),J=n.useCallback((e,t,r)=>{let n=!G.current&&!r;(void 0!==M.value&&M.value===t||n)&&(O(e),n&&(G.current=!0))},[M.value]),et=n.useCallback(()=>j?.focus(),[j]),er=n.useCallback((e,t,r)=>{let n=!G.current&&!r;(void 0!==M.value&&M.value===t||n)&&F(e)},[M.value]),en="popper"===o?ee:Q,eo=en===ee?{side:c,sideOffset:m,align:h,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:b,sticky:w,hideWhenDetached:x,avoidCollisions:E}:{};return(0,C.jsx)(X,{scope:r,content:j,viewport:P,onViewportChange:T,itemRefCallback:J,selectedItem:L,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:K,selectedItemText:I,position:o,isPositioned:B,searchRef:q,children:(0,C.jsx)(k.A,{as:Z,allowPinchZoom:!0,children:(0,C.jsx)(p.n,{asChild:!0,trapped:M.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(i,e=>{M.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,C.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>M.onOpenChange(!1),children:(0,C.jsx)(en,{role:"listbox",id:M.contentId,"data-state":M.open?"open":"closed",dir:M.dir,onContextMenu:e=>e.preventDefault(),...S,...eo,onPlaced:()=>W(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...S.style},onKeyDown:(0,l.m)(S.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=z().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>H(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:o,...l}=e,a=_(V,r),u=Y(V,r),[c,d]=n.useState(null),[f,p]=n.useState(null),m=(0,s.s)(t,e=>p(e)),h=N(r),v=n.useRef(!1),y=n.useRef(!0),{viewport:b,selectedItem:w,selectedItemText:E,focusSelectedItem:S}=u,R=n.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&f&&b&&w&&E){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),r=a.valueNode.getBoundingClientRect(),n=E.getBoundingClientRect();if("rtl"!==a.dir){let o=n.left-t.left,l=r.left-o,a=e.left-l,s=e.width+a,u=Math.max(s,t.width),d=window.innerWidth-10,f=(0,i.q)(l,[10,Math.max(10,d-u)]);c.style.minWidth=s+"px",c.style.left=f+"px"}else{let o=t.right-n.right,l=window.innerWidth-r.right-o,a=window.innerWidth-e.right-l,s=e.width+a,u=Math.max(s,t.width),d=window.innerWidth-10,f=(0,i.q)(l,[10,Math.max(10,d-u)]);c.style.minWidth=s+"px",c.style.right=f+"px"}let l=h(),s=window.innerHeight-20,u=b.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),m=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),y=p+m+u+parseInt(d.paddingBottom,10)+g,x=Math.min(5*w.offsetHeight,y),C=window.getComputedStyle(b),S=parseInt(C.paddingTop,10),R=parseInt(C.paddingBottom,10),k=e.top+e.height/2-10,M=w.offsetHeight/2,j=p+m+(w.offsetTop+M);if(j<=k){let e=l.length>0&&w===l[l.length-1].ref.current;c.style.bottom="0px";let t=Math.max(s-k,M+(e?R:0)+(f.clientHeight-b.offsetTop-b.offsetHeight)+g);c.style.height=j+t+"px"}else{let e=l.length>0&&w===l[0].ref.current;c.style.top="0px";let t=Math.max(k,p+b.offsetTop+(e?S:0)+M);c.style.height=t+(y-j)+"px",b.scrollTop=j-k+b.offsetTop}c.style.margin="10px 0",c.style.minHeight=x+"px",c.style.maxHeight=s+"px",o?.(),requestAnimationFrame(()=>v.current=!0)}},[h,a.trigger,a.valueNode,c,f,b,w,E,a.dir,o]);(0,x.N)(()=>R(),[R]);let[k,M]=n.useState();(0,x.N)(()=>{f&&M(window.getComputedStyle(f).zIndex)},[f]);let j=n.useCallback(e=>{e&&!0===y.current&&(R(),S?.(),y.current=!1)},[R,S]);return(0,C.jsx)(et,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:v,onScrollButtonChange:j,children:(0,C.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:k},children:(0,C.jsx)(g.sG.div,{...l,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...i}=e,l=O(r);return(0,C.jsx)(h.UC,{...l,...i,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=D(V,{}),en="SelectViewport",eo=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:o,...i}=e,a=Y(en,r),u=er(en,r),c=(0,s.s)(t,a.onViewportChange),d=n.useRef(0);return(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,C.jsx)(P.Slot,{scope:r,children:(0,C.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=u;if(n?.current&&r){let e=Math.abs(d.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let i=o+e,l=Math.min(n,i),a=i-l;r.style.height=l+"px","0px"===r.style.bottom&&(t.scrollTop=a>0?a:0,r.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});eo.displayName=en;var ei="SelectGroup",[el,ea]=D(ei),es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=(0,m.B)();return(0,C.jsx)(el,{scope:r,id:o,children:(0,C.jsx)(g.sG.div,{role:"group","aria-labelledby":o,...n,ref:t})})});es.displayName=ei;var eu="SelectLabel",ec=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=ea(eu,r);return(0,C.jsx)(g.sG.div,{id:o.id,...n,ref:t})});ec.displayName=eu;var ed="SelectItem",[ef,ep]=D(ed),em=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:o,disabled:i=!1,textValue:a,...u}=e,c=_(ed,r),d=Y(ed,r),f=c.value===o,[p,h]=n.useState(a??""),[v,y]=n.useState(!1),b=(0,s.s)(t,e=>d.itemRefCallback?.(e,o,i)),w=(0,m.B)(),x=n.useRef("touch"),E=()=>{i||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,C.jsx)(ef,{scope:r,value:o,disabled:i,textId:w,isSelected:f,onItemTextChange:n.useCallback(e=>{h(t=>t||(e?.textContent??"").trim())},[]),children:(0,C.jsx)(P.ItemSlot,{scope:r,value:o,disabled:i,textValue:p,children:(0,C.jsx)(g.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...u,ref:b,onFocus:(0,l.m)(u.onFocus,()=>y(!0)),onBlur:(0,l.m)(u.onBlur,()=>y(!1)),onClick:(0,l.m)(u.onClick,()=>{"mouse"!==x.current&&E()}),onPointerUp:(0,l.m)(u.onPointerUp,()=>{"mouse"===x.current&&E()}),onPointerDown:(0,l.m)(u.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,l.m)(u.onPointerMove,e=>{x.current=e.pointerType,i?d.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(u.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,l.m)(u.onKeyDown,e=>{(d.searchRef?.current===""||" "!==e.key)&&(j.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});em.displayName=ed;var eh="SelectItemText",ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:i,style:l,...a}=e,u=_(eh,r),c=Y(eh,r),d=ep(eh,r),f=z(eh,r),[p,m]=n.useState(null),h=(0,s.s)(t,e=>m(e),d.onItemTextChange,e=>c.itemTextRefCallback?.(e,d.value,d.disabled)),v=p?.textContent,y=n.useMemo(()=>(0,C.jsx)("option",{value:d.value,disabled:d.disabled,children:v},d.value),[d.disabled,d.value,v]),{onNativeOptionAdd:b,onNativeOptionRemove:w}=f;return(0,x.N)(()=>(b(y),()=>w(y)),[b,w,y]),(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(g.sG.span,{id:d.textId,...a,ref:h}),d.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(a.children,u.valueNode):null]})});ev.displayName=eh;var eg="SelectItemIndicator",ey=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ep(eg,r).isSelected?(0,C.jsx)(g.sG.span,{"aria-hidden":!0,...n,ref:t}):null});ey.displayName=eg;var eb="SelectScrollUpButton",ew=n.forwardRef((e,t)=>{let r=Y(eb,e.__scopeSelect),o=er(eb,e.__scopeSelect),[i,l]=n.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,x.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){l(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),i?(0,C.jsx)(eC,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=eb;var ex="SelectScrollDownButton",eE=n.forwardRef((e,t)=>{let r=Y(ex,e.__scopeSelect),o=er(ex,e.__scopeSelect),[i,l]=n.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,x.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),i?(0,C.jsx)(eC,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eE.displayName=ex;var eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:o,...i}=e,a=Y("SelectScrollButton",r),s=n.useRef(null),u=N(r),c=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>c(),[c]),(0,x.N)(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,C.jsx)(g.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.m)(i.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(i.onPointerMove,()=>{a.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(i.onPointerLeave,()=>{c()})})}),eS=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,C.jsx)(g.sG.div,{"aria-hidden":!0,...n,ref:t})});eS.displayName="SelectSeparator";var eR="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=O(r),i=_(eR,r),l=Y(eR,r);return i.open&&"popper"===l.position?(0,C.jsx)(h.i3,{...o,...n,ref:t}):null}).displayName=eR;var ek=n.forwardRef(({__scopeSelect:e,value:t,...r},o)=>{let i=n.useRef(null),l=(0,s.s)(o,i),a=(0,E.Z)(t);return n.useEffect(()=>{let e=i.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==t&&r){let n=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[a,t]),(0,C.jsx)(g.sG.select,{...r,style:{...S,...r.style},ref:l,defaultValue:t})});function eM(e){return""===e||void 0===e}function ej(e){let t=(0,b.c)(e),r=n.useRef(""),o=n.useRef(0),i=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),l=n.useCallback(()=>{r.current="",window.clearTimeout(o.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),[r,i,l]}function eA(e,t,r){var n,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=r?e.indexOf(r):-1,a=(n=e,o=Math.max(l,0),n.map((e,t)=>n[(o+t)%n.length]));1===i.length&&(a=a.filter(e=>e!==r));let s=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return s!==r?s:void 0}ek.displayName="SelectBubbleInput";var eP=B,eN=G,eT=K,eD=$,eL=U,eO=q,eI=eo,e_=es,eF=ec,ez=em,eB=ev,eW=ey,eG=ew,eH=eE,eK=eS},31355:(e,t,r)=>{r.d(t,{qW:()=>f});var n,o=r(43210),i=r(70569),l=r(14163),a=r(98599),s=r(35876),u=r(60687),c="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:f,onPointerDownOutside:h,onFocusOutside:v,onInteractOutside:g,onDismiss:y,...b}=e,w=o.useContext(d),[x,E]=o.useState(null),C=x?.ownerDocument??globalThis?.document,[,S]=o.useState({}),R=(0,a.s)(t,e=>E(e)),k=Array.from(w.layers),[M]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),j=k.indexOf(M),A=x?k.indexOf(x):-1,P=w.layersWithOutsidePointerEventsDisabled.size>0,N=A>=j,T=function(e,t=globalThis?.document){let r=(0,s.c)(e),n=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){m("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=n,t.addEventListener("click",i.current,{once:!0})):n()}else t.removeEventListener("click",i.current);n.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...w.branches].some(e=>e.contains(t));N&&!r&&(h?.(e),g?.(e),e.defaultPrevented||y?.())},C),D=function(e,t=globalThis?.document){let r=(0,s.c)(e),n=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!n.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;![...w.branches].some(e=>e.contains(t))&&(v?.(e),g?.(e),e.defaultPrevented||y?.())},C);return!function(e,t=globalThis?.document){let r=(0,s.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{A===w.layers.size-1&&(f?.(e),!e.defaultPrevented&&y&&(e.preventDefault(),y()))},C),o.useEffect(()=>{if(x)return r&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(n=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(x)),w.layers.add(x),p(),()=>{r&&1===w.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=n)}},[x,C,r,w]),o.useEffect(()=>()=>{x&&(w.layers.delete(x),w.layersWithOutsidePointerEventsDisabled.delete(x),p())},[x,w]),o.useEffect(()=>{let e=()=>S({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(l.sG.div,{...b,ref:R,style:{pointerEvents:P?N?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,T.onPointerDownCapture)})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function m(e,t,r,{discrete:n}){let o=r.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?(0,l.hO)(o,i):o.dispatchEvent(i)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(d),n=o.useRef(null),i=(0,a.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(l.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},32547:(e,t,r)=>{r.d(t,{n:()=>d});var n=r(43210),o=r(98599),i=r(14163),l=r(35876),a=r(60687),s="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[b,w]=n.useState(null),x=(0,l.c)(v),E=(0,l.c)(g),C=n.useRef(null),S=(0,o.s)(t,e=>w(e)),R=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(d){let e=function(e){if(R.paused||!b)return;let t=e.target;b.contains(t)?C.current=t:m(C.current,{select:!0})},t=function(e){if(R.paused||!b)return;let t=e.relatedTarget;null!==t&&(b.contains(t)||m(C.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(b)});return b&&r.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[d,b,R.paused]),n.useEffect(()=>{if(b){h.add(R);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(s,c);b.addEventListener(s,x),b.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(m(n,{select:t}),document.activeElement!==r)return}(f(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(b))}return()=>{b.removeEventListener(s,x),setTimeout(()=>{let t=new CustomEvent(u,c);b.addEventListener(u,E),b.dispatchEvent(t),t.defaultPrevented||m(e??document.body,{select:!0}),b.removeEventListener(u,E),h.remove(R)},0)}}},[b,x,E,R]);let k=n.useCallback(e=>{if(!r&&!d||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||n!==i?e.shiftKey&&n===o&&(e.preventDefault(),r&&m(i,{select:!0})):(e.preventDefault(),r&&m(o,{select:!0})):n===t&&e.preventDefault()}},[r,d,R.paused]);return(0,a.jsx)(i.sG.div,{tabIndex:-1,...y,ref:S,onKeyDown:k})});function f(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function p(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function m(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}d.displayName="FocusScope";var h=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},35876:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(43210);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},42247:(e,t,r)=>{r.d(t,{A:()=>U});var n,o,i=function(){return(i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function l(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,r(43210)),s="right-scroll-bar-position",u="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,f=new WeakMap;function p(e){return e}var m=function(e){void 0===e&&(e={});var t,r,n,o,l=(t=null,void 0===r&&(r=p),n=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,o);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){o=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var i=function(){var r=t;t=[],r.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return l.options=i({async:!0,ssr:!1},e),l}(),h=function(){},v=a.forwardRef(function(e,t){var r,n,o,s,u=a.useRef(null),p=a.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),v=p[0],g=p[1],y=e.forwardProps,b=e.children,w=e.className,x=e.removeScrollBar,E=e.enabled,C=e.shards,S=e.sideCar,R=e.noRelative,k=e.noIsolation,M=e.inert,j=e.allowPinchZoom,A=e.as,P=e.gapMode,N=l(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),T=(r=[u,t],n=function(e){return r.forEach(function(t){return c(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,s=o.facade,d(function(){var e=f.get(s);if(e){var t=new Set(e),n=new Set(r),o=s.current;t.forEach(function(e){n.has(e)||c(e,null)}),n.forEach(function(e){t.has(e)||c(e,o)})}f.set(s,r)},[r]),s),D=i(i({},N),v);return a.createElement(a.Fragment,null,E&&a.createElement(S,{sideCar:m,removeScrollBar:x,shards:C,noRelative:R,noIsolation:k,inert:M,setCallbacks:g,allowPinchZoom:!!j,lockRef:u,gapMode:P}),y?a.cloneElement(a.Children.only(b),i(i({},D),{ref:T})):a.createElement(void 0===A?"div":A,i({},D,{className:w,ref:T}),b))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:u,zeroRight:s};var g=function(e){var t=e.sideCar,r=l(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return a.createElement(n,i({},r))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||r.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=n:i.appendChild(document.createTextNode(n)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=y();return function(t,r){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},w=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},C=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(r),E(n),E(o)]},S=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=C(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},R=w(),k="data-scroll-locked",M=function(e,t,r,n){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(a,"px ").concat(n,";\n  }\n  body[").concat(k,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(a,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(s," {\n    right: ").concat(a,"px ").concat(n,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(a,"px ").concat(n,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(k,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},j=function(){var e=parseInt(document.body.getAttribute(k)||"0",10);return isFinite(e)?e:0},A=function(){a.useEffect(function(){return document.body.setAttribute(k,(j()+1).toString()),function(){var e=j()-1;e<=0?document.body.removeAttribute(k):document.body.setAttribute(k,e.toString())}},[])},P=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;A();var i=a.useMemo(function(){return S(o)},[o]);return a.createElement(R,{styles:M(i,!t,o,r?"":"!important")})},N=!1;if("undefined"!=typeof window)try{var T=Object.defineProperty({},"passive",{get:function(){return N=!0,!0}});window.addEventListener("test",T,T),window.removeEventListener("test",T,T)}catch(e){N=!1}var D=!!N&&{passive:!1},L=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},O=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),I(e,n)){var o=_(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},I=function(e,t){return"v"===e?L(t,"overflowY"):L(t,"overflowX")},_=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,r,n,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*n,s=r.target,u=t.contains(s),c=!1,d=a>0,f=0,p=0;do{if(!s)break;var m=_(e,s),h=m[0],v=m[1]-m[2]-l*h;(h||v)&&I(e,s)&&(f+=v,p+=h);var g=s.parentNode;s=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!u&&s!==document.body||u&&(t.contains(s)||t===s));return d&&(o&&1>Math.abs(f)||!o&&a>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(c=!0),c},z=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},W=function(e){return e&&"current"in e?e.current:e},G=0,H=[];let K=(n=function(e){var t=a.useRef([]),r=a.useRef([0,0]),n=a.useRef(),o=a.useState(G++)[0],i=a.useState(w)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(W),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=z(e),a=r.current,s="deltaX"in e?e.deltaX:a[0]-i[0],u="deltaY"in e?e.deltaY:a[1]-i[1],c=e.target,d=Math.abs(s)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=O(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=O(d,c)),!f)return!1;if(!n.current&&"changedTouches"in e&&(s||u)&&(n.current=o),!o)return!0;var p=n.current||o;return F(p,t,e,"h"===p?s:u,!0)},[]),u=a.useCallback(function(e){if(H.length&&H[H.length-1]===i){var r="deltaY"in e?B(e):z(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(l.current.shards||[]).map(W).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=a.useCallback(function(e,r,n,o){var i={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){r.current=z(e),n.current=void 0},[]),f=a.useCallback(function(t){c(t.type,B(t),t.target,s(t,e.lockRef.current))},[]),p=a.useCallback(function(t){c(t.type,z(t),t.target,s(t,e.lockRef.current))},[]);a.useEffect(function(){return H.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",u,D),document.addEventListener("touchmove",u,D),document.addEventListener("touchstart",d,D),function(){H=H.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,D),document.removeEventListener("touchmove",u,D),document.removeEventListener("touchstart",d,D)}},[]);var m=e.removeScrollBar,h=e.inert;return a.createElement(a.Fragment,null,h?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?a.createElement(P,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},m.useMedium(n),g);var $=a.forwardRef(function(e,t){return a.createElement(v,i({},e,{ref:t,sideCar:K}))});$.classNames=v.classNames;let U=$},45347:(e,t,r)=>{r.d(t,{H_:()=>tm,UC:()=>tc,YJ:()=>td,q7:()=>tp,VF:()=>tg,JU:()=>tf,ZL:()=>tu,z6:()=>th,hN:()=>tv,bL:()=>ta,wv:()=>ty,Pb:()=>tb,G5:()=>tx,ZP:()=>tw,l9:()=>ts});var n=r(43210),o=r(70569),i=r(98599),l=r(11273),a=r(65551),s=r(14163),u=r(9510),c=r(43),d=r(31355),f=r(1359),p=r(32547),m=r(96963),h=r(55509),v=r(25028),g=r(66156),y=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[o,i]=n.useState(),l=n.useRef(null),a=n.useRef(e),s=n.useRef("none"),[u,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=b(l.current);s.current="mounted"===u?e:"none"},[u]),(0,g.N)(()=>{let t=l.current,r=a.current;if(r!==e){let n=s.current,o=b(t);e?c("MOUNT"):"none"===o||t?.display==="none"?c("UNMOUNT"):r&&n!==o?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),(0,g.N)(()=>{if(o){let e,t=o.ownerDocument.defaultView??window,r=r=>{let n=b(l.current).includes(r.animationName);if(r.target===o&&n&&(c("ANIMATION_END"),!a.current)){let r=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=r)})}},n=e=>{e.target===o&&(s.current=b(l.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}c("ANIMATION_END")},[o,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:n.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(t),l="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),a=(0,i.s)(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||o.isPresent?n.cloneElement(l,{ref:a}):null};function b(e){return e?.animationName||"none"}y.displayName="Presence";var w=r(35876),x=r(60687),E="rovingFocusGroup.onEntryFocus",C={bubbles:!1,cancelable:!0},S="RovingFocusGroup",[R,k,M]=(0,u.N)(S),[j,A]=(0,l.A)(S,[M]),[P,N]=j(S),T=n.forwardRef((e,t)=>(0,x.jsx)(R.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,x.jsx)(R.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,x.jsx)(D,{...e,ref:t})})}));T.displayName=S;var D=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:l,loop:u=!1,dir:d,currentTabStopId:f,defaultCurrentTabStopId:p,onCurrentTabStopIdChange:m,onEntryFocus:h,preventScrollOnEntryFocus:v=!1,...g}=e,y=n.useRef(null),b=(0,i.s)(t,y),R=(0,c.jH)(d),[M,j]=(0,a.i)({prop:f,defaultProp:p??null,onChange:m,caller:S}),[A,N]=n.useState(!1),T=(0,w.c)(h),D=k(r),L=n.useRef(!1),[O,I]=n.useState(0);return n.useEffect(()=>{let e=y.current;if(e)return e.addEventListener(E,T),()=>e.removeEventListener(E,T)},[T]),(0,x.jsx)(P,{scope:r,orientation:l,dir:R,loop:u,currentTabStopId:M,onItemFocus:n.useCallback(e=>j(e),[j]),onItemShiftTab:n.useCallback(()=>N(!0),[]),onFocusableItemAdd:n.useCallback(()=>I(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>I(e=>e-1),[]),children:(0,x.jsx)(s.sG.div,{tabIndex:A||0===O?-1:0,"data-orientation":l,...g,ref:b,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!A){let t=new CustomEvent(E,C);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=D().filter(e=>e.focusable);_([e.find(e=>e.active),e.find(e=>e.id===M),...e].filter(Boolean).map(e=>e.ref.current),v)}}L.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>N(!1))})})}),L="RovingFocusGroupItem",O=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:l=!1,tabStopId:a,children:u,...c}=e,d=(0,m.B)(),f=a||d,p=N(L,r),h=p.currentTabStopId===f,v=k(r),{onFocusableItemAdd:g,onFocusableItemRemove:y,currentTabStopId:b}=p;return n.useEffect(()=>{if(i)return g(),()=>y()},[i,g,y]),(0,x.jsx)(R.ItemSlot,{scope:r,id:f,focusable:i,active:l,children:(0,x.jsx)(s.sG.span,{tabIndex:h?0:-1,"data-orientation":p.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void p.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return I[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>_(r))}}),children:"function"==typeof u?u({isCurrentTabStop:h,hasTabStop:null!=b}):u})})});O.displayName=L;var I={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function _(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var F=r(8730),z=r(63376),B=r(42247),W=["Enter"," "],G=["ArrowUp","PageDown","End"],H=["ArrowDown","PageUp","Home",...G],K={ltr:[...W,"ArrowRight"],rtl:[...W,"ArrowLeft"]},$={ltr:["ArrowLeft"],rtl:["ArrowRight"]},U="Menu",[V,q,X]=(0,u.N)(U),[Y,Z]=(0,l.A)(U,[X,h.Bk,A]),J=(0,h.Bk)(),Q=A(),[ee,et]=Y(U),[er,en]=Y(U),eo=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:i,onOpenChange:l,modal:a=!0}=e,s=J(t),[u,d]=n.useState(null),f=n.useRef(!1),p=(0,w.c)(l),m=(0,c.jH)(i);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,x.jsx)(h.bL,{...s,children:(0,x.jsx)(ee,{scope:t,open:r,onOpenChange:p,content:u,onContentChange:d,children:(0,x.jsx)(er,{scope:t,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:a,children:o})})})};eo.displayName=U;var ei=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=J(r);return(0,x.jsx)(h.Mz,{...o,...n,ref:t})});ei.displayName="MenuAnchor";var el="MenuPortal",[ea,es]=Y(el,{forceMount:void 0}),eu=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,i=et(el,t);return(0,x.jsx)(ea,{scope:t,forceMount:r,children:(0,x.jsx)(y,{present:r||i.open,children:(0,x.jsx)(v.Z,{asChild:!0,container:o,children:n})})})};eu.displayName=el;var ec="MenuContent",[ed,ef]=Y(ec),ep=n.forwardRef((e,t)=>{let r=es(ec,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,i=et(ec,e.__scopeMenu),l=en(ec,e.__scopeMenu);return(0,x.jsx)(V.Provider,{scope:e.__scopeMenu,children:(0,x.jsx)(y,{present:n||i.open,children:(0,x.jsx)(V.Slot,{scope:e.__scopeMenu,children:l.modal?(0,x.jsx)(em,{...o,ref:t}):(0,x.jsx)(eh,{...o,ref:t})})})})}),em=n.forwardRef((e,t)=>{let r=et(ec,e.__scopeMenu),l=n.useRef(null),a=(0,i.s)(t,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,z.Eq)(e)},[]),(0,x.jsx)(eg,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),eh=n.forwardRef((e,t)=>{let r=et(ec,e.__scopeMenu);return(0,x.jsx)(eg,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),ev=(0,F.TL)("MenuContent.ScrollLock"),eg=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:l=!1,trapFocus:a,onOpenAutoFocus:s,onCloseAutoFocus:u,disableOutsidePointerEvents:c,onEntryFocus:m,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:b,onDismiss:w,disableOutsideScroll:E,...C}=e,S=et(ec,r),R=en(ec,r),k=J(r),M=Q(r),j=q(r),[A,P]=n.useState(null),N=n.useRef(null),D=(0,i.s)(t,N,S.onContentChange),L=n.useRef(0),O=n.useRef(""),I=n.useRef(0),_=n.useRef(null),F=n.useRef("right"),z=n.useRef(0),W=E?B.A:n.Fragment,K=e=>{let t=O.current+e,r=j().filter(e=>!e.disabled),n=document.activeElement,o=r.find(e=>e.ref.current===n)?.textValue,i=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=r?e.indexOf(r):-1,l=(n=Math.max(i,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(l=l.filter(e=>e!==r));let a=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==r?a:void 0}(r.map(e=>e.textValue),t,o),l=r.find(e=>e.textValue===i)?.ref.current;!function e(t){O.current=t,window.clearTimeout(L.current),""!==t&&(L.current=window.setTimeout(()=>e(""),1e3))}(t),l&&setTimeout(()=>l.focus())};n.useEffect(()=>()=>window.clearTimeout(L.current),[]),(0,f.Oh)();let $=n.useCallback(e=>F.current===_.current?.side&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],s=l.x,u=l.y,c=a.x,d=a.y;u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,_.current?.area),[]);return(0,x.jsx)(ed,{scope:r,searchRef:O,onItemEnter:n.useCallback(e=>{$(e)&&e.preventDefault()},[$]),onItemLeave:n.useCallback(e=>{$(e)||(N.current?.focus(),P(null))},[$]),onTriggerLeave:n.useCallback(e=>{$(e)&&e.preventDefault()},[$]),pointerGraceTimerRef:I,onPointerGraceIntentChange:n.useCallback(e=>{_.current=e},[]),children:(0,x.jsx)(W,{...E?{as:ev,allowPinchZoom:!0}:void 0,children:(0,x.jsx)(p.n,{asChild:!0,trapped:a,onMountAutoFocus:(0,o.m)(s,e=>{e.preventDefault(),N.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,x.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:b,onDismiss:w,children:(0,x.jsx)(T,{asChild:!0,...M,dir:R.dir,orientation:"vertical",loop:l,currentTabStopId:A,onCurrentTabStopIdChange:P,onEntryFocus:(0,o.m)(m,e=>{R.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,x.jsx)(h.UC,{role:"menu","aria-orientation":"vertical","data-state":e$(S.open),"data-radix-menu-content":"",dir:R.dir,...k,...C,ref:D,style:{outline:"none",...C.style},onKeyDown:(0,o.m)(C.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&K(e.key));let o=N.current;if(e.target!==o||!H.includes(e.key))return;e.preventDefault();let i=j().filter(e=>!e.disabled).map(e=>e.ref.current);G.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(L.current),O.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eq(e=>{let t=e.target,r=z.current!==e.clientX;e.currentTarget.contains(t)&&r&&(F.current=e.clientX>z.current?"right":"left",z.current=e.clientX)}))})})})})})})});ep.displayName=ec;var ey=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,x.jsx)(s.sG.div,{role:"group",...n,ref:t})});ey.displayName="MenuGroup";var eb=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,x.jsx)(s.sG.div,{...n,ref:t})});eb.displayName="MenuLabel";var ew="MenuItem",ex="menu.itemSelect",eE=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:l,...a}=e,u=n.useRef(null),c=en(ew,e.__scopeMenu),d=ef(ew,e.__scopeMenu),f=(0,i.s)(t,u),p=n.useRef(!1);return(0,x.jsx)(eC,{...a,ref:f,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=u.current;if(!r&&e){let t=new CustomEvent(ex,{bubbles:!0,cancelable:!0});e.addEventListener(ex,e=>l?.(e),{once:!0}),(0,s.hO)(e,t),t.defaultPrevented?p.current=!1:c.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;r||t&&" "===e.key||W.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eE.displayName=ew;var eC=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:l=!1,textValue:a,...u}=e,c=ef(ew,r),d=Q(r),f=n.useRef(null),p=(0,i.s)(t,f),[m,h]=n.useState(!1),[v,g]=n.useState("");return n.useEffect(()=>{let e=f.current;e&&g((e.textContent??"").trim())},[u.children]),(0,x.jsx)(V.ItemSlot,{scope:r,disabled:l,textValue:a??v,children:(0,x.jsx)(O,{asChild:!0,...d,focusable:!l,children:(0,x.jsx)(s.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...u,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eq(e=>{l?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eq(e=>c.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>h(!0)),onBlur:(0,o.m)(e.onBlur,()=>h(!1))})})})}),eS=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...i}=e;return(0,x.jsx)(eT,{scope:e.__scopeMenu,checked:r,children:(0,x.jsx)(eE,{role:"menuitemcheckbox","aria-checked":eU(r)?"mixed":r,...i,ref:t,"data-state":eV(r),onSelect:(0,o.m)(i.onSelect,()=>n?.(!!eU(r)||!r),{checkForDefaultPrevented:!1})})})});eS.displayName="MenuCheckboxItem";var eR="MenuRadioGroup",[ek,eM]=Y(eR,{value:void 0,onValueChange:()=>{}}),ej=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,i=(0,w.c)(n);return(0,x.jsx)(ek,{scope:e.__scopeMenu,value:r,onValueChange:i,children:(0,x.jsx)(ey,{...o,ref:t})})});ej.displayName=eR;var eA="MenuRadioItem",eP=n.forwardRef((e,t)=>{let{value:r,...n}=e,i=eM(eA,e.__scopeMenu),l=r===i.value;return(0,x.jsx)(eT,{scope:e.__scopeMenu,checked:l,children:(0,x.jsx)(eE,{role:"menuitemradio","aria-checked":l,...n,ref:t,"data-state":eV(l),onSelect:(0,o.m)(n.onSelect,()=>i.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});eP.displayName=eA;var eN="MenuItemIndicator",[eT,eD]=Y(eN,{checked:!1}),eL=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,i=eD(eN,r);return(0,x.jsx)(y,{present:n||eU(i.checked)||!0===i.checked,children:(0,x.jsx)(s.sG.span,{...o,ref:t,"data-state":eV(i.checked)})})});eL.displayName=eN;var eO=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,x.jsx)(s.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});eO.displayName="MenuSeparator";var eI=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=J(r);return(0,x.jsx)(h.i3,{...o,...n,ref:t})});eI.displayName="MenuArrow";var e_="MenuSub",[eF,ez]=Y(e_),eB=e=>{let{__scopeMenu:t,children:r,open:o=!1,onOpenChange:i}=e,l=et(e_,t),a=J(t),[s,u]=n.useState(null),[c,d]=n.useState(null),f=(0,w.c)(i);return n.useEffect(()=>(!1===l.open&&f(!1),()=>f(!1)),[l.open,f]),(0,x.jsx)(h.bL,{...a,children:(0,x.jsx)(ee,{scope:t,open:o,onOpenChange:f,content:c,onContentChange:d,children:(0,x.jsx)(eF,{scope:t,contentId:(0,m.B)(),triggerId:(0,m.B)(),trigger:s,onTriggerChange:u,children:r})})})};eB.displayName=e_;var eW="MenuSubTrigger",eG=n.forwardRef((e,t)=>{let r=et(eW,e.__scopeMenu),l=en(eW,e.__scopeMenu),a=ez(eW,e.__scopeMenu),s=ef(eW,e.__scopeMenu),u=n.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=s,f={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,x.jsx)(ei,{asChild:!0,...f,children:(0,x.jsx)(eC,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":a.contentId,"data-state":e$(r.open),...e,ref:(0,i.t)(t,a.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eq(t=>{s.onItemEnter(t),!t.defaultPrevented&&(e.disabled||r.open||u.current||(s.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eq(e=>{p();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,o="right"===n,i=t[o?"left":"right"],l=t[o?"right":"left"];s.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:i,y:t.top},{x:l,y:t.top},{x:l,y:t.bottom},{x:i,y:t.bottom}],side:n}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(e),e.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let n=""!==s.searchRef.current;e.disabled||n&&" "===t.key||K[l.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});eG.displayName=eW;var eH="MenuSubContent",eK=n.forwardRef((e,t)=>{let r=es(ec,e.__scopeMenu),{forceMount:l=r.forceMount,...a}=e,s=et(ec,e.__scopeMenu),u=en(ec,e.__scopeMenu),c=ez(eH,e.__scopeMenu),d=n.useRef(null),f=(0,i.s)(t,d);return(0,x.jsx)(V.Provider,{scope:e.__scopeMenu,children:(0,x.jsx)(y,{present:l||s.open,children:(0,x.jsx)(V.Slot,{scope:e.__scopeMenu,children:(0,x.jsx)(eg,{id:c.contentId,"aria-labelledby":c.triggerId,...a,ref:f,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{u.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=$[u.dir].includes(e.key);t&&r&&(s.onOpenChange(!1),c.trigger?.focus(),e.preventDefault())})})})})})});function e$(e){return e?"open":"closed"}function eU(e){return"indeterminate"===e}function eV(e){return eU(e)?"indeterminate":e?"checked":"unchecked"}function eq(e){return t=>"mouse"===t.pointerType?e(t):void 0}eK.displayName=eH;var eX="DropdownMenu",[eY,eZ]=(0,l.A)(eX,[Z]),eJ=Z(),[eQ,e0]=eY(eX),e1=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:i,defaultOpen:l,onOpenChange:s,modal:u=!0}=e,c=eJ(t),d=n.useRef(null),[f,p]=(0,a.i)({prop:i,defaultProp:l??!1,onChange:s,caller:eX});return(0,x.jsx)(eQ,{scope:t,triggerId:(0,m.B)(),triggerRef:d,contentId:(0,m.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:u,children:(0,x.jsx)(eo,{...c,open:f,onOpenChange:p,dir:o,modal:u,children:r})})};e1.displayName=eX;var e2="DropdownMenuTrigger",e6=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...l}=e,a=e0(e2,r),u=eJ(r);return(0,x.jsx)(ei,{asChild:!0,...u,children:(0,x.jsx)(s.sG.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...l,ref:(0,i.t)(t,a.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(a.onOpenToggle(),a.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&a.onOpenToggle(),"ArrowDown"===e.key&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e6.displayName=e2;var e3=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eJ(t);return(0,x.jsx)(eu,{...n,...r})};e3.displayName="DropdownMenuPortal";var e5="DropdownMenuContent",e9=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...i}=e,l=e0(e5,r),a=eJ(r),s=n.useRef(!1);return(0,x.jsx)(ep,{id:l.contentId,"aria-labelledby":l.triggerId,...a,...i,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{s.current||l.triggerRef.current?.focus(),s.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!l.modal||n)&&(s.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e9.displayName=e5;var e8=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(ey,{...o,...n,ref:t})});e8.displayName="DropdownMenuGroup";var e4=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eb,{...o,...n,ref:t})});e4.displayName="DropdownMenuLabel";var e7=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eE,{...o,...n,ref:t})});e7.displayName="DropdownMenuItem";var te=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eS,{...o,...n,ref:t})});te.displayName="DropdownMenuCheckboxItem";var tt=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(ej,{...o,...n,ref:t})});tt.displayName="DropdownMenuRadioGroup";var tr=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eP,{...o,...n,ref:t})});tr.displayName="DropdownMenuRadioItem";var tn=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eL,{...o,...n,ref:t})});tn.displayName="DropdownMenuItemIndicator";var to=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eO,{...o,...n,ref:t})});to.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eI,{...o,...n,ref:t})}).displayName="DropdownMenuArrow";var ti=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eG,{...o,...n,ref:t})});ti.displayName="DropdownMenuSubTrigger";var tl=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eK,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});tl.displayName="DropdownMenuSubContent";var ta=e1,ts=e6,tu=e3,tc=e9,td=e8,tf=e4,tp=e7,tm=te,th=tt,tv=tr,tg=tn,ty=to,tb=e=>{let{__scopeDropdownMenu:t,children:r,open:n,onOpenChange:o,defaultOpen:i}=e,l=eJ(t),[s,u]=(0,a.i)({prop:n,defaultProp:i??!1,onChange:o,caller:"DropdownMenuSub"});return(0,x.jsx)(eB,{...l,open:s,onOpenChange:u,children:r})},tw=ti,tx=tl},49384:(e,t,r)=>{r.d(t,{$:()=>n});function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(r=0;r<i;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=t);return n}},55509:(e,t,r)=>{r.d(t,{Mz:()=>eY,i3:()=>eJ,UC:()=>eZ,bL:()=>eX,Bk:()=>eD});var n=r(43210);let o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,s=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>d[e])}function b(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function w(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function E(e,t,r){let n,{reference:o,floating:i}=e,l=g(t),a=h(g(t)),s=v(a),u=p(t),c="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,y=o[s]/2-i[s]/2;switch(u){case"top":n={x:d,y:o.y-i.height};break;case"bottom":n={x:d,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-i.width,y:f};break;default:n={x:o.x,y:o.y}}switch(m(t)){case"start":n[a]-=y*(r&&c?-1:1);break;case"end":n[a]+=y*(r&&c?-1:1)}return n}let C=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:i=[],platform:l}=r,a=i.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=E(u,n,s),f=n,p={},m=0;for(let r=0;r<a.length;r++){let{name:i,fn:h}=a[r],{x:v,y:g,data:y,reset:b}=await h({x:c,y:d,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},b&&m<=50&&(m++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(u=!0===b.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:c,y:d}=E(u,f,s)),r=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function S(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:i,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:m=0}=f(t,e),h=w(m),v=a[p?"floating"===d?"reference":"floating":d],g=x(await i.getClippingRect({element:null==(r=await (null==i.isElement?void 0:i.isElement(v)))||r?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),y="floating"===d?{x:n,y:o,width:l.floating.width,height:l.floating.height}:l.reference,b=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),E=await (null==i.isElement?void 0:i.isElement(b))&&await (null==i.getScale?void 0:i.getScale(b))||{x:1,y:1},C=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:b,strategy:s}):y);return{top:(g.top-C.top+h.top)/E.y,bottom:(C.bottom-g.bottom+h.bottom)/E.y,left:(g.left-C.left+h.left)/E.x,right:(C.right-g.right+h.right)/E.x}}function R(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function k(e){return o.some(t=>e[t]>=0)}async function M(e,t){let{placement:r,platform:n,elements:o}=e,i=await (null==n.isRTL?void 0:n.isRTL(o.floating)),l=p(r),a=m(r),s="y"===g(r),u=["left","top"].includes(l)?-1:1,c=i&&s?-1:1,d=f(t,e),{mainAxis:h,crossAxis:v,alignmentAxis:y}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof y&&(v="end"===a?-1*y:y),s?{x:v*c,y:h*u}:{x:h*u,y:v*c}}function j(){return"undefined"!=typeof window}function A(e){return T(e)?(e.nodeName||"").toLowerCase():"#document"}function P(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function N(e){var t;return null==(t=(T(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function T(e){return!!j()&&(e instanceof Node||e instanceof P(e).Node)}function D(e){return!!j()&&(e instanceof Element||e instanceof P(e).Element)}function L(e){return!!j()&&(e instanceof HTMLElement||e instanceof P(e).HTMLElement)}function O(e){return!!j()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof P(e).ShadowRoot)}function I(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=W(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(o)}function _(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function F(e){let t=z(),r=D(e)?W(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function B(e){return["html","body","#document"].includes(A(e))}function W(e){return P(e).getComputedStyle(e)}function G(e){return D(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function H(e){if("html"===A(e))return e;let t=e.assignedSlot||e.parentNode||O(e)&&e.host||N(e);return O(t)?t.host:t}function K(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=H(t);return B(r)?t.ownerDocument?t.ownerDocument.body:t.body:L(r)&&I(r)?r:e(r)}(e),i=o===(null==(n=e.ownerDocument)?void 0:n.body),l=P(o);if(i){let e=$(l);return t.concat(l,l.visualViewport||[],I(o)?o:[],e&&r?K(e):[])}return t.concat(o,K(o,[],r))}function $(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function U(e){let t=W(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=L(e),i=o?e.offsetWidth:r,l=o?e.offsetHeight:n,s=a(r)!==i||a(n)!==l;return s&&(r=i,n=l),{width:r,height:n,$:s}}function V(e){return D(e)?e:e.contextElement}function q(e){let t=V(e);if(!L(t))return u(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:i}=U(t),l=(i?a(r.width):r.width)/n,s=(i?a(r.height):r.height)/o;return l&&Number.isFinite(l)||(l=1),s&&Number.isFinite(s)||(s=1),{x:l,y:s}}let X=u(0);function Y(e){let t=P(e);return z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:X}function Z(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let i=e.getBoundingClientRect(),l=V(e),a=u(1);t&&(n?D(n)&&(a=q(n)):a=q(e));let s=(void 0===(o=r)&&(o=!1),n&&(!o||n===P(l))&&o)?Y(l):u(0),c=(i.left+s.x)/a.x,d=(i.top+s.y)/a.y,f=i.width/a.x,p=i.height/a.y;if(l){let e=P(l),t=n&&D(n)?P(n):n,r=e,o=$(r);for(;o&&n&&t!==r;){let e=q(o),t=o.getBoundingClientRect(),n=W(o),i=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=i,d+=l,o=$(r=P(o))}}return x({width:f,height:p,x:c,y:d})}function J(e,t){let r=G(e).scrollLeft;return t?t.left+r:Z(N(e)).left+r}function Q(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:J(e,n)),y:n.top+t.scrollTop}}function ee(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=P(e),n=N(e),o=r.visualViewport,i=n.clientWidth,l=n.clientHeight,a=0,s=0;if(o){i=o.width,l=o.height;let e=z();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:i,height:l,x:a,y:s}}(e,r);else if("document"===t)n=function(e){let t=N(e),r=G(e),n=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),i=l(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),a=-r.scrollLeft+J(e),s=-r.scrollTop;return"rtl"===W(n).direction&&(a+=l(t.clientWidth,n.clientWidth)-o),{width:o,height:i,x:a,y:s}}(N(e));else if(D(t))n=function(e,t){let r=Z(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,i=L(e)?q(e):u(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:n*i.y}}(t,r);else{let r=Y(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return x(n)}function et(e){return"static"===W(e).position}function er(e,t){if(!L(e)||"fixed"===W(e).position)return null;if(t)return t(e);let r=e.offsetParent;return N(e)===r&&(r=r.ownerDocument.body),r}function en(e,t){let r=P(e);if(_(e))return r;if(!L(e)){let t=H(e);for(;t&&!B(t);){if(D(t)&&!et(t))return t;t=H(t)}return r}let n=er(e,t);for(;n&&["table","td","th"].includes(A(n))&&et(n);)n=er(n,t);return n&&B(n)&&et(n)&&!F(n)?r:n||function(e){let t=H(e);for(;L(t)&&!B(t);){if(F(t))return t;if(_(t))break;t=H(t)}return null}(e)||r}let eo=async function(e){let t=this.getOffsetParent||en,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=L(t),o=N(t),i="fixed"===r,l=Z(e,!0,i,t),a={scrollLeft:0,scrollTop:0},s=u(0);if(n||!n&&!i)if(("body"!==A(t)||I(o))&&(a=G(t)),n){let e=Z(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=J(o));i&&!n&&o&&(s.x=J(o));let c=!o||n||i?u(0):Q(o,a);return{x:l.left+a.scrollLeft-s.x-c.x,y:l.top+a.scrollTop-s.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,i="fixed"===o,l=N(n),a=!!t&&_(t.floating);if(n===l||a&&i)return r;let s={scrollLeft:0,scrollTop:0},c=u(1),d=u(0),f=L(n);if((f||!f&&!i)&&(("body"!==A(n)||I(l))&&(s=G(n)),L(n))){let e=Z(n);c=q(n),d.x=e.x+n.clientLeft,d.y=e.y+n.clientTop}let p=!l||f||i?u(0):Q(l,s,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-s.scrollLeft*c.x+d.x+p.x,y:r.y*c.y-s.scrollTop*c.y+d.y+p.y}},getDocumentElement:N,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,a=[..."clippingAncestors"===r?_(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=K(e,[],!1).filter(e=>D(e)&&"body"!==A(e)),o=null,i="fixed"===W(e).position,l=i?H(e):e;for(;D(l)&&!B(l);){let t=W(l),r=F(l);r||"fixed"!==t.position||(o=null),(i?!r&&!o:!r&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||I(l)&&!r&&function e(t,r){let n=H(t);return!(n===r||!D(n)||B(n))&&("fixed"===W(n).position||e(n,r))}(e,l))?n=n.filter(e=>e!==l):o=t,l=H(l)}return t.set(e,n),n}(t,this._c):[].concat(r),n],s=a[0],u=a.reduce((e,r)=>{let n=ee(t,r,o);return e.top=l(n.top,e.top),e.right=i(n.right,e.right),e.bottom=i(n.bottom,e.bottom),e.left=l(n.left,e.left),e},ee(t,s,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:en,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=U(e);return{width:t,height:r}},getScale:q,isElement:D,isRTL:function(e){return"rtl"===W(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:a,platform:s,elements:u,middlewareData:c}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let y=w(p),b={x:r,y:n},x=h(g(o)),E=v(x),C=await s.getDimensions(d),S="y"===x,R=S?"clientHeight":"clientWidth",k=a.reference[E]+a.reference[x]-b[x]-a.floating[E],M=b[x]-a.reference[x],j=await (null==s.getOffsetParent?void 0:s.getOffsetParent(d)),A=j?j[R]:0;A&&await (null==s.isElement?void 0:s.isElement(j))||(A=u.floating[R]||a.floating[E]);let P=A/2-C[E]/2-1,N=i(y[S?"top":"left"],P),T=i(y[S?"bottom":"right"],P),D=A-C[E]-T,L=A/2-C[E]/2+(k/2-M/2),O=l(N,i(L,D)),I=!c.arrow&&null!=m(o)&&L!==O&&a.reference[E]/2-(L<N?N:T)-C[E]/2<0,_=I?L<N?L-N:L-D:0;return{[x]:b[x]+_,data:{[x]:O,centerOffset:L-O-_,...I&&{alignmentOffset:_}},reset:I}}}),es=(e,t,r)=>{let n=new Map,o={platform:ei,...r},i={...o.platform,_c:n};return C(e,t,{...o,platform:i})};var eu=r(51215),ec="undefined"!=typeof document?n.useLayoutEffect:function(){};function ed(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!ed(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!ed(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let r=ef(e);return Math.round(t*r)/r}function em(e){let t=n.useRef(e);return ec(()=>{t.current=e}),t}let eh=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?ea({element:r.current,padding:n}).fn(t):{}:r?ea({element:r,padding:n}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:i,placement:l,middlewareData:a}=t,s=await M(t,e);return l===(null==(r=a.offset)?void 0:r.placement)&&null!=(n=a.arrow)&&n.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:l}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:a=!0,crossAxis:s=!1,limiter:u={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...c}=f(e,t),d={x:r,y:n},m=await S(t,c),v=g(p(o)),y=h(v),b=d[y],w=d[v];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",r=b+m[e],n=b-m[t];b=l(r,i(b,n))}if(s){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",r=w+m[e],n=w-m[t];w=l(r,i(w,n))}let x=u.fn({...t,[y]:b,[v]:w});return{...x,data:{x:x.x-r,y:x.y-n,enabled:{[y]:a,[v]:s}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=f(e,t),c={x:r,y:n},d=g(o),m=h(d),v=c[m],y=c[d],b=f(a,t),w="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(s){let e="y"===m?"height":"width",t=i.reference[m]-i.floating[e]+w.mainAxis,r=i.reference[m]+i.reference[e]-w.mainAxis;v<t?v=t:v>r&&(v=r)}if(u){var x,E;let e="y"===m?"width":"height",t=["top","left"].includes(p(o)),r=i.reference[d]-i.floating[e]+(t&&(null==(x=l.offset)?void 0:x[d])||0)+(t?0:w.crossAxis),n=i.reference[d]+i.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[d])||0)-(t?w.crossAxis:0);y<r?y=r:y>n&&(y=n)}return{[m]:v,[d]:y}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,i,l;let{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:d,elements:w}=t,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:C,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:k="none",flipAlignment:M=!0,...j}=f(e,t);if(null!=(r=s.arrow)&&r.alignmentOffset)return{};let A=p(a),P=g(c),N=p(c)===c,T=await (null==d.isRTL?void 0:d.isRTL(w.floating)),D=C||(N||!M?[b(c)]:function(e){let t=b(e);return[y(e),t,y(t)]}(c)),L="none"!==k;!C&&L&&D.push(...function(e,t,r,n){let o=m(e),i=function(e,t,r){let n=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(r)return t?o:n;return t?n:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===r,n);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(c,M,k,T));let O=[c,...D],I=await S(t,j),_=[],F=(null==(n=s.flip)?void 0:n.overflows)||[];if(x&&_.push(I[A]),E){let e=function(e,t,r){void 0===r&&(r=!1);let n=m(e),o=h(g(e)),i=v(o),l="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=b(l)),[l,b(l)]}(a,u,T);_.push(I[e[0]],I[e[1]])}if(F=[...F,{placement:a,overflows:_}],!_.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=O[e];if(t&&("alignment"!==E||P===g(t)||F.every(e=>e.overflows[0]>0&&g(e.placement)===P)))return{data:{index:e,overflows:F},reset:{placement:t}};let r=null==(i=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!r)switch(R){case"bestFit":{let e=null==(l=F.filter(e=>{if(L){let t=g(e.placement);return t===P||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(r=e);break}case"initialPlacement":r=c}if(a!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,a,{placement:s,rects:u,platform:c,elements:d}=t,{apply:h=()=>{},...v}=f(e,t),y=await S(t,v),b=p(s),w=m(s),x="y"===g(s),{width:E,height:C}=u.floating;"top"===b||"bottom"===b?(o=b,a=w===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(a=b,o="end"===w?"top":"bottom");let R=C-y.top-y.bottom,k=E-y.left-y.right,M=i(C-y[o],R),j=i(E-y[a],k),A=!t.middlewareData.shift,P=M,N=j;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(N=k),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(P=R),A&&!w){let e=l(y.left,0),t=l(y.right,0),r=l(y.top,0),n=l(y.bottom,0);x?N=E-2*(0!==e||0!==t?e+t:l(y.left,y.right)):P=C-2*(0!==r||0!==n?r+n:l(y.top,y.bottom))}await h({...t,availableWidth:N,availableHeight:P});let T=await c.getDimensions(d.floating);return E!==T.width||C!==T.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=f(e,t);switch(n){case"referenceHidden":{let e=R(await S(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:k(e)}}}case"escaped":{let e=R(await S(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:k(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...eh(e),options:[e,t]});var eC=r(14163),eS=r(60687),eR=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,eS.jsx)(eC.sG.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,eS.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eR.displayName="Arrow";var ek=r(98599),eM=r(11273),ej=r(35876),eA=r(66156),eP=r(18853),eN="Popper",[eT,eD]=(0,eM.A)(eN),[eL,eO]=eT(eN),eI=e=>{let{__scopePopper:t,children:r}=e,[o,i]=n.useState(null);return(0,eS.jsx)(eL,{scope:t,anchor:o,onAnchorChange:i,children:r})};eI.displayName=eN;var e_="PopperAnchor",eF=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...i}=e,l=eO(e_,r),a=n.useRef(null),s=(0,ek.s)(t,a);return n.useEffect(()=>{l.onAnchorChange(o?.current||a.current)}),o?null:(0,eS.jsx)(eC.sG.div,{...i,ref:s})});eF.displayName=e_;var ez="PopperContent",[eB,eW]=eT(ez),eG=n.forwardRef((e,t)=>{let{__scopePopper:r,side:o="bottom",sideOffset:a=0,align:u="center",alignOffset:c=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:m=0,sticky:h="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...b}=e,w=eO(ez,r),[x,E]=n.useState(null),C=(0,ek.s)(t,e=>E(e)),[S,R]=n.useState(null),k=(0,eP.X)(S),M=k?.width??0,j=k?.height??0,A="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},P=Array.isArray(p)?p:[p],T=P.length>0,D={padding:A,boundary:P.filter(eU),altBoundary:T},{refs:L,floatingStyles:O,placement:I,isPositioned:_,middlewareData:F}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[d,f]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=n.useState(o);ed(p,o)||m(o);let[h,v]=n.useState(null),[g,y]=n.useState(null),b=n.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),w=n.useCallback(e=>{e!==S.current&&(S.current=e,y(e))},[]),x=l||h,E=a||g,C=n.useRef(null),S=n.useRef(null),R=n.useRef(d),k=null!=u,M=em(u),j=em(i),A=em(c),P=n.useCallback(()=>{if(!C.current||!S.current)return;let e={placement:t,strategy:r,middleware:p};j.current&&(e.platform=j.current),es(C.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==A.current};N.current&&!ed(R.current,t)&&(R.current=t,eu.flushSync(()=>{f(t)}))})},[p,t,r,j,A]);ec(()=>{!1===c&&R.current.isPositioned&&(R.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let N=n.useRef(!1);ec(()=>(N.current=!0,()=>{N.current=!1}),[]),ec(()=>{if(x&&(C.current=x),E&&(S.current=E),x&&E){if(M.current)return M.current(x,E,P);P()}},[x,E,P,M,k]);let T=n.useMemo(()=>({reference:C,floating:S,setReference:b,setFloating:w}),[b,w]),D=n.useMemo(()=>({reference:x,floating:E}),[x,E]),L=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!D.floating)return e;let t=ep(D.floating,d.x),n=ep(D.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+n+"px)",...ef(D.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,s,D.floating,d.x,d.y]);return n.useMemo(()=>({...d,update:P,refs:T,elements:D,floatingStyles:L}),[d,P,T,D,L])}({strategy:"fixed",placement:o+("center"!==u?"-"+u:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=n,p=V(e),m=a||u?[...p?K(p):[],...K(t)]:[];m.forEach(e=>{a&&e.addEventListener("scroll",r,{passive:!0}),u&&e.addEventListener("resize",r)});let h=p&&d?function(e,t){let r,n=null,o=N(e);function a(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),a();let f=e.getBoundingClientRect(),{left:p,top:m,width:h,height:v}=f;if(c||t(),!h||!v)return;let g=s(m),y=s(o.clientWidth-(p+h)),b={rootMargin:-g+"px "+-y+"px "+-s(o.clientHeight-(m+v))+"px "+-s(p)+"px",threshold:l(0,i(1,d))||1},w=!0;function x(t){let n=t[0].intersectionRatio;if(n!==d){if(!w)return u();n?u(!1,n):r=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==n||el(f,e.getBoundingClientRect())||u(),w=!1}try{n=new IntersectionObserver(x,{...b,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(x,b)}n.observe(e)}(!0),a}(p,r):null,v=-1,g=null;c&&(g=new ResizeObserver(e=>{let[n]=e;n&&n.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),r()}),p&&!f&&g.observe(p),g.observe(t));let y=f?Z(e):null;return f&&function t(){let n=Z(e);y&&!el(y,n)&&r(),y=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;m.forEach(e=>{a&&e.removeEventListener("scroll",r),u&&e.removeEventListener("resize",r)}),null==h||h(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:w.anchor},middleware:[ev({mainAxis:a+j,alignmentAxis:c}),f&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?ey():void 0,...D}),f&&eb({...D}),ew({...D,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${r}px`),l.setProperty("--radix-popper-available-height",`${n}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),S&&eE({element:S,padding:d}),eV({arrowWidth:M,arrowHeight:j}),v&&ex({strategy:"referenceHidden",...D})]}),[z,B]=eq(I),W=(0,ej.c)(y);(0,eA.N)(()=>{_&&W?.()},[_,W]);let G=F.arrow?.x,H=F.arrow?.y,$=F.arrow?.centerOffset!==0,[U,q]=n.useState();return(0,eA.N)(()=>{x&&q(window.getComputedStyle(x).zIndex)},[x]),(0,eS.jsx)("div",{ref:L.setFloating,"data-radix-popper-content-wrapper":"",style:{...O,transform:_?O.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:U,"--radix-popper-transform-origin":[F.transformOrigin?.x,F.transformOrigin?.y].join(" "),...F.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eS.jsx)(eB,{scope:r,placedSide:z,onArrowChange:R,arrowX:G,arrowY:H,shouldHideArrow:$,children:(0,eS.jsx)(eC.sG.div,{"data-side":z,"data-align":B,...b,ref:C,style:{...b.style,animation:_?void 0:"none"}})})})});eG.displayName=ez;var eH="PopperArrow",eK={top:"bottom",right:"left",bottom:"top",left:"right"},e$=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=eW(eH,r),i=eK[o.placedSide];return(0,eS.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eS.jsx)(eR,{...n,ref:t,style:{...n.style,display:"block"}})})});function eU(e){return null!==e}e$.displayName=eH;var eV=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[s,u]=eq(r),c={start:"0%",center:"50%",end:"100%"}[u],d=(o.arrow?.x??0)+l/2,f=(o.arrow?.y??0)+a/2,p="",m="";return"bottom"===s?(p=i?c:`${d}px`,m=`${-a}px`):"top"===s?(p=i?c:`${d}px`,m=`${n.floating.height+a}px`):"right"===s?(p=`${-a}px`,m=i?c:`${f}px`):"left"===s&&(p=`${n.floating.width+a}px`,m=i?c:`${f}px`),{data:{x:p,y:m}}}});function eq(e){let[t,r="center"]=e.split("-");return[t,r]}var eX=eI,eY=eF,eZ=eG,eJ=e$},62688:(e,t,r)=>{r.d(t,{A:()=>s});var n=r(43210);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:a="",children:s,iconNode:u,...c},d)=>(0,n.createElement)("svg",{ref:d,...l,width:t,height:t,stroke:e,strokeWidth:o?24*Number(r)/Number(t):r,className:i("lucide",a),...c},[...u.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(s)?s:[s]])),s=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...l},s)=>(0,n.createElement)(a,{ref:s,iconNode:t,className:i(`lucide-${o(e)}`,r),...l}));return r.displayName=`${e}`,r}},63376:(e,t,r)=>{r.d(t,{Eq:()=>c});var n=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,l={},a=0,s=function(e){return e&&(e.host||s(e.parentNode))},u=function(e,t,r,n){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=s(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});l[r]||(l[r]=new WeakMap);var c=l[r],d=[],f=new Set,p=new Set(u),m=function(e){!e||f.has(e)||(f.add(e),m(e.parentNode))};u.forEach(m);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(n),l=null!==t&&"false"!==t,a=(o.get(e)||0)+1,s=(c.get(e)||0)+1;o.set(e,a),c.set(e,s),d.push(e),1===a&&l&&i.set(e,!0),1===s&&e.setAttribute(r,"true"),l||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),a++,function(){d.forEach(function(e){var t=o.get(e)-1,l=c.get(e)-1;o.set(e,t),c.set(e,l),t||(i.has(e)||e.removeAttribute(n),i.delete(e)),l||e.removeAttribute(r)}),--a||(o=new WeakMap,o=new WeakMap,i=new WeakMap,l={})}},c=function(e,t,r){void 0===r&&(r="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||n(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),u(o,i,r,"aria-hidden")):function(){return null}}},64398:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},65551:(e,t,r)=>{r.d(t,{i:()=>a});var n,o=r(43210),i=r(66156),l=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function a({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[i,a,s]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),i=o.useRef(r),a=o.useRef(t);return l(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==r&&(a.current?.(r),i.current=r)},[r,i]),[r,n,a]}({defaultProp:t,onChange:r}),u=void 0!==e,c=u?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,n])}return[c,o.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&s.current?.(r)}else a(t)},[u,e,a,s])]}Symbol("RADIX:SYNC_STATE")},65822:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},66156:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(43210),o=globalThis?.document?n.useLayoutEffect:()=>{}},67969:(e,t,r)=>{r.d(t,{q:()=>n});function n(e,[t,r]){return Math.min(r,Math.max(t,e))}},70569:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},78272:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},82348:(e,t,r)=>{r.d(t,{QP:()=>X});let n=e=>{let t=a(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),o(r,t)||l(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),i=n?o(e.slice(1),n):void 0;if(i)return i;if(0===t.validators.length)return;let l=e.join("-");return t.validators.find(({validator:e})=>e(l))?.classGroupId},i=/^\[(.+)\]$/,l=e=>{if(i.test(e)){let t=i.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},a=e=>{let{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return d(Object.entries(e.classGroups),r).forEach(([e,r])=>{s(r,n,e,t)}),n},s=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e)return c(e)?void s(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,o])=>{s(o,u(t,e),r,n)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,d=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,i)=>{r.set(o,i),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},p=e=>{let{separator:t,experimentalParseClassName:r}=e,n=1===t.length,o=t[0],i=t.length,l=e=>{let r,l=[],a=0,s=0;for(let u=0;u<e.length;u++){let c=e[u];if(0===a){if(c===o&&(n||e.slice(u,u+i)===t)){l.push(e.slice(s,u)),s=u+i;continue}if("/"===c){r=u;continue}}"["===c?a++:"]"===c&&a--}let u=0===l.length?e:e.substring(s),c=u.startsWith("!"),d=c?u.substring(1):u;return{modifiers:l,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:r&&r>s?r-s:void 0}};return r?e=>r({className:e,parseClassName:l}):l},m=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},h=e=>({cache:f(e.cacheSize),parseClassName:p(e),...n(e)}),v=/\s+/,g=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o}=t,i=[],l=e.trim().split(v),a="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{modifiers:s,hasImportantModifier:u,baseClassName:c,maybePostfixModifierPosition:d}=r(t),f=!!d,p=n(f?c.substring(0,d):c);if(!p){if(!f||!(p=n(c))){a=t+(a.length>0?" "+a:a);continue}f=!1}let h=m(s).join(":"),v=u?h+"!":h,g=v+p;if(i.includes(g))continue;i.push(g);let y=o(p,f);for(let e=0;e<y.length;++e){let t=y[e];i.push(v+t)}a=t+(a.length>0?" "+a:a)}return a};function y(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=b(e))&&(n&&(n+=" "),n+=t);return n}let b=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=b(e[n]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:([a-z-]+):)?(.+)\]$/i,E=/^\d+\/\d+$/,C=new Set(["px","full","screen"]),S=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,R=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,k=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,M=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,j=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,A=e=>N(e)||C.has(e)||E.test(e),P=e=>K(e,"length",$),N=e=>!!e&&!Number.isNaN(Number(e)),T=e=>K(e,"number",N),D=e=>!!e&&Number.isInteger(Number(e)),L=e=>e.endsWith("%")&&N(e.slice(0,-1)),O=e=>x.test(e),I=e=>S.test(e),_=new Set(["length","size","percentage"]),F=e=>K(e,_,U),z=e=>K(e,"position",U),B=new Set(["image","url"]),W=e=>K(e,B,q),G=e=>K(e,"",V),H=()=>!0,K=(e,t,r)=>{let n=x.exec(e);return!!n&&(n[1]?"string"==typeof t?n[1]===t:t.has(n[1]):r(n[2]))},$=e=>R.test(e)&&!k.test(e),U=()=>!1,V=e=>M.test(e),q=e=>j.test(e);Symbol.toStringTag;let X=function(e,...t){let r,n,o,i=function(a){return n=(r=h(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,i=l,l(a)};function l(e){let t=n(e);if(t)return t;let i=g(e,r);return o(e,i),i}return function(){return i(y.apply(null,arguments))}}(()=>{let e=w("colors"),t=w("spacing"),r=w("blur"),n=w("brightness"),o=w("borderColor"),i=w("borderRadius"),l=w("borderSpacing"),a=w("borderWidth"),s=w("contrast"),u=w("grayscale"),c=w("hueRotate"),d=w("invert"),f=w("gap"),p=w("gradientColorStops"),m=w("gradientColorStopPositions"),h=w("inset"),v=w("margin"),g=w("opacity"),y=w("padding"),b=w("saturate"),x=w("scale"),E=w("sepia"),C=w("skew"),S=w("space"),R=w("translate"),k=()=>["auto","contain","none"],M=()=>["auto","hidden","clip","visible","scroll"],j=()=>["auto",O,t],_=()=>[O,t],B=()=>["",A,P],K=()=>["auto",N,O],$=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],U=()=>["solid","dashed","dotted","double","none"],V=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],q=()=>["start","end","center","between","around","evenly","stretch"],X=()=>["","0",O],Y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Z=()=>[N,O];return{cacheSize:500,separator:":",theme:{colors:[H],spacing:[A,P],blur:["none","",I,O],brightness:Z(),borderColor:[e],borderRadius:["none","","full",I,O],borderSpacing:_(),borderWidth:B(),contrast:Z(),grayscale:X(),hueRotate:Z(),invert:X(),gap:_(),gradientColorStops:[e],gradientColorStopPositions:[L,P],inset:j(),margin:j(),opacity:Z(),padding:_(),saturate:Z(),scale:Z(),sepia:X(),skew:Z(),space:_(),translate:_()},classGroups:{aspect:[{aspect:["auto","square","video",O]}],container:["container"],columns:[{columns:[I]}],"break-after":[{"break-after":Y()}],"break-before":[{"break-before":Y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...$(),O]}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:k()}],"overscroll-x":[{"overscroll-x":k()}],"overscroll-y":[{"overscroll-y":k()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",D,O]}],basis:[{basis:j()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",O]}],grow:[{grow:X()}],shrink:[{shrink:X()}],order:[{order:["first","last","none",D,O]}],"grid-cols":[{"grid-cols":[H]}],"col-start-end":[{col:["auto",{span:["full",D,O]},O]}],"col-start":[{"col-start":K()}],"col-end":[{"col-end":K()}],"grid-rows":[{"grid-rows":[H]}],"row-start-end":[{row:["auto",{span:[D,O]},O]}],"row-start":[{"row-start":K()}],"row-end":[{"row-end":K()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",O]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",O]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...q()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...q(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...q(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[y]}],px:[{px:[y]}],py:[{py:[y]}],ps:[{ps:[y]}],pe:[{pe:[y]}],pt:[{pt:[y]}],pr:[{pr:[y]}],pb:[{pb:[y]}],pl:[{pl:[y]}],m:[{m:[v]}],mx:[{mx:[v]}],my:[{my:[v]}],ms:[{ms:[v]}],me:[{me:[v]}],mt:[{mt:[v]}],mr:[{mr:[v]}],mb:[{mb:[v]}],ml:[{ml:[v]}],"space-x":[{"space-x":[S]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[S]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",O,t]}],"min-w":[{"min-w":[O,t,"min","max","fit"]}],"max-w":[{"max-w":[O,t,"none","full","min","max","fit","prose",{screen:[I]},I]}],h:[{h:[O,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[O,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[O,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[O,t,"auto","min","max","fit"]}],"font-size":[{text:["base",I,P]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",T]}],"font-family":[{font:[H]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",O]}],"line-clamp":[{"line-clamp":["none",N,T]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",A,O]}],"list-image":[{"list-image":["none",O]}],"list-style-type":[{list:["none","disc","decimal",O]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...U(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",A,P]}],"underline-offset":[{"underline-offset":["auto",A,O]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:_()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",O]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",O]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...$(),z]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",F]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},W]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...U(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:U()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...U()]}],"outline-offset":[{"outline-offset":[A,O]}],"outline-w":[{outline:[A,P]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:B()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[A,P]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",I,G]}],"shadow-color":[{shadow:[H]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...V(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":V()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[s]}],"drop-shadow":[{"drop-shadow":["","none",I,O]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[b]}],sepia:[{sepia:[E]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[s]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[E]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[l]}],"border-spacing-x":[{"border-spacing-x":[l]}],"border-spacing-y":[{"border-spacing-y":[l]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",O]}],duration:[{duration:Z()}],ease:[{ease:["linear","in","out","in-out",O]}],delay:[{delay:Z()}],animate:[{animate:["none","spin","ping","pulse","bounce",O]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[D,O]}],"translate-x":[{"translate-x":[R]}],"translate-y":[{"translate-y":[R]}],"skew-x":[{"skew-x":[C]}],"skew-y":[{"skew-y":[C]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",O]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",O]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":_()}],"scroll-mx":[{"scroll-mx":_()}],"scroll-my":[{"scroll-my":_()}],"scroll-ms":[{"scroll-ms":_()}],"scroll-me":[{"scroll-me":_()}],"scroll-mt":[{"scroll-mt":_()}],"scroll-mr":[{"scroll-mr":_()}],"scroll-mb":[{"scroll-mb":_()}],"scroll-ml":[{"scroll-ml":_()}],"scroll-p":[{"scroll-p":_()}],"scroll-px":[{"scroll-px":_()}],"scroll-py":[{"scroll-py":_()}],"scroll-ps":[{"scroll-ps":_()}],"scroll-pe":[{"scroll-pe":_()}],"scroll-pt":[{"scroll-pt":_()}],"scroll-pr":[{"scroll-pr":_()}],"scroll-pb":[{"scroll-pb":_()}],"scroll-pl":[{"scroll-pl":_()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",O]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[A,P,T]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})},83721:(e,t,r)=>{r.d(t,{Z:()=>o});var n=r(43210);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},90270:(e,t,r)=>{r.d(t,{bL:()=>E,zi:()=>C});var n=r(43210),o=r(70569),i=r(98599),l=r(11273),a=r(65551),s=r(83721),u=r(18853),c=r(14163),d=r(60687),f="Switch",[p,m]=(0,l.A)(f),[h,v]=p(f),g=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:l,checked:s,defaultChecked:u,required:p,disabled:m,value:v="on",onCheckedChange:g,form:y,...b}=e,[E,C]=n.useState(null),S=(0,i.s)(t,e=>C(e)),R=n.useRef(!1),k=!E||y||!!E.closest("form"),[M,j]=(0,a.i)({prop:s,defaultProp:u??!1,onChange:g,caller:f});return(0,d.jsxs)(h,{scope:r,checked:M,disabled:m,children:[(0,d.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":M,"aria-required":p,"data-state":x(M),"data-disabled":m?"":void 0,disabled:m,value:v,...b,ref:S,onClick:(0,o.m)(e.onClick,e=>{j(e=>!e),k&&(R.current=e.isPropagationStopped(),R.current||e.stopPropagation())})}),k&&(0,d.jsx)(w,{control:E,bubbles:!R.current,name:l,value:v,checked:M,required:p,disabled:m,form:y,style:{transform:"translateX(-100%)"}})]})});g.displayName=f;var y="SwitchThumb",b=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=v(y,r);return(0,d.jsx)(c.sG.span,{"data-state":x(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});b.displayName=y;var w=n.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:o=!0,...l},a)=>{let c=n.useRef(null),f=(0,i.s)(c,a),p=(0,s.Z)(r),m=(0,u.X)(t);return n.useEffect(()=>{let e=c.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==r&&t){let n=new Event("click",{bubbles:o});t.call(e,r),e.dispatchEvent(n)}},[p,r,o]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...l,tabIndex:-1,ref:f,style:{...l.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}w.displayName="SwitchBubbleInput";var E=g,C=b},96963:(e,t,r)=>{r.d(t,{B:()=>s});var n,o=r(43210),i=r(66156),l=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function s(e){let[t,r]=o.useState(l());return(0,i.N)(()=>{e||r(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},97992:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},98599:(e,t,r)=>{r.d(t,{s:()=>l,t:()=>i});var n=r(43210);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function l(...e){return n.useCallback(i(...e),e)}},99270:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}};