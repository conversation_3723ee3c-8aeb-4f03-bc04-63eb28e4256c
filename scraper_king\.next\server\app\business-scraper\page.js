(()=>{var e={};e.id=82,e.ids=[82],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6921:()=>{},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29873:(e,s,t)=>{Promise.resolve().then(t.bind(t,20665)),Promise.resolve().then(t.bind(t,55465)),Promise.resolve().then(t.bind(t,51503)),Promise.resolve().then(t.bind(t,82246)),Promise.resolve().then(t.bind(t,58920)),Promise.resolve().then(t.bind(t,93250)),Promise.resolve().then(t.bind(t,35917)),Promise.resolve().then(t.bind(t,74052)),Promise.resolve().then(t.bind(t,64480)),Promise.resolve().then(t.bind(t,25080)),Promise.resolve().then(t.bind(t,25480)),Promise.resolve().then(t.bind(t,10240)),Promise.resolve().then(t.bind(t,7512)),Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.bind(t,60075))},33873:e=>{"use strict";e.exports=require("path")},45298:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c,metadata:()=>i});var r=t(37413),a=t(4536),o=t.n(a),l=t(60075),n=t(60250);let i={title:"Business Scraper - Scraper King",description:"Scrape business data including address, contact info, and email"};function c(){return(0,r.jsx)(n.d,{children:(0,r.jsx)("main",{className:"min-h-screen bg-gradient-to-b from-[#2e026d] to-[#15162c] text-white",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("header",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("h1",{className:"text-4xl font-extrabold tracking-tight sm:text-5xl",children:["Business ",(0,r.jsx)("span",{className:"text-[hsl(280,100%,70%)]",children:"Scraper"})]}),(0,r.jsx)(o(),{href:"/",className:"rounded-full bg-white/10 px-4 py-2 font-medium transition hover:bg-white/20",children:"← Back to Home"})]}),(0,r.jsx)("p",{className:"mt-2 text-lg text-white/70",children:"Scrape business data including address, contact information, and email"})]}),(0,r.jsx)("div",{className:"rounded-xl bg-white/10 p-6 shadow-lg",children:(0,r.jsx)(l.BusinessScraperForm,{})})]})})})}},60075:(e,s,t)=>{"use strict";t.d(s,{BusinessScraperForm:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call BusinessScraperForm() from the server but BusinessScraperForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\_components\\business-scraper\\BusinessScraperForm.tsx","BusinessScraperForm")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},76737:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=t(65239),a=t(48088),o=t(88170),l=t.n(o),n=t(30893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);t.d(s,i);let c={children:["",{children:["business-scraper",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,45298)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\business-scraper\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\business-scraper\\page.tsx"],h={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/business-scraper/page",pathname:"/business-scraper",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79646:e=>{"use strict";e.exports=require("child_process")},80148:(e,s,t)=>{"use strict";t.d(s,{BusinessScraperForm:()=>g});var r={};t.r(r),t.d(r,{checkApiHealth:()=>h,scrapeBusiness:()=>c,simpleUploadCsv:()=>m,standaloneUploadCsv:()=>p,testUploadCsv:()=>u,uploadCsv:()=>d});var a=t(60687),o=t(43210);function l({data:e}){let[s,t]=(0,o.useState)(1),[r,l]=(0,o.useState)("name"),[n,i]=(0,o.useState)("asc"),c=e=>{e===r?i("asc"===n?"desc":"asc"):(l(e),i("asc"))},d=(0,o.useMemo)(()=>[...e].sort((e,s)=>{let t=0;switch(r){case"name":t=e.name.localeCompare(s.name);break;case"address":t=e.address.localeCompare(s.address);break;case"phone":t=e.phone.localeCompare(s.phone);break;case"url":t=e.url.localeCompare(s.url);break;case"social_links":t=(e.social_links||"N/A").localeCompare(s.social_links||"N/A");break;case"is_shopify":t=e.is_shopify===s.is_shopify?0:e.is_shopify?-1:1;break;case"is_active":t=e.is_active===s.is_active?0:e.is_active?-1:1}return"asc"===n?t:-t}),[e,r,n]),h=Math.ceil(d.length/10),u=(s-1)*10,m=Math.min(u+10,d.length),p=d.slice(u,m);return 0===e.length?(0,a.jsx)("div",{className:"rounded-lg bg-white/10 p-6 text-center",children:(0,a.jsx)("p",{className:"text-lg",children:"No results found"})}):(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold",children:"Scraped Results"}),(0,a.jsx)("button",{onClick:()=>{let e=new Blob([["Name,Address,Phone,URL,Category,Social Links,Shopify Site,Active Domain,Status",...d.map(e=>[e.name,e.address,e.phone,e.url,e.category||"",e.social_links||"",e.is_shopify?"Yes":"No",e.is_active?"Yes":"No",e.status||""]).map(e=>e.map(e=>`"${String(e).replace(/"/g,'""')}"`).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),s=URL.createObjectURL(e),t=document.createElement("a");t.setAttribute("href",s),t.setAttribute("download","business_data.csv"),document.body.appendChild(t),t.click(),document.body.removeChild(t)},className:"rounded-lg bg-[hsl(280,100%,70%)] px-4 py-2 font-medium text-white transition hover:bg-[hsl(280,100%,60%)]",children:"Download CSV"})]}),(0,a.jsx)("div",{className:"overflow-x-auto rounded-lg bg-white/5",children:(0,a.jsxs)("table",{className:"w-full border-collapse",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-white/10 bg-white/10 text-left",children:[(0,a.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>c("name"),children:["Name ","name"===r&&("asc"===n?"↑":"↓")]}),(0,a.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>c("address"),children:["Address ","address"===r&&("asc"===n?"↑":"↓")]}),(0,a.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>c("phone"),children:["Phone ","phone"===r&&("asc"===n?"↑":"↓")]}),(0,a.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>c("url"),children:["URL ","url"===r&&("asc"===n?"↑":"↓")]}),(0,a.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>c("social_links"),children:["Social Links ","social_links"===r&&("asc"===n?"↑":"↓")]}),(0,a.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>c("is_shopify"),children:["Shopify ","is_shopify"===r&&("asc"===n?"↑":"↓")]}),(0,a.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>c("is_active"),children:["Active ","is_active"===r&&("asc"===n?"↑":"↓")]})]})}),(0,a.jsx)("tbody",{children:p.map((e,s)=>(0,a.jsxs)("tr",{className:"border-b border-white/10 transition hover:bg-white/5",children:[(0,a.jsx)("td",{className:"p-4",children:e.name}),(0,a.jsx)("td",{className:"p-4",children:e.address}),(0,a.jsx)("td",{className:"p-4",children:e.phone||"N/A"}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-[hsl(280,100%,80%)] hover:underline",children:e.url})}),(0,a.jsx)("td",{className:"p-4",children:e.social_links&&"N/A"!==e.social_links?(0,a.jsx)("div",{className:"flex flex-col gap-1",children:e.social_links.split("; ").map((e,s)=>(0,a.jsx)("a",{href:e,target:"_blank",rel:"noopener noreferrer",className:"text-[hsl(280,100%,80%)] hover:underline",children:e.includes("facebook.com")?"Facebook":e.includes("twitter.com")?"Twitter":e.includes("instagram.com")?"Instagram":e.includes("linkedin.com")?"LinkedIn":e.includes("youtube.com")?"YouTube":e.includes("pinterest.com")?"Pinterest":e.includes("tiktok.com")?"TikTok":e.includes("x.com")?"X":new URL(e).hostname},s))}):"N/A"}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsx)("span",{className:e.is_shopify?"text-green-400":"text-white/50",children:e.is_shopify?"Yes":"No"})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsx)("span",{className:e.is_active?"text-green-400":"text-white/50",children:e.is_active?"Yes":"No"})})]},s))})]})}),h>1&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-white/70",children:["Showing ",u+1,"-",m," of ",d.length," results"]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>{t(e=>Math.max(e-1,1))},disabled:1===s,className:"rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50",children:"Previous"}),(0,a.jsxs)("div",{className:"flex items-center justify-center rounded-lg bg-white/10 px-4 py-2",children:[s," / ",h]}),(0,a.jsx)("button",{onClick:()=>{t(e=>Math.min(e+1,h))},disabled:s===h,className:"rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50",children:"Next"})]})]})]})}function n({isProcessingMultiple:e,progress:s}){return(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center py-8",children:[(0,a.jsx)("div",{className:"mb-4 h-12 w-12 animate-spin rounded-full border-4 border-white/20 border-t-[hsl(280,100%,70%)]"}),e&&s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("p",{className:"text-lg font-medium",children:["Scraping businesses... (",s.current+1,"/",s.total,")"]}),(0,a.jsx)("div",{className:"mt-3 h-2 w-64 overflow-hidden rounded-full bg-white/10",children:(0,a.jsx)("div",{className:"h-full bg-[hsl(280,100%,70%)]",style:{width:`${Math.round(s.current/s.total*100)}%`}})}),(0,a.jsx)("p",{className:"mt-2 text-sm text-white/70",children:"Processing multiple queries. Please wait..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("p",{className:"text-lg font-medium",children:"Scraping business data..."}),(0,a.jsx)("p",{className:"mt-2 text-sm text-white/70",children:"This may take a few moments depending on the number of results."})]})]})}let i="http://localhost:5000/api";async function c(e){try{let s=await fetch(`${i}/scrape`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)try{let e=await s.json();throw Error(e.error||"Failed to scrape business data")}catch(e){throw Error(`Failed to scrape business data: ${s.status} ${s.statusText}`)}try{return await s.json()}catch(e){throw console.error("Error parsing response JSON:",e),Error("Failed to parse response from server")}}catch(e){throw console.error("Error scraping business data:",e),e}}async function d(e,s=!1){try{console.log(`Starting CSV upload: ${e.name}, size: ${e.size}, type: ${e.type}`);let t=new FormData;t.append("file",e);let r=s?`${i}/upload-csv-with-uploader`:`${i}/upload-csv`;console.log(`Uploading CSV to endpoint: ${r}`);let a=new AbortController,o=setTimeout(()=>a.abort(),6e4);try{let e=await fetch(r,{method:"POST",body:t,mode:"cors",credentials:"same-origin",headers:{Accept:"application/json"},signal:a.signal});if(clearTimeout(o),console.log(`Upload response status: ${e.status} ${e.statusText}`),!e.ok){let s=await e.text();console.error(`Error response body: ${s}`);try{let e=JSON.parse(s),t=e.error||"Unknown server error",r=e.traceback?`

Details: ${e.traceback}`:"";throw Error(`${t}${r}`)}catch(t){throw Error(`Failed to upload CSV file: ${e.status} ${e.statusText}

Response: ${s.substring(0,500)}`)}}try{let s=await e.json();return console.log(`Upload successful, received ${s.length} results`),s}catch(e){throw console.error("Error parsing response JSON:",e),Error("Failed to parse response from server")}}catch(e){if(clearTimeout(o),e instanceof Error&&"AbortError"===e.name)throw Error("Request timed out after 60 seconds");throw e}}catch(e){throw console.error("Error uploading CSV:",e),e}}async function h(){try{let e=await fetch(`${i}/health`);if(!e.ok)throw Error("API health check failed");return await e.json()}catch(e){throw console.error("API health check error:",e),e}}async function u(e){try{console.log(`Testing CSV upload for file: ${e.name}, size: ${e.size}, type: ${e.type}`);let s=new FormData;s.append("file",e);let t=`${i}/test-upload`;console.log(`Uploading CSV to test endpoint: ${t}`);let r=await fetch(t,{method:"POST",body:s,mode:"cors",credentials:"same-origin",headers:{Accept:"application/json"}});if(console.log(`Test upload response status: ${r.status} ${r.statusText}`),!r.ok)try{let e=await r.text();console.error("Error response body:",e);try{let s=JSON.parse(e);throw Error(s.error||`Test upload failed: ${r.status} ${r.statusText}`)}catch(s){throw Error(`Test upload failed: ${r.status} ${r.statusText}. Response: ${e.substring(0,200)}`)}}catch(e){throw Error(`Test upload failed: ${r.status} ${r.statusText}`)}let a=await r.json();return console.log("Test upload successful:",a),a}catch(e){throw console.error("Error in testUploadCsv function:",e),e}}async function m(e){try{console.log(`Simple CSV upload for file: ${e.name}, size: ${e.size}, type: ${e.type}`);let s=new FormData;s.append("file",e);let t=`${i}/simple-csv-upload`;console.log(`Uploading CSV to simple endpoint: ${t}`);let r=await fetch(t,{method:"POST",body:s,mode:"cors",credentials:"same-origin",headers:{Accept:"application/json"}});if(console.log(`Simple upload response status: ${r.status} ${r.statusText}`),!r.ok){let e=await r.text();console.error("Error response body:",e);try{let s=JSON.parse(e);throw Error(s.error||`Simple upload failed: ${r.status} ${r.statusText}`)}catch(s){throw Error(`Simple upload failed: ${r.status} ${r.statusText}. Response: ${e.substring(0,200)}`)}}let a=await r.json();return console.log("Simple upload successful:",a),a}catch(e){throw console.error("Error in simpleUploadCsv function:",e),e}}async function p(e){try{console.log(`Standalone CSV upload for file: ${e.name}, size: ${e.size}, type: ${e.type}`);let s=new FormData;s.append("file",e);let t=`${i}/standalone-csv-upload`;console.log(`Uploading CSV to standalone endpoint: ${t}`);let r=await fetch(t,{method:"POST",body:s,mode:"cors",headers:{Accept:"application/json"}});if(console.log(`Standalone upload response status: ${r.status} ${r.statusText}`),!r.ok){let e=await r.text();console.error("Error response body:",e);try{let s=JSON.parse(e);throw Error(s.error||`Standalone upload failed: ${r.status} ${r.statusText}`)}catch(s){throw Error(`Standalone upload failed: ${r.status} ${r.statusText}. Response: ${e.substring(0,200)}`)}}let a=await r.json();return console.log("Standalone upload successful:",a),a}catch(e){throw console.error("Error in standaloneUploadCsv function:",e),e}}function x({onResults:e,onError:s,onLoading:t}){let[r,l]=(0,o.useState)(null),[n,i]=(0,o.useState)(!1),[c,m]=(0,o.useState)(!0),p=(0,o.useRef)(null),x=async()=>{if(!r)return void s("Please select a CSV file first");if(0===r.size)return void s("The selected file is empty");if(!r.name.toLowerCase().endsWith(".csv"))return void s("Please select a valid CSV file");try{t(!0),s(""),console.log("Uploading CSV file:",r.name,"Size:",r.size),console.log(`Using ${c?"advanced":"standard"} CSV uploader`);try{let t=new File([r.size>1024?await r.slice(0,1024).text():await r.text()],r.name,{type:r.type});console.log("Testing with smaller file first:",t.size,"bytes");try{let t=await d(r,c);console.log("CSV upload results:",t),t&&t.length>0?e(t):s("No results found in the CSV file")}catch(r){console.error("CSV upload error:",r);let e=r instanceof Error?r.message:"Unknown error",t=e.includes("traceback")?"Server error processing the CSV file. Please check the file format and try again.":e;s(t),r instanceof Error&&console.error("Full error details:",r)}}catch(e){console.error("Error preparing file:",e),s(`Error preparing file: ${e instanceof Error?e.message:"Unknown error"}`)}}catch(e){console.error("CSV upload error:",e),e instanceof Error&&(console.error("Error name:",e.name),console.error("Error message:",e.message),console.error("Error stack:",e.stack)),s(`Error: ${e instanceof Error?e.message:"Failed to upload CSV file"}`)}finally{t(!1)}},f=async()=>{if(!r)return void s("Please select a CSV file first");try{t(!0),s(""),console.log("Testing CSV upload with file:",r.name);try{let e=await u(r);console.log("Test upload result:",e),s(`Test successful! File info: ${JSON.stringify(e)}`)}catch(e){console.error("Test upload error:",e),s(`Test upload error: ${e instanceof Error?e.message:String(e)}`)}}catch(e){console.error("Test error:",e),s(`Error: ${e instanceof Error?e.message:"Test failed"}`)}finally{t(!1)}},g=async()=>{try{t(!0),s(""),console.log("Checking API health");try{let e=await h();console.log("Health check result:",e),s(`API health check: ${JSON.stringify(e)}`)}catch(e){console.error("Health check error:",e),s(`Health check error: ${e instanceof Error?e.message:"Unknown error"}`)}}catch(e){console.error("Health check error:",e),s(`Error: ${e instanceof Error?e.message:"Health check failed"}`)}finally{t(!1)}};return(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsx)("label",{className:"font-medium",children:"Upload CSV File"}),(0,a.jsxs)("div",{className:`flex flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition ${n?"border-[hsl(280,100%,70%)] bg-[hsl(280,100%,70%)]/10":"border-white/30 hover:border-white/50"}`,onDragOver:e=>{e.preventDefault(),i(!0)},onDragLeave:e=>{e.preventDefault(),i(!1)},onDrop:e=>{e.preventDefault(),i(!1);let t=e.dataTransfer.files;if(t&&t.length>0){let e=t[0];if(!e)return void s("No file selected");if(!("text/csv"===e.type||e.name.toLowerCase().endsWith(".csv")||"application/vnd.ms-excel"===e.type||"application/csv"===e.type))return void s("Please select a CSV file");if(0===e.size)return void s("The selected file is empty");console.log("File dropped:",e.name,"Type:",e.type,"Size:",e.size),l(e)}},children:[(0,a.jsx)("input",{type:"file",accept:".csv",onChange:e=>{let t=e.target.files;if(t&&t.length>0){let e=t[0];if(!e)return void s("No file selected");if(!("text/csv"===e.type||e.name.toLowerCase().endsWith(".csv")||"application/vnd.ms-excel"===e.type||"application/csv"===e.type))return void s("Please select a CSV file");if(0===e.size)return void s("The selected file is empty");console.log("File selected:",e.name,"Type:",e.type,"Size:",e.size),l(e)}},className:"hidden",ref:p}),(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"mb-2 h-10 w-10 text-white/70",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})}),(0,a.jsx)("p",{className:"mb-1 text-center text-lg font-medium",children:"Drag & Drop your CSV file here"}),(0,a.jsxs)("p",{className:"text-center text-sm text-white/70",children:["or"," ",(0,a.jsx)("button",{type:"button",onClick:()=>{p.current&&p.current.click()},className:"text-[hsl(280,100%,80%)] hover:underline",children:"browse files"})]}),r&&(0,a.jsxs)("div",{className:"mt-4 flex items-center gap-2 rounded-lg bg-white/10 px-4 py-2",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),(0,a.jsx)("span",{className:"flex-1 truncate",children:r.name}),(0,a.jsx)("button",{type:"button",onClick:()=>{l(null),p.current&&(p.current.value="")},className:"text-white/70 hover:text-white",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsxs)("p",{className:"text-sm text-white/70",children:["CSV must include columns: name, location, country.",(0,a.jsx)("a",{href:"/example.csv",download:!0,className:"ml-1 text-[hsl(280,100%,80%)] hover:underline",children:"Download example CSV"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:c,onChange:e=>m(e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-white/20 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[hsl(280,100%,70%)]"}),(0,a.jsx)("span",{className:"ml-3 text-sm font-medium text-white/70",children:"Use Advanced CSV Uploader"})]}),(0,a.jsx)("div",{className:"text-xs text-white/50 ml-2",children:c?"Uses csv_uploader.py for better extraction":"Uses standard extraction"})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-center",children:[(0,a.jsx)("button",{type:"button",onClick:x,disabled:!r,className:`rounded-lg px-6 py-3 font-semibold transition ${r?"bg-[hsl(280,100%,70%)] text-white hover:bg-[hsl(280,100%,60%)]":"bg-white/10 text-white/50 cursor-not-allowed"}`,children:"Upload & Process CSV"}),(0,a.jsx)("button",{onClick:f,className:"ml-2 rounded bg-gray-600 px-4 py-2 text-white hover:bg-gray-700",disabled:!r,children:"Test Upload"}),(0,a.jsx)("button",{onClick:g,className:"ml-2 rounded bg-purple-600 px-4 py-2 text-white hover:bg-purple-700",children:"Check API"})]}),(0,a.jsxs)("div",{className:"mt-2 rounded-lg bg-white/5 p-4",children:[(0,a.jsx)("h3",{className:"mb-2 font-medium",children:"CSV Format Example:"}),(0,a.jsx)("pre",{className:"overflow-x-auto whitespace-pre-wrap text-sm text-white/70",children:"name,location,country Starbucks,New York,us Apple Store,San Francisco,us Microsoft Office,Seattle,us"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-white/70",children:"The CSV file should have the following columns:"}),(0,a.jsxs)("ul",{className:"mt-1 list-disc pl-5 text-sm text-white/70",children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"name"}),": Business name or search term"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"location"}),": City, address, or general location"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"country"}),': Country code (e.g., "us", "uk", "in")']}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"url"})," (optional): Website URL if known"]})]}),(0,a.jsx)("p",{className:"mt-2 text-sm text-white/70",children:"The system will automatically detect column headers and supports various CSV formats, including comma, semicolon, and tab-separated files."})]})]})}t(6921);let f=[{code:"us",name:"United States",states:[{code:"al",name:"Alabama",cities:["Birmingham","Montgomery","Mobile","Huntsville"]},{code:"ak",name:"Alaska",cities:["Anchorage","Fairbanks","Juneau","Sitka"]},{code:"az",name:"Arizona",cities:["Phoenix","Tucson","Mesa","Chandler"]},{code:"ca",name:"California",cities:["Los Angeles","San Francisco","San Diego","Sacramento"]},{code:"co",name:"Colorado",cities:["Denver","Colorado Springs","Aurora","Fort Collins"]},{code:"fl",name:"Florida",cities:["Miami","Orlando","Tampa","Jacksonville"]},{code:"ga",name:"Georgia",cities:["Atlanta","Savannah","Augusta","Columbus"]},{code:"ny",name:"New York",cities:["New York City","Buffalo","Rochester","Syracuse"]},{code:"tx",name:"Texas",cities:["Houston","Dallas","Austin","San Antonio"]}]},{code:"uk",name:"United Kingdom",states:[{code:"eng",name:"England",cities:["London","Manchester","Birmingham","Liverpool"]},{code:"sct",name:"Scotland",cities:["Edinburgh","Glasgow","Aberdeen","Dundee"]},{code:"wls",name:"Wales",cities:["Cardiff","Swansea","Newport","Bangor"]},{code:"nir",name:"Northern Ireland",cities:["Belfast","Derry","Lisburn","Newry"]}]},{code:"ca",name:"Canada",states:[{code:"on",name:"Ontario",cities:["Toronto","Ottawa","Hamilton","London"]},{code:"qc",name:"Quebec",cities:["Montreal","Quebec City","Laval","Gatineau"]},{code:"bc",name:"British Columbia",cities:["Vancouver","Victoria","Surrey","Burnaby"]},{code:"ab",name:"Alberta",cities:["Calgary","Edmonton","Red Deer","Lethbridge"]}]},{code:"au",name:"Australia",states:[{code:"nsw",name:"New South Wales",cities:["Sydney","Newcastle","Wollongong","Wagga Wagga"]},{code:"vic",name:"Victoria",cities:["Melbourne","Geelong","Ballarat","Bendigo"]},{code:"qld",name:"Queensland",cities:["Brisbane","Gold Coast","Cairns","Townsville"]},{code:"wa",name:"Western Australia",cities:["Perth","Fremantle","Bunbury","Geraldton"]}]},{code:"in",name:"India",states:[{code:"mh",name:"Maharashtra",cities:["Mumbai","Pune","Nagpur","Nashik"]},{code:"dl",name:"Delhi",cities:["New Delhi","Delhi","Noida","Gurgaon"]},{code:"ka",name:"Karnataka",cities:["Bangalore","Mysore","Hubli","Mangalore"]},{code:"tn",name:"Tamil Nadu",cities:["Chennai","Coimbatore","Madurai","Salem"]}]},{code:"de",name:"Germany",states:[{code:"by",name:"Bavaria",cities:["Munich","Nuremberg","Augsburg","Regensburg"]},{code:"nw",name:"North Rhine-Westphalia",cities:["Cologne","D\xfcsseldorf","Dortmund","Essen"]},{code:"be",name:"Berlin",cities:["Berlin"]},{code:"hh",name:"Hamburg",cities:["Hamburg"]}]}];function g(){let[e,s]=(0,o.useState)(""),[t,r]=(0,o.useState)("us"),[i,d]=(0,o.useState)(""),[h,u]=(0,o.useState)(""),[m,p]=(0,o.useState)(""),[g,b]=(0,o.useState)(5),[v,w]=(0,o.useState)(!1),[y,j]=(0,o.useState)(!1),[N,S]=(0,o.useState)(null),[k,C]=(0,o.useState)(null),[P,$]=(0,o.useState)(null),[E,T]=(0,o.useState)(!1),[_,A]=(0,o.useState)(null),[F,U]=(0,o.useState)("form"),V=f.find(e=>e.code===t),D=V?.states||[],B=D.find(e=>e.code===i),L=B?.cities||[],M=async s=>{s.preventDefault(),S(null);let r="";if(h){if(r=h,i){let e=B?.name||"";r+=`, ${e}`}}else{if(!m.trim())return void S("Please select a city or enter a custom location");r=m.trim()}if(!e.trim())return void S("Search term is required");C(null),T(!0),$({current:0,total:g});try{let s=await c({location:r,category:e.trim(),country:t,maxResults:g,filterShopify:v,filterActive:y});s&&s.length>0?C(s):S("No results found. Please try a different search query.")}catch(e){console.error("Scraping error:",e),S(`Error: ${e instanceof Error?e.message:"Failed to scrape business data. Please try again."}`)}finally{T(!1),$(null)}},O=()=>{s(""),r("us"),d(""),u(""),p(""),b(5),w(!1),j(!1),S(null),C(null),$(null),T(!1)};return(0,a.jsxs)("div",{className:"flex flex-col gap-6",children:[_&&"error"===_.status&&(0,a.jsxs)("div",{className:"rounded-lg bg-red-500/20 p-4 text-red-200",children:[(0,a.jsx)("p",{className:"font-semibold",children:"API Error:"}),(0,a.jsx)("p",{children:_.message}),(0,a.jsx)("p",{className:"mt-2 text-sm",children:"Please make sure the Flask backend is running on http://localhost:5000"})]}),!E&&!k&&(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsxs)("div",{className:"flex border-b border-white/10",children:[(0,a.jsx)("button",{type:"button",className:`px-4 py-2 font-medium transition ${"form"===F?"border-b-2 border-[hsl(280,100%,70%)] text-[hsl(280,100%,80%)]":"text-white/70 hover:text-white"}`,onClick:()=>U("form"),children:"Search Form"}),(0,a.jsx)("button",{type:"button",className:`px-4 py-2 font-medium transition ${"csv"===F?"border-b-2 border-[hsl(280,100%,70%)] text-[hsl(280,100%,80%)]":"text-white/70 hover:text-white"}`,onClick:()=>U("csv"),children:"CSV Upload"})]}),"form"===F&&(0,a.jsxs)("form",{onSubmit:M,className:"flex flex-col gap-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsxs)("label",{htmlFor:"country",className:"font-medium",children:["Country ",(0,a.jsx)("span",{className:"text-red-400",children:"*"})]}),(0,a.jsx)("select",{id:"country",value:t,onChange:e=>{r(e.target.value),d(""),u("")},className:"rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)] scrollable-select",children:f.map(e=>(0,a.jsx)("option",{value:e.code,children:e.name},e.code))})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsx)("label",{htmlFor:"state",className:"font-medium",children:"State/Region"}),(0,a.jsxs)("select",{id:"state",value:i,onChange:e=>{d(e.target.value),u("")},className:"rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)] scrollable-select",disabled:0===D.length,children:[(0,a.jsx)("option",{value:"",children:"Select a state/region"}),D.map(e=>(0,a.jsx)("option",{value:e.code,children:e.name},e.code))]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsx)("label",{htmlFor:"city",className:"font-medium",children:"City"}),(0,a.jsxs)("select",{id:"city",value:h,onChange:e=>u(e.target.value),className:"rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)] scrollable-select",disabled:0===L.length,children:[(0,a.jsx)("option",{value:"",children:"Select a city"}),L.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsxs)("label",{htmlFor:"customLocation",className:"font-medium",children:["Custom Location",(0,a.jsx)("span",{className:"ml-2 text-sm text-white/70",children:"(Optional, if city not listed)"})]}),(0,a.jsx)("input",{id:"customLocation",type:"text",value:m,onChange:e=>p(e.target.value),placeholder:"Enter specific location",className:"rounded-lg bg-white/10 px-4 py-2 text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]"})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2 md:col-span-2",children:[(0,a.jsxs)("label",{htmlFor:"category",className:"font-medium",children:["Search Term ",(0,a.jsx)("span",{className:"text-red-400",children:"*"})]}),(0,a.jsx)("input",{id:"category",type:"text",value:e,onChange:e=>s(e.target.value),placeholder:"Business name, type, or any search term (e.g., Starbucks, restaurants, plumbers)",className:"rounded-lg bg-white/10 px-4 py-2 text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]",required:!0}),(0,a.jsx)("p",{className:"mt-1 text-sm text-white/70",children:"Enter any business name, type, or search term related to the businesses you want to find"})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2 md:col-span-2",children:[(0,a.jsx)("label",{htmlFor:"maxResults",className:"font-medium",children:"Number of Searches to Scrape"}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("input",{id:"maxResults",type:"range",min:"1",max:"20",value:g,onChange:e=>b(parseInt(e.target.value)),className:"w-full"}),(0,a.jsx)("span",{className:"min-w-[2rem] text-center",children:g})]}),(0,a.jsx)("p",{className:"mt-1 text-sm text-white/70",children:"Higher values may take longer but provide more results"})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2 md:col-span-2",children:[(0,a.jsx)("label",{className:"font-medium",children:"Filter Options"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{id:"filterShopify",type:"checkbox",checked:v,onChange:e=>w(e.target.checked),className:"h-4 w-4 rounded border-white/30 bg-white/10 text-[hsl(280,100%,70%)]"}),(0,a.jsx)("label",{htmlFor:"filterShopify",className:"text-sm",children:"Shopify Sites Only"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{id:"filterActive",type:"checkbox",checked:y,onChange:e=>j(e.target.checked),className:"h-4 w-4 rounded border-white/30 bg-white/10 text-[hsl(280,100%,70%)]"}),(0,a.jsx)("label",{htmlFor:"filterActive",className:"text-sm",children:"Active Domains Only"})]})]}),(0,a.jsx)("p",{className:"mt-1 text-sm text-white/70",children:"Filter results to include only specific types of websites"})]})]}),N&&(0,a.jsxs)("div",{className:"rounded-lg bg-red-500/20 p-4 text-red-200",children:[(0,a.jsx)("p",{className:"font-semibold",children:"Error:"}),(0,a.jsx)("p",{children:N.replace(/^Error: /,"")}),(0,a.jsx)("p",{className:"mt-2 text-sm",children:"Please check your search parameters and try again. Make sure you've entered a valid location and category."})]}),(0,a.jsxs)("div",{className:"mt-4 flex gap-4",children:[(0,a.jsx)("button",{type:"submit",className:"flex-1 rounded-lg bg-[hsl(280,100%,70%)] px-6 py-3 font-semibold text-white transition hover:bg-[hsl(280,100%,60%)]",children:"Start Scraping"}),(0,a.jsx)("button",{type:"button",onClick:O,className:"rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20",children:"Reset"})]})]}),"csv"===F&&(0,a.jsx)(x,{onResults:C,onError:S,onLoading:T})]}),E&&(0,a.jsx)(n,{isProcessingMultiple:!0,progress:P||{current:0,total:5}}),k&&(0,a.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,a.jsx)(l,{data:k}),(0,a.jsx)("div",{className:"flex gap-4",children:(0,a.jsx)("button",{onClick:O,className:"rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20",children:"New Search"})})]})]})}},95025:(e,s,t)=>{Promise.resolve().then(t.bind(t,39295)),Promise.resolve().then(t.bind(t,24903)),Promise.resolve().then(t.bind(t,8693)),Promise.resolve().then(t.bind(t,18228)),Promise.resolve().then(t.bind(t,87394)),Promise.resolve().then(t.bind(t,19100)),Promise.resolve().then(t.bind(t,54050)),Promise.resolve().then(t.bind(t,97322)),Promise.resolve().then(t.bind(t,93425)),Promise.resolve().then(t.bind(t,12030)),Promise.resolve().then(t.bind(t,46674)),Promise.resolve().then(t.bind(t,35522)),Promise.resolve().then(t.bind(t,45806)),Promise.resolve().then(t.t.bind(t,85814,23)),Promise.resolve().then(t.bind(t,80148))},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[719,338,814,788,923,240],()=>t(76737));module.exports=r})();