import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter, publicProcedure } from "~/server/api/trpc";
import Papa from "papaparse";

export const scraperRouter = createTRPCRouter({
  uploadCsv: publicProcedure
    .input(
      z.object({
        fileName: z.string(),
        fileContent: z.string(), // base64 string
      })
    )
    .mutation(async ({ input }) => {
      try {
        const buffer = Buffer.from(input.fileContent, "base64");
        const text = buffer.toString("utf-8");

        const parsed = Papa.parse(text, {
          header: true,
          skipEmptyLines: true,
        });

        if (parsed.errors.length) {
          console.error("CSV parsing errors:", parsed.errors);
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Failed to parse CSV",
          });
        }

        // Map parsed data to ScrapedData[] type
        const results = (parsed.data as any[]).map((row) => ({
          url: row.url ?? "",
          title: row.title ?? "",
          description: row.description ?? "",
          contactInfo: {
            phone: row.phone ?? "",
            email: row.email ?? "",
          },
        }));

        return results;
      } catch (error) {
        console.error("Upload processing error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to process uploaded file",
        });
      }
    }),
});
