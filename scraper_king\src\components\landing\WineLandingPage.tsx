"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { Card, CardContent } from "~/components/ui/card"
import { Label } from "~/components/ui/label"
import { Slider } from "~/components/ui/slider"
import { Switch } from "~/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "~/components/ui/dropdown-menu"
import { Moon, Sun, Wine, Star, Search, Menu, Grape, MapPin, Building2, Crown } from "lucide-react"
import Link from "next/link"

export default function WineLandingPage() {
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [priceRange, setPriceRange] = useState([500, 2000])
  const [rating, setRating] = useState([3.8])

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode)
    document.documentElement.classList.toggle("dark")
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${isDarkMode ? "dark" : ""}`}>
      {/* Navigation Bar */}
      <nav className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <Wine className="h-8 w-8 text-red-600 mr-2" />
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">WineData</h1>
              </div>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-8">
                <Link
                  href="#"
                  className="text-gray-900 dark:text-white hover:text-red-600 dark:hover:text-red-400 px-3 py-2 text-sm font-medium transition-colors flex items-center"
                >
                  <Wine className="h-4 w-4 mr-1" />
                  Wines
                </Link>
                <Link
                  href="#"
                  className="text-gray-600 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 px-3 py-2 text-sm font-medium transition-colors flex items-center"
                >
                  <MapPin className="h-4 w-4 mr-1" />
                  Regions
                </Link>
                <Link
                  href="#"
                  className="text-gray-600 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 px-3 py-2 text-sm font-medium transition-colors flex items-center"
                >
                  <Grape className="h-4 w-4 mr-1" />
                  Grapes
                </Link>
                <Link
                  href="#"
                  className="text-gray-600 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 px-3 py-2 text-sm font-medium transition-colors flex items-center"
                >
                  <Building2 className="h-4 w-4 mr-1" />
                  Wineries
                </Link>
                <Link
                  href="#"
                  className="text-gray-600 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 px-3 py-2 text-sm font-medium transition-colors flex items-center"
                >
                  <Crown className="h-4 w-4 mr-1" />
                  Premium
                </Link>
                <Button variant="outline" className="border-red-600 text-red-600 hover:bg-red-50 dark:hover:bg-red-950">
                  Login
                </Button>
              </div>
            </div>

            {/* Dark Mode Toggle & Mobile Menu */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Sun className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                <Switch
                  checked={isDarkMode}
                  onCheckedChange={toggleDarkMode}
                  className="data-[state=checked]:bg-red-600"
                />
                <Moon className="h-4 w-4 text-gray-600 dark:text-gray-300" />
              </div>

              {/* Mobile menu button */}
              <div className="md:hidden">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <Menu className="h-5 w-5" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem>Wines</DropdownMenuItem>
                    <DropdownMenuItem>Regions</DropdownMenuItem>
                    <DropdownMenuItem>Grapes</DropdownMenuItem>
                    <DropdownMenuItem>Wineries</DropdownMenuItem>
                    <DropdownMenuItem>Premium</DropdownMenuItem>
                    <DropdownMenuItem>Login</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Image with Blur */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('/placeholder.svg?height=1080&width=1920')`,
            filter: "blur(8px)",
            transform: "scale(1.1)",
          }}
        />

        {/* Wine-themed Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-red-900/70 via-purple-900/60 to-black/80 dark:from-red-950/80 dark:via-purple-950/70 dark:to-black/90" />

        {/* Content */}
        <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            {/* Wine Icon */}
            <div className="flex justify-center mb-8">
              <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                <Wine className="h-10 w-10 text-white" />
              </div>
            </div>

            {/* Main Headline */}
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-8 leading-tight">
              Discover the Right
              <span className="block text-red-300">Wine Data</span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-200 mb-12 max-w-3xl mx-auto">
              Extract comprehensive wine data from top platforms. Get ratings, prices, reviews, and detailed wine
              information for your business intelligence.
            </p>

            {/* Filter Card */}
            <div className="max-w-4xl mx-auto mb-12">
              <Card className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border-0 shadow-2xl hover:shadow-3xl transition-all duration-300">
                <CardContent className="p-8">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    {/* Wine Type Filter */}
                    <div className="space-y-4">
                      <div className="flex items-center mb-4">
                        <Wine className="h-5 w-5 text-red-600 mr-2" />
                        <Label className="text-sm font-semibold text-gray-700 dark:text-gray-200">Wine Type</Label>
                      </div>
                      <Select defaultValue="red">
                        <SelectTrigger className="w-full h-12 text-base">
                          <SelectValue placeholder="Select Wine Type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="red">Red Wine</SelectItem>
                          <SelectItem value="white">White Wine</SelectItem>
                          <SelectItem value="rose">Rosé Wine</SelectItem>
                          <SelectItem value="sparkling">Sparkling Wine</SelectItem>
                          <SelectItem value="dessert">Dessert Wine</SelectItem>
                          <SelectItem value="fortified">Fortified Wine</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Price Range Filter */}
                    <div className="space-y-4">
                      <div className="flex items-center mb-4">
                        <span className="text-red-600 mr-2 font-bold">₹</span>
                        <Label className="text-sm font-semibold text-gray-700 dark:text-gray-200">Price Range</Label>
                      </div>
                      <div className="space-y-4 pt-2">
                        <Slider
                          value={priceRange}
                          onValueChange={setPriceRange}
                          max={2000}
                          min={500}
                          step={50}
                          className="w-full"
                        />
                        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                          <span className="font-medium">₹{priceRange[0]}</span>
                          <span className="font-medium">₹{priceRange[1]}</span>
                        </div>
                      </div>
                    </div>

                    {/* Rating Filter */}
                    <div className="space-y-4">
                      <div className="flex items-center mb-4">
                        <Star className="h-5 w-5 text-red-600 mr-2 fill-current" />
                        <Label className="text-sm font-semibold text-gray-700 dark:text-gray-200">Rating</Label>
                      </div>
                      <div className="space-y-4 pt-2">
                        <Slider
                          value={rating}
                          onValueChange={setRating}
                          max={5}
                          min={3.8}
                          step={0.1}
                          className="w-full"
                        />
                        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                          <span className="font-medium">{rating[0]} stars</span>
                          <span className="font-medium">5+ stars</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Start Scraping Button */}
                  <div className="mt-8 text-center">
                    <Button
                      size="lg"
                      className="bg-green-600 hover:bg-green-700 text-white px-12 py-6 text-xl font-semibold rounded-xl shadow-2xl hover:shadow-green-500/25 transition-all duration-300 transform hover:scale-105"
                    >
                      <Search className="mr-3 h-6 w-6" />
                      Start Scraping
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Additional Info */}
            <div className="flex flex-wrap justify-center items-center gap-8 text-gray-300 text-sm">
              <div className="flex items-center">
                <Star className="h-4 w-4 text-yellow-400 mr-1 fill-current" />
                <span>4.9/5 Rating</span>
              </div>
              <div className="flex items-center">
                <Wine className="h-4 w-4 text-red-400 mr-1" />
                <span>1M+ Wines Tracked</span>
              </div>
              <div className="flex items-center">
                <Building2 className="h-4 w-4 text-purple-400 mr-1" />
                <span>10K+ Wineries</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">Why Wine Professionals Choose Us</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Comprehensive wine data extraction platform trusted by sommeliers, wine retailers, and industry experts
              worldwide.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="bg-white dark:bg-gray-800 border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                  <Wine className="h-8 w-8 text-red-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Comprehensive Data</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Extract detailed wine information including vintage, region, grape variety, tasting notes, and expert
                  ratings from multiple sources.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                  <Star className="h-8 w-8 text-green-600 fill-current" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Real-time Pricing</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Monitor wine prices across multiple platforms and regions. Get instant alerts on price changes and
                  market trends.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                  <Grape className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Expert Analytics</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Advanced analytics and insights to help you make informed decisions about wine investments and
                  inventory management.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gradient-to-r from-red-600 to-purple-600 dark:from-red-700 dark:to-purple-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center text-white">
            <div>
              <div className="text-4xl font-bold mb-2">1M+</div>
              <div className="text-red-100">Wines Tracked</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">10K+</div>
              <div className="text-red-100">Wineries</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">50+</div>
              <div className="text-red-100">Countries</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">99.9%</div>
              <div className="text-red-100">Accuracy</div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
