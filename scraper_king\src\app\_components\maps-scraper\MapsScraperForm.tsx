"use client";

import { useState } from "react";
import { ResultsTable } from "./ResultsTable";
import { LoadingIndicator } from "./LoadingIndicator";
import { api } from "~/trpc/react";

// Define the countries and their major cities
const COUNTRIES_WITH_CITIES = [
  {
    code: "us",
    name: "United States",
    cities: ["New York", "Los Angeles", "Chicago", "Houston", "Phoenix", "Philadelphia", "San Antonio", "San Diego", "Dallas", "San Jose"]
  },
  {
    code: "uk",
    name: "United Kingdom",
    cities: ["London", "Birmingham", "Manchester", "Glasgow", "Liverpool", "Bristol", "Edinburgh", "Leeds", "Sheffield", "Newcastle"]
  },
  {
    code: "ca",
    name: "Canada",
    cities: ["Toronto", "Montreal", "Vancouver", "Calgary", "Edmonton", "Ottawa", "Winnipeg", "Quebec City", "Hamilton", "Kitchener"]
  },
  {
    code: "au",
    name: "Australia",
    cities: ["Sydney", "Melbourne", "Brisbane", "Perth", "Adelaide", "Gold Coast", "Newcastle", "Canberra", "Wollongong", "Hobart"]
  },
  {
    code: "in",
    name: "India",
    cities: ["Mumbai", "Delhi", "Bangalore", "Hyderabad", "Chennai", "Kolkata", "Pune", "Ahmedabad", "Jaipur", "Surat"]
  },
  {
    code: "de",
    name: "Germany",
    cities: ["Berlin", "Hamburg", "Munich", "Cologne", "Frankfurt", "Stuttgart", "Düsseldorf", "Leipzig", "Dortmund", "Essen"]
  },
  {
    code: "fr",
    name: "France",
    cities: ["Paris", "Marseille", "Lyon", "Toulouse", "Nice", "Nantes", "Strasbourg", "Montpellier", "Bordeaux", "Lille"]
  },
  {
    code: "es",
    name: "Spain",
    cities: ["Madrid", "Barcelona", "Valencia", "Seville", "Zaragoza", "Málaga", "Murcia", "Palma", "Las Palmas", "Bilbao"]
  },
  {
    code: "it",
    name: "Italy",
    cities: ["Rome", "Milan", "Naples", "Turin", "Palermo", "Genoa", "Bologna", "Florence", "Bari", "Catania"]
  },
  {
    code: "jp",
    name: "Japan",
    cities: ["Tokyo", "Yokohama", "Osaka", "Nagoya", "Sapporo", "Fukuoka", "Kobe", "Kyoto", "Kawasaki", "Saitama"]
  },
  {
    code: "br",
    name: "Brazil",
    cities: ["São Paulo", "Rio de Janeiro", "Brasília", "Salvador", "Fortaleza", "Belo Horizonte", "Manaus", "Curitiba", "Recife", "Porto Alegre"]
  },
  {
    code: "mx",
    name: "Mexico",
    cities: ["Mexico City", "Guadalajara", "Monterrey", "Puebla", "Tijuana", "León", "Juárez", "Zapopan", "Mérida", "Cancún"]
  },
];

export type ScrapedResult = {
  name: string;
  city: string;
  category: string;
  rating: number;
  reviews: number;
  mostVisited: boolean;
  address?: string;
  phone?: string;
  website?: string;
};

export function MapsScraperForm() {
  // Form state
  const [cities, setCities] = useState<string[]>([]);
  const [customCity, setCustomCity] = useState("");
  const [category, setCategory] = useState("");
  const [country, setCountry] = useState("us");
  const [minRating, setMinRating] = useState(3);
  const [minReviews, setMinReviews] = useState(10);
  const [sortByMostVisited, setSortByMostVisited] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<ScrapedResult[] | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState({ current: 0, total: 0 });

  // Get the cities for the selected country
  const selectedCountry = COUNTRIES_WITH_CITIES.find(c => c.code === country);
  const availableCities = selectedCountry?.cities || [];

  // tRPC mutation for scraping
  const scrapeMutation = api.scraper.scrapeGoogleMaps.useMutation({
    onSuccess: (data) => {
      // If we're processing multiple cities, accumulate the results
      if (isProcessing) {
        setProgress(prev => ({ ...prev, current: prev.current + 1 }));

        // If this is the last city, we're done
        if (progress.current + 1 >= progress.total) {
          setIsProcessing(false);
          setResults(prev => [...(prev || []), ...data]);
        } else {
          // Process the next city
          const nextCity = cities[progress.current + 1];
          processCity(nextCity);
        }
      } else {
        setResults(data);
      }
    },
    onError: (error) => {
      setError(error.message);
      setIsProcessing(false);
    },
  });

  // Process a single city
  const processCity = (city: string) => {
    scrapeMutation.mutate({
      city: city.trim(),
      category: category.trim(),
      country,
      minRating,
      minReviews,
      sortByMostVisited,
    });
  };

  // Process multiple cities at once
  const processCities = (citiesToProcess: string[]) => {
    scrapeMutation.mutate({
      city: citiesToProcess[0].trim(), // First city as the primary city
      cities: citiesToProcess.map(c => c.trim()), // All cities
      category: category.trim(),
      country,
      minRating,
      minReviews,
      sortByMostVisited,
    });
  };

  // Handle city selection/deselection
  const handleCityToggle = (city: string) => {
    setCities(prev =>
      prev.includes(city)
        ? prev.filter(c => c !== city)
        : [...prev, city]
    );
  };

  // Add custom city
  const handleAddCustomCity = () => {
    if (customCity.trim() && !cities.includes(customCity.trim())) {
      setCities(prev => [...prev, customCity.trim()]);
      setCustomCity("");
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validate form
    if (cities.length === 0) {
      setError("At least one city must be selected");
      return;
    }

    if (!category.trim()) {
      setError("Category is required");
      return;
    }

    // Reset results
    setResults(null);

    // Process all cities at once using the backend's batch processing
    processCities(cities);
  };

  const handleReset = () => {
    setCities([]);
    setCustomCity("");
    setCategory("");
    setCountry("us");
    setMinRating(3);
    setMinReviews(10);
    setSortByMostVisited(false);
    setError(null);
    setResults(null);
    setIsProcessing(false);
    setProgress({ current: 0, total: 0 });
  };

  return (
    <div className="flex flex-col gap-6">
      {!scrapeMutation.isPending && !results && (
        <form onSubmit={handleSubmit} className="flex flex-col gap-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {/* Country Selection */}
            <div className="flex flex-col gap-2">
              <label htmlFor="country" className="font-medium">
                Country
              </label>
              <select
                id="country"
                value={country}
                onChange={(e) => {
                  setCountry(e.target.value);
                  setCities([]); // Reset cities when country changes
                }}
                className="rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]"
              >
                {COUNTRIES_WITH_CITIES.map((c) => (
                  <option key={c.code} value={c.code}>
                    {c.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Category Input */}
            <div className="flex flex-col gap-2">
              <label htmlFor="category" className="font-medium">
                Category <span className="text-red-400">*</span>
              </label>
              <input
                id="category"
                type="text"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                placeholder="e.g., restaurants, hotels, etc."
                className="rounded-lg bg-white/10 px-4 py-2 text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]"
                required
              />
            </div>

            {/* City Selection */}
            <div className="col-span-1 flex flex-col gap-2 md:col-span-2">
              <label className="font-medium">
                Cities <span className="text-red-400">*</span>
                <span className="ml-2 text-sm text-white/70">
                  (Select cities to scrape or add your own)
                </span>
              </label>

              {/* City Checkboxes */}
              <div className="grid grid-cols-2 gap-2 rounded-lg bg-white/5 p-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
                {availableCities.map((city) => (
                  <div key={city} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id={`city-${city}`}
                      checked={cities.includes(city)}
                      onChange={() => handleCityToggle(city)}
                      className="h-4 w-4 rounded border-white/30 bg-white/10 text-[hsl(280,100%,70%)] focus:ring-[hsl(280,100%,70%)]"
                    />
                    <label htmlFor={`city-${city}`} className="text-sm">
                      {city}
                    </label>
                  </div>
                ))}
              </div>

              {/* Custom City Input */}
              <div className="mt-2 flex gap-2">
                <input
                  type="text"
                  value={customCity}
                  onChange={(e) => setCustomCity(e.target.value)}
                  placeholder="Add a custom city"
                  className="flex-1 rounded-lg bg-white/10 px-4 py-2 text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]"
                />
                <button
                  type="button"
                  onClick={handleAddCustomCity}
                  disabled={!customCity.trim()}
                  className="rounded-lg bg-white/10 px-4 py-2 font-medium transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  Add
                </button>
              </div>

              {/* Selected Cities */}
              {cities.length > 0 && (
                <div className="mt-2">
                  <p className="mb-1 text-sm text-white/70">Selected Cities:</p>
                  <div className="flex flex-wrap gap-2">
                    {cities.map((city) => (
                      <span
                        key={city}
                        className="flex items-center gap-1 rounded-full bg-[hsl(280,100%,70%)]/20 px-3 py-1 text-sm"
                      >
                        {city}
                        <button
                          type="button"
                          onClick={() => handleCityToggle(city)}
                          className="ml-1 rounded-full p-1 text-white/70 hover:bg-white/10 hover:text-white"
                        >
                          ✕
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Min Rating */}
            <div className="flex flex-col gap-2">
              <label htmlFor="minRating" className="font-medium">
                Minimum Star Rating
              </label>
              <input
                id="minRating"
                type="number"
                min="1"
                max="5"
                step="0.1"
                value={minRating}
                onChange={(e) => setMinRating(parseFloat(e.target.value))}
                className="rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]"
              />
            </div>

            {/* Min Reviews */}
            <div className="flex flex-col gap-2">
              <label htmlFor="minReviews" className="font-medium">
                Minimum Number of Reviews
              </label>
              <input
                id="minReviews"
                type="number"
                min="0"
                value={minReviews}
                onChange={(e) => setMinReviews(parseInt(e.target.value))}
                className="rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]"
              />
            </div>

            {/* Sort by Most Visited */}
            <div className="flex items-center gap-2">
              <input
                id="sortByMostVisited"
                type="checkbox"
                checked={sortByMostVisited}
                onChange={(e) => setSortByMostVisited(e.target.checked)}
                className="h-5 w-5 rounded border-white/30 bg-white/10 text-[hsl(280,100%,70%)] focus:ring-[hsl(280,100%,70%)]"
              />
              <label htmlFor="sortByMostVisited" className="font-medium">
                Sort by Most Visited
              </label>
            </div>
          </div>

          {error && (
            <div className="rounded-lg bg-red-500/20 p-4 text-red-200">
              <p>{error}</p>
            </div>
          )}

          <div className="mt-4 flex gap-4">
            <button
              type="submit"
              className="flex-1 rounded-lg bg-[hsl(280,100%,70%)] px-6 py-3 font-semibold text-white transition hover:bg-[hsl(280,100%,60%)]"
            >
              Start Scraping
            </button>
            <button
              type="button"
              onClick={handleReset}
              className="rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20"
            >
              Reset
            </button>
          </div>
        </form>
      )}

      {scrapeMutation.isPending && (
        <LoadingIndicator
          isProcessingMultiple={isProcessing}
          progress={isProcessing ? progress : undefined}
        />
      )}

      {results && (
        <div className="flex flex-col gap-6">
          <ResultsTable data={results} />
          <div className="flex gap-4">
            <button
              onClick={handleReset}
              className="rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20"
            >
              New Search
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
