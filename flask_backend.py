#!/usr/bin/env python3
"""
Flask Backend for Business Scraper

This script creates a Flask API that serves as the backend for the business scraper.
It integrates the existing Python scraping functionality and provides API endpoints
for the React frontend to consume.
"""

import os
import json
import time
import asyncio
import csv
import io
import re
import socket
import requests
from typing import List, Dict, Any, Optional
from flask import Flask, request, jsonify
from flask_cors import CORS
from concurrent.futures import ThreadPoolExecutor
from urllib.parse import urlparse, parse_qs, unquote
import traceback
import sys
import tempfile
from werkzeug.utils import secure_filename

# Create Flask app
app = Flask(__name__)

# Set up a maximum file size limit
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB limit

# Enable CORS for all routes with more permissive settings
CORS(app, resources={r"/api/*": {"origins": "*", "supports_credentials": True, "methods": ["GET", "POST", "OPTIONS"]}})

# Add CORS headers to all responses
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')
    return response

@app.errorhandler(413)
def request_entity_too_large(error):
    """Handle file size too large errors"""
    return jsonify({"error": "File too large. Maximum size is 16MB"}), 413

# Import functions from csv_uploader.py
try:
    from csv_uploader import (
        clean_url,
        extract_phone,
        extract_with_requests,
        read_urls_from_csv,
        write_data_to_csv
    )
    print("Successfully imported functions from csv_uploader.py")
except ImportError as e:
    print(f"Error importing from csv_uploader.py: {e}")
    # Define fallback functions in case import fails
    def clean_url(url):
        """Fallback clean_url function if import fails"""
        if not url or not isinstance(url, str):
            return None
        url = url.strip()
        if not url:
            return None
        if not urlparse(url).scheme:
            return "https://" + url.lstrip('/')
        return url

    def extract_phone(text):
        """Fallback extract_phone function if import fails"""
        return "N/A"
        
    def extract_with_requests(url):
        """Fallback extract_with_requests function if import fails"""
        return {
            "page_title": "Fallback Title",
            "address": "N/A",
            "phone": "N/A",
            "social_links": "N/A"
        }
        
    def read_urls_from_csv():
        """Fallback read_urls_from_csv function if import fails"""
        return []
        
    def write_data_to_csv(data, output_file):
        """Fallback write_data_to_csv function if import fails"""
        pass

# Import functions from the existing scraper
try:
    from abcd import (
        extract_with_requests,
        scrape_duckduckgo,
        clean_url
    )
    from business_scraper import (
        get_cache_key,
        get_cached_data,
        save_to_cache
    )
    print("Successfully imported functions from abcd.py and business_scraper.py")
except ImportError as e:
    print(f"Error importing required modules: {e}")
    
    # Define fallback functions
    def extract_with_requests(url):
        """Fallback extract_with_requests function if import fails"""
        return {
            "page_title": "Fallback Title",
            "address": "N/A",
            "phone": "N/A",
            "social_links": "N/A"
        }
        
    async def scrape_duckduckgo(queries, max_results=5):
        """Fallback scrape_duckduckgo function if import fails"""
        return []
        
    def clean_url(url):
        """Fallback clean_url function if import fails"""
        if not url or not isinstance(url, str):
            return None
        url = url.strip()
        if not url:
            return None
        if not urlparse(url).scheme:
            return "https://" + url.lstrip('/')
        return url
        
    def get_cache_key(url):
        """Fallback get_cache_key function if import fails"""
        return url
        
    def get_cached_data(url):
        """Fallback get_cached_data function if import fails"""
        return None
        
    def save_to_cache(url, data):
        """Fallback save_to_cache function if import fails"""
        pass

# Maximum number of concurrent requests
MAX_WORKERS = 5

# Cache directory for storing scraped data
CACHE_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cache")
os.makedirs(CACHE_DIR, exist_ok=True)

def scrape_url(url: str, category: str, check_shopify: bool = False, check_active: bool = False) -> Optional[Dict[str, Any]]:
    """
    Scrape a single URL using the extract_with_requests function from abcd.py.
    Prioritizes results with more complete data and removes email field.

    Args:
        url: The URL to scrape
        category: The business category or search term
        check_shopify: If True, check if the site is a Shopify site
        check_active: If True, check if the domain is active

    Returns:
        Dictionary with business data or None if the URL doesn't match filter criteria
    """
    # Check cache first
    cached_data = get_cached_data(url)
    if cached_data:
        # Remove email field from cached data
        if 'email' in cached_data:
            del cached_data['email']
        return cached_data

    try:
        # Check if the site is a Shopify site if requested
        is_shopify = False
        if check_shopify:
            try:
                response = requests.get(url, timeout=10)
                is_shopify = "myshopify.com" in response.text or "shopify.com/shop" in response.text
            except Exception:
                pass

        # Check if the domain is active if requested
        is_active = True
        if check_active:
            try:
                domain = url.split("//")[-1].split("/")[0]
                socket.gethostbyname(domain)
            except Exception:
                is_active = False

        # Skip if we're filtering and the site doesn't match our criteria
        if (check_shopify and not is_shopify) or (check_active and not is_active):
            return None

        # Use the extract_with_requests function from abcd.py
        data = extract_with_requests(url)

        if data:
            # Transform the data to match our expected format
            business_data = {
                "name": data.get("page_title", "Unknown Business"),
                "address": data.get("address", "N/A"),
                "phone": data.get("phone", "N/A"),
                "url": url,
                "category": category,
                "social_links": data.get("social_links", "N/A"),
                "is_shopify": is_shopify,
                "is_active": is_active
            }

            # Calculate completeness score (higher is better)
            completeness_score = 0
            if business_data["name"] and business_data["name"] != "Unknown Business":
                completeness_score += 1
            if business_data["address"] and business_data["address"] != "N/A":
                completeness_score += 2  # Address is important, so give it more weight
            if business_data["phone"] and business_data["phone"] != "N/A":
                completeness_score += 1
            if business_data["social_links"] and business_data["social_links"] != "N/A":
                completeness_score += 1

            # Add the completeness score to the data
            business_data["completeness_score"] = completeness_score

            # Save to cache
            save_to_cache(url, business_data)

            return business_data
        else:
            # If extract_with_requests returned None, raise an exception
            raise Exception("Failed to extract data from URL")
    except Exception as e:
        print(f"Error scraping {url}: {e}")
        # Re-raise the exception to be handled by the caller
        raise

async def get_search_results(query: str, max_results: int = 5) -> List[Dict[str, str]]:
    """
    Get search results from DuckDuckGo for a given query.

    Args:
        query: The search query
        max_results: Maximum number of results to return

    Returns:
        List of dictionaries with title and URL
    """
    try:
        # Use the scrape_duckduckgo function from abcd.py
        results = await scrape_duckduckgo([query], max_results)
        return results
    except Exception as e:
        print(f"Error getting search results: {e}")
        return []

def scrape_business_data(
    search_term: str,
    location: str,
    country: str,
    max_results: int = 5,
    filter_shopify: bool = False,
    filter_active: bool = False
) -> List[Dict[str, Any]]:
    """
    Scrape business data using the abcd.py functions.

    Args:
        search_term: Business name, type, or any search term
        location: Location to search in
        country: Country for proxy selection
        max_results: Maximum number of results to return (default: 5)
        filter_shopify: If True, only return Shopify sites
        filter_active: If True, only return active domains

    Returns:
        List of business data dictionaries
    """
    try:
        # Construct search query using the search term
        search_query = f"{search_term} in {location}, {country}"
        print(f"Searching for: {search_query}")

        # Get search results from DuckDuckGo
        # We need to run the async function in a synchronous context
        search_results = asyncio.run(get_search_results(search_query, max_results))

        if not search_results:
            print("No search results found.")
            raise Exception("No search results found for the given query")

        # Extract URLs from search results
        urls = []
        for result in search_results:
            url = result.get("Result URL")
            if url:
                clean_result_url = clean_url(url)
                if clean_result_url:
                    urls.append(clean_result_url)

        if not urls:
            print("No valid URLs found in search results.")
            raise Exception("No valid URLs found in search results")

        print(f"Found {len(urls)} URLs to scrape")

        # Use ThreadPoolExecutor for concurrent scraping
        results = []
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # Submit all scraping tasks
            future_to_url = {
                executor.submit(scrape_url, url, search_term, filter_shopify, filter_active): url
                for url in urls
            }

            # Process results as they complete
            for future in future_to_url:
                url = future_to_url[future]
                try:
                    data = future.result()
                    if data:  # Only add non-None results
                        results.append(data)
                        # Print progress
                        print(f"Scraped {len(results)}/{len(urls)}: {url}")
                except Exception as e:
                    print(f"Error processing {url}: {e}")
                    # Skip failed URLs
                    continue

        if not results:
            print("No results were successfully scraped.")
            raise Exception("No results were successfully scraped")

        # Sort results by completeness score (higher scores first)
        sorted_results = sorted(results, key=lambda x: x.get('completeness_score', 0), reverse=True)

        # Remove completeness_score field from results
        for result in sorted_results:
            if 'completeness_score' in result:
                del result['completeness_score']

        return sorted_results

    except Exception as e:
        print(f"Error in scrape_business_data: {e}")
        # Re-raise the exception
        raise

@app.route('/api/scrape', methods=['POST'])
def scrape():
    """API endpoint for scraping business data."""
    try:
        # Get request data
        data = request.json

        # Validate request data
        if not data:
            return jsonify({"error": "No data provided"}), 400

        search_term = data.get('category')
        location = data.get('location')
        country = data.get('country')
        max_results = data.get('maxResults', 5)  # Default to 5 if not provided
        filter_shopify = data.get('filterShopify', False)  # Filter for Shopify sites
        filter_active = data.get('filterActive', False)  # Filter for active domains

        # Convert max_results to integer
        try:
            max_results = int(max_results)
            if max_results < 1:
                max_results = 5  # Ensure a minimum value
            elif max_results > 20:
                max_results = 20  # Cap at a reasonable maximum
        except (ValueError, TypeError):
            max_results = 5  # Default if conversion fails

        if not search_term:
            return jsonify({"error": "Search term is required"}), 400
        if not location:
            return jsonify({"error": "Location is required"}), 400
        if not country:
            return jsonify({"error": "Country is required"}), 400

        # Scrape business data
        results = scrape_business_data(
            search_term,
            location,
            country,
            max_results,
            filter_shopify=filter_shopify,
            filter_active=filter_active
        )

        # Return results
        return jsonify(results)

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/upload-csv', methods=['POST'])
def upload_csv():
    """API endpoint for processing CSV uploads using the robust CSV handling from abcd.py."""
    try:
        print("CSV upload request received")
        
        # Debug request information
        print(f"Request method: {request.method}")
        print(f"Request content type: {request.content_type}")
        print(f"Request files: {list(request.files.keys()) if request.files else 'None'}")
        print(f"Request form: {list(request.form.keys()) if request.form else 'None'}")

        # Check if file is present in request
        if 'file' not in request.files:
            print("No file in request")
            return jsonify({"error": "No file provided"}), 400

        file = request.files['file']
        print(f"File received: {file.filename}, Content Type: {file.content_type}")

        # Check if filename is empty
        if not file.filename or file.filename == '':
            print("Empty filename")
            return jsonify({"error": "No file selected"}), 400

        # Create a temporary file to save the uploaded content
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, secure_filename(file.filename))
        
        try:
            # Save the file to a temporary location
            file.save(temp_file_path)
            print(f"File saved temporarily to: {temp_file_path}")
            
            # Check if the file exists and is readable
            if not os.path.exists(temp_file_path):
                return jsonify({"error": "Failed to save uploaded file"}), 500
                
            # Check file size
            file_size = os.path.getsize(temp_file_path)
            print(f"File size: {file_size} bytes")
            
            if file_size == 0:
                return jsonify({"error": "The uploaded file is empty"}), 400
                
            # Try to read the file content to verify it's accessible
            with open(temp_file_path, 'rb') as f:
                file_preview = f.read(100)
                print(f"File preview (hex): {file_preview.hex()}")
                
            # Process the CSV file with detailed error handling
            try:
                # Read the file with different encodings
                encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'iso-8859-1', 'cp1252']
                file_content = None
                detected_encoding = None
                
                for encoding in encodings:
                    try:
                        with open(temp_file_path, 'r', encoding=encoding, errors='replace') as f:
                            file_content = f.read(1024)  # Read a sample
                            detected_encoding = encoding
                            print(f"Successfully read file with encoding: {encoding}")
                            print(f"File content preview: {file_content[:100]}")
                            break
                    except UnicodeDecodeError:
                        print(f"Failed to decode with encoding: {encoding}")
                        continue
                
                if not file_content or not detected_encoding:
                    return jsonify({"error": "Could not decode CSV file with any supported encoding"}), 400
                
                # Continue with your existing CSV processing logic...
                # ...
                
                # For now, just return a success message to test if we can get this far
                return jsonify({
                    "message": "File uploaded and processed successfully",
                    "filename": file.filename,
                    "size": file_size,
                    "encoding": detected_encoding,
                    "preview": file_content[:100]
                })
                
            except Exception as csv_error:
                error_details = {
                    "error": f"Error processing CSV: {str(csv_error)}",
                    "traceback": traceback.format_exc()
                }
                print(f"CSV processing error: {error_details}")
                return jsonify(error_details), 500
                
        finally:
            # Clean up the temporary file
            try:
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
                    print(f"Temporary file removed: {temp_file_path}")
            except Exception as cleanup_error:
                print(f"Error cleaning up temporary file: {cleanup_error}")
                
    except Exception as e:
        error_details = {
            "error": f"Server error: {str(e)}",
            "traceback": traceback.format_exc()
        }
        print(f"Server error in upload_csv: {error_details}")
        return jsonify(error_details), 500

@app.route('/api/upload-csv-with-uploader', methods=['POST'])
def upload_csv_with_uploader():
    """API endpoint for processing CSV uploads using csv_uploader.py."""
    try:
        print("CSV upload request received for csv_uploader.py")

        # Check if file is present in request
        if 'file' not in request.files:
            print("No file in request")
            return jsonify({"error": "No file provided"}), 400

        file = request.files['file']
        print(f"File received: {file.filename}, Content Type: {file.content_type}")

        # Check if filename is empty
        if not file.filename or file.filename == '':
            print("Empty filename")
            return jsonify({"error": "No file selected"}), 400

        # Check file size
        file_content = file.read()
        file.seek(0)  # Reset file pointer after reading

        if len(file_content) == 0:
            print("File is empty")
            return jsonify({"error": "The uploaded file is empty"}), 400

        # Create a temporary directory for csv_uploader.py
        temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
        os.makedirs(temp_dir, exist_ok=True)

        # Save the uploaded file temporarily
        temp_file_path = os.path.join(temp_dir, "temp_upload.csv")
        file.save(temp_file_path)

        # Set environment variables for csv_uploader.py
        os.environ['CSV_INPUT_FILE'] = temp_file_path
        output_file = os.path.join(temp_dir, "output.csv")
        os.environ['OUTPUT_FILE'] = output_file

        try:
            # Import the necessary functions from csv_uploader
            from csv_uploader import read_urls_from_csv, extract_with_requests

            # Read URLs from the CSV file
            urls = read_urls_from_csv()

            if not urls:
                return jsonify({"error": "No valid URLs found in the CSV file"}), 400

            print(f"Found {len(urls)} URLs to process")

            # Process each URL
            results = []
            for url in urls:
                try:
                    print(f"Processing URL: {url}")

                    # Use extract_with_requests from csv_uploader.py
                    data = extract_with_requests(url)

                    if data:
                        # Transform the data to match our expected format
                        business_data = {
                            "name": data.get("page_title", "Unknown Business"),
                            "address": data.get("address", "N/A"),
                            "phone": data.get("phone", "N/A"),
                            "url": url,
                            "category": "N/A",
                            "social_links": data.get("social_links", "N/A"),
                            "is_shopify": "myshopify.com" in data.get("source_url", "") or "shopify.com" in data.get("source_url", ""),
                            "is_active": True,  # Assume active since we could extract data
                            "status": "Success"
                        }

                        # Calculate completeness score
                        completeness_score = 0
                        if business_data["name"] and business_data["name"] != "Unknown Business":
                            completeness_score += 1
                        if business_data["address"] and business_data["address"] != "N/A":
                            completeness_score += 2  # Address is important
                        if business_data["phone"] and business_data["phone"] != "N/A":
                            completeness_score += 1
                        if business_data["social_links"] and business_data["social_links"] != "N/A":
                            completeness_score += 1

                        business_data["completeness_score"] = completeness_score
                        results.append(business_data)
                    else:
                        # If extraction failed, add basic info
                        domain = url.split("//")[-1].split("/")[0]
                        results.append({
                            "name": domain,
                            "address": "N/A",
                            "phone": "N/A",
                            "url": url,
                            "category": "N/A",
                            "social_links": "N/A",
                            "is_shopify": False,
                            "is_active": False,
                            "status": "Failed to extract data"
                        })
                except Exception as e:
                    print(f"Error processing URL {url}: {e}")
                    traceback.print_exc()

                    # Add error entry
                    domain = url.split("//")[-1].split("/")[0]
                    results.append({
                        "name": domain,
                        "address": "N/A",
                        "phone": "N/A",
                        "url": url,
                        "category": "N/A",
                        "social_links": "N/A",
                        "is_shopify": False,
                        "is_active": False,
                        "status": f"Error: {str(e)}"
                    })

            # Clean up temporary files
            try:
                os.remove(temp_file_path)
                if os.path.exists(output_file):
                    os.remove(output_file)
            except Exception as e:
                print(f"Error cleaning up temporary files: {e}")

            # Sort results by completeness score
            sorted_results = sorted(
                [r for r in results if "completeness_score" in r],
                key=lambda x: x.get('completeness_score', 0),
                reverse=True
            )

            # Add results without completeness score at the end
            sorted_results.extend([r for r in results if "completeness_score" not in r])

            # Remove completeness_score field from results
            for result in sorted_results:
                if 'completeness_score' in result:
                    del result['completeness_score']

            return jsonify(sorted_results)

        except Exception as e:
            print(f"Error using csv_uploader.py: {e}")
            traceback.print_exc()
            return jsonify({"error": f"Error using csv_uploader.py: {str(e)}"}), 500

    except Exception as e:
        print(f"Error in upload_csv_with_uploader endpoint: {str(e)}")
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({"status": "ok", "message": "Flask backend is running"})

@app.route('/api/test-upload', methods=['POST'])
def test_upload():
    """Simple test endpoint to verify file upload functionality."""
    try:
        print("Test upload request received")

        # Check if file is present in request
        if 'file' not in request.files:
            print("No file in request")
            return jsonify({"error": "No file provided"}), 400

        file = request.files['file']
        print(f"File received: {file.filename}, Content Type: {file.content_type}")

        # Check if filename is empty
        if not file.filename or file.filename == '':
            print("Empty filename")
            return jsonify({"error": "No file selected"}), 400

        # Read file content
        file_content = file.read()
        file.seek(0)  # Reset file pointer after reading

        if len(file_content) == 0:
            print("File is empty")
            return jsonify({"error": "The uploaded file is empty"}), 400

        # Just return basic info about the file without processing it
        return jsonify({
            "message": "File received successfully",
            "filename": file.filename,
            "content_type": file.content_type,
            "size": len(file_content),
            "preview": file_content[:100].decode('utf-8', errors='replace')
        })

    except Exception as e:
        print(f"Error in test_upload endpoint: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

@app.route('/api/simple-csv-upload', methods=['POST'])
def simple_csv_upload():
    """Simplified CSV upload endpoint with minimal processing."""
    try:
        print("Simple CSV upload request received")
        
        # Check if file is present in request
        if 'file' not in request.files:
            return jsonify({"error": "No file provided"}), 400

        file = request.files['file']
        
        # Check if filename is empty
        if not file.filename or file.filename == '':
            return jsonify({"error": "No file selected"}), 400

        # Create a temporary file
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, secure_filename(file.filename))
        
        try:
            # Save the file
            file.save(temp_file_path)
            
            # Read the first few lines to verify it's a CSV
            with open(temp_file_path, 'r', encoding='utf-8', errors='replace') as f:
                lines = [next(f, '') for _ in range(5)]
                
            # Return success with file info
            return jsonify({
                "success": True,
                "filename": file.filename,
                "size": os.path.getsize(temp_file_path),
                "preview": lines
            })
            
        finally:
            # Clean up
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
                
    except Exception as e:
        error_message = str(e)
        error_traceback = traceback.format_exc()
        print(f"Error in simple CSV upload: {error_message}")
        print(error_traceback)
        return jsonify({
            "error": error_message,
            "traceback": error_traceback
        }), 500

@app.route('/api/standalone-csv-upload', methods=['POST'])
def standalone_csv_upload():
    """Standalone CSV upload endpoint that doesn't rely on external modules."""
    try:
        print("Standalone CSV upload request received")
        
        # Check if file is present in request
        if 'file' not in request.files:
            return jsonify({"error": "No file provided"}), 400

        file = request.files['file']
        
        # Check if filename is empty
        if not file.filename or file.filename == '':
            return jsonify({"error": "No file selected"}), 400

        # Create a temporary file
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, secure_filename(file.filename))
        
        try:
            # Save the file
            file.save(temp_file_path)
            
            # Process the CSV file manually
            results = []
            
            try:
                # Try to read the CSV file
                with open(temp_file_path, 'r', encoding='utf-8', errors='replace') as f:
                    # Read the CSV file
                    csv_reader = csv.reader(f)
                    headers = next(csv_reader, [])
                    
                    # Check if there's a URL column
                    url_column_index = -1
                    for i, header in enumerate(headers):
                        if 'url' in header.lower():
                            url_column_index = i
                            break
                    
                    if url_column_index == -1 and len(headers) > 0:
                        # If no URL column found, use the first column
                        url_column_index = 0
                    
                    # Process each row
                    for row in csv_reader:
                        if len(row) > url_column_index:
                            url = row[url_column_index].strip()
                            if url:
                                # Create a simple result
                                domain = url.split("//")[-1].split("/")[0]
                                results.append({
                                    "name": domain,
                                    "address": "N/A",
                                    "phone": "N/A",
                                    "url": url,
                                    "category": "N/A",
                                    "social_links": "N/A",
                                    "is_shopify": False,
                                    "is_active": True,
                                    "status": "Success"
                                })
            
            except Exception as csv_error:
                print(f"Error processing CSV: {csv_error}")
                return jsonify({
                    "error": f"Error processing CSV: {str(csv_error)}",
                    "traceback": traceback.format_exc()
                }), 500
            
            # Return the results
            if results:
                return jsonify(results)
            else:
                return jsonify({"error": "No valid URLs found in the CSV file"}), 400
            
        finally:
            # Clean up
            if os.path.exists(temp_file_path):
                try:
                    os.remove(temp_file_path)
                except Exception as e:
                    print(f"Error removing temporary file: {e}")
                
    except Exception as e:
        error_message = str(e)
        error_traceback = traceback.format_exc()
        print(f"Error in standalone CSV upload: {error_message}")
        print(error_traceback)
        return jsonify({
            "error": error_message,
            "traceback": error_traceback
        }), 500

@app.errorhandler(Exception)
def handle_exception(e):
    """Handle all unhandled exceptions."""
    print(f"Unhandled exception: {str(e)}")
    traceback.print_exc()
    
    # Return JSON instead of HTML for exceptions
    response = jsonify({
        "error": str(e),
        "traceback": traceback.format_exc()
    })
    response.status_code = 500
    return response

if __name__ == '__main__':
    # Run the Flask app
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=True)











