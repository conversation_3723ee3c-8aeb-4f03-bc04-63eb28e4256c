"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[377],{1275:(e,t,r)=>{r.d(t,{X:()=>l});var n=r(2115),o=r(2712);function l(e){let[t,r]=n.useState(void 0);return(0,o.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},1285:(e,t,r)=>{r.d(t,{B:()=>s});var n,o=r(2115),l=r(2712),i=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function s(e){let[t,r]=o.useState(i());return(0,l.N)(()=>{e||r(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},1305:(e,t,r)=>{r.d(t,{H_:()=>tm,UC:()=>tc,YJ:()=>td,q7:()=>tp,VF:()=>tg,JU:()=>tf,ZL:()=>tu,z6:()=>th,hN:()=>tv,bL:()=>ta,wv:()=>ty,Pb:()=>tb,G5:()=>tx,ZP:()=>tw,l9:()=>ts});var n=r(2115),o=r(5185),l=r(6101),i=r(6081),a=r(5845),s=r(3655),u=r(7328),c=r(4315),d=r(9178),f=r(2293),p=r(7900),m=r(1285),h=r(5152),v=r(4378),g=r(2712),y=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[o,l]=n.useState(),i=n.useRef(null),a=n.useRef(e),s=n.useRef("none"),[u,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=b(i.current);s.current="mounted"===u?e:"none"},[u]),(0,g.N)(()=>{let t=i.current,r=a.current;if(r!==e){let n=s.current,o=b(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):r&&n!==o?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),(0,g.N)(()=>{if(o){var e;let t,r=null!=(e=o.ownerDocument.defaultView)?e:window,n=e=>{let n=b(i.current).includes(e.animationName);if(e.target===o&&n&&(c("ANIMATION_END"),!a.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},l=e=>{e.target===o&&(s.current=b(i.current))};return o.addEventListener("animationstart",l),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",l),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}c("ANIMATION_END")},[o,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:n.useCallback(e=>{i.current=e?getComputedStyle(e):null,l(e)},[])}}(t),i="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),a=(0,l.s)(o.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof r||o.isPresent?n.cloneElement(i,{ref:a}):null};function b(e){return(null==e?void 0:e.animationName)||"none"}y.displayName="Presence";var w=r(9033),x=r(5155),E="rovingFocusGroup.onEntryFocus",C={bubbles:!1,cancelable:!0},S="RovingFocusGroup",[k,R,M]=(0,u.N)(S),[j,A]=(0,i.A)(S,[M]),[P,N]=j(S),T=n.forwardRef((e,t)=>(0,x.jsx)(k.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,x.jsx)(k.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,x.jsx)(D,{...e,ref:t})})}));T.displayName=S;var D=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:u=!1,dir:d,currentTabStopId:f,defaultCurrentTabStopId:p,onCurrentTabStopIdChange:m,onEntryFocus:h,preventScrollOnEntryFocus:v=!1,...g}=e,y=n.useRef(null),b=(0,l.s)(t,y),k=(0,c.jH)(d),[M,j]=(0,a.i)({prop:f,defaultProp:null!=p?p:null,onChange:m,caller:S}),[A,N]=n.useState(!1),T=(0,w.c)(h),D=R(r),L=n.useRef(!1),[O,I]=n.useState(0);return n.useEffect(()=>{let e=y.current;if(e)return e.addEventListener(E,T),()=>e.removeEventListener(E,T)},[T]),(0,x.jsx)(P,{scope:r,orientation:i,dir:k,loop:u,currentTabStopId:M,onItemFocus:n.useCallback(e=>j(e),[j]),onItemShiftTab:n.useCallback(()=>N(!0),[]),onFocusableItemAdd:n.useCallback(()=>I(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>I(e=>e-1),[]),children:(0,x.jsx)(s.sG.div,{tabIndex:A||0===O?-1:0,"data-orientation":i,...g,ref:b,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!A){let t=new CustomEvent(E,C);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=D().filter(e=>e.focusable);_([e.find(e=>e.active),e.find(e=>e.id===M),...e].filter(Boolean).map(e=>e.ref.current),v)}}L.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>N(!1))})})}),L="RovingFocusGroupItem",O=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:l=!0,active:i=!1,tabStopId:a,children:u,...c}=e,d=(0,m.B)(),f=a||d,p=N(L,r),h=p.currentTabStopId===f,v=R(r),{onFocusableItemAdd:g,onFocusableItemRemove:y,currentTabStopId:b}=p;return n.useEffect(()=>{if(l)return g(),()=>y()},[l,g,y]),(0,x.jsx)(k.ItemSlot,{scope:r,id:f,focusable:l,active:i,children:(0,x.jsx)(s.sG.span,{tabIndex:h?0:-1,"data-orientation":p.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{l?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void p.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return I[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>_(r))}}),children:"function"==typeof u?u({isCurrentTabStop:h,hasTabStop:null!=b}):u})})});O.displayName=L;var I={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function _(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var F=r(9708),z=r(8168),B=r(3795),W=["Enter"," "],G=["ArrowUp","PageDown","End"],H=["ArrowDown","PageUp","Home",...G],K={ltr:[...W,"ArrowRight"],rtl:[...W,"ArrowLeft"]},U={ltr:["ArrowLeft"],rtl:["ArrowRight"]},V="Menu",[q,$,X]=(0,u.N)(V),[Y,Z]=(0,i.A)(V,[X,h.Bk,A]),J=(0,h.Bk)(),Q=A(),[ee,et]=Y(V),[er,en]=Y(V),eo=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:l,onOpenChange:i,modal:a=!0}=e,s=J(t),[u,d]=n.useState(null),f=n.useRef(!1),p=(0,w.c)(i),m=(0,c.jH)(l);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,x.jsx)(h.bL,{...s,children:(0,x.jsx)(ee,{scope:t,open:r,onOpenChange:p,content:u,onContentChange:d,children:(0,x.jsx)(er,{scope:t,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:a,children:o})})})};eo.displayName=V;var el=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=J(r);return(0,x.jsx)(h.Mz,{...o,...n,ref:t})});el.displayName="MenuAnchor";var ei="MenuPortal",[ea,es]=Y(ei,{forceMount:void 0}),eu=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,l=et(ei,t);return(0,x.jsx)(ea,{scope:t,forceMount:r,children:(0,x.jsx)(y,{present:r||l.open,children:(0,x.jsx)(v.Z,{asChild:!0,container:o,children:n})})})};eu.displayName=ei;var ec="MenuContent",[ed,ef]=Y(ec),ep=n.forwardRef((e,t)=>{let r=es(ec,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,l=et(ec,e.__scopeMenu),i=en(ec,e.__scopeMenu);return(0,x.jsx)(q.Provider,{scope:e.__scopeMenu,children:(0,x.jsx)(y,{present:n||l.open,children:(0,x.jsx)(q.Slot,{scope:e.__scopeMenu,children:i.modal?(0,x.jsx)(em,{...o,ref:t}):(0,x.jsx)(eh,{...o,ref:t})})})})}),em=n.forwardRef((e,t)=>{let r=et(ec,e.__scopeMenu),i=n.useRef(null),a=(0,l.s)(t,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,z.Eq)(e)},[]),(0,x.jsx)(eg,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),eh=n.forwardRef((e,t)=>{let r=et(ec,e.__scopeMenu);return(0,x.jsx)(eg,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),ev=(0,F.TL)("MenuContent.ScrollLock"),eg=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:i=!1,trapFocus:a,onOpenAutoFocus:s,onCloseAutoFocus:u,disableOutsidePointerEvents:c,onEntryFocus:m,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:b,onDismiss:w,disableOutsideScroll:E,...C}=e,S=et(ec,r),k=en(ec,r),R=J(r),M=Q(r),j=$(r),[A,P]=n.useState(null),N=n.useRef(null),D=(0,l.s)(t,N,S.onContentChange),L=n.useRef(0),O=n.useRef(""),I=n.useRef(0),_=n.useRef(null),F=n.useRef("right"),z=n.useRef(0),W=E?B.A:n.Fragment,K=e=>{var t,r;let n=O.current+e,o=j().filter(e=>!e.disabled),l=document.activeElement,i=null==(t=o.find(e=>e.ref.current===l))?void 0:t.textValue,a=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=r?e.indexOf(r):-1,i=(n=Math.max(l,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(i=i.filter(e=>e!==r));let a=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==r?a:void 0}(o.map(e=>e.textValue),n,i),s=null==(r=o.find(e=>e.textValue===a))?void 0:r.ref.current;!function e(t){O.current=t,window.clearTimeout(L.current),""!==t&&(L.current=window.setTimeout(()=>e(""),1e3))}(n),s&&setTimeout(()=>s.focus())};n.useEffect(()=>()=>window.clearTimeout(L.current),[]),(0,f.Oh)();let U=n.useCallback(e=>{var t,r;return F.current===(null==(t=_.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,l=t.length-1;e<t.length;l=e++){let i=t[e],a=t[l],s=i.x,u=i.y,c=a.x,d=a.y;u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(r=_.current)?void 0:r.area)},[]);return(0,x.jsx)(ed,{scope:r,searchRef:O,onItemEnter:n.useCallback(e=>{U(e)&&e.preventDefault()},[U]),onItemLeave:n.useCallback(e=>{var t;U(e)||(null==(t=N.current)||t.focus(),P(null))},[U]),onTriggerLeave:n.useCallback(e=>{U(e)&&e.preventDefault()},[U]),pointerGraceTimerRef:I,onPointerGraceIntentChange:n.useCallback(e=>{_.current=e},[]),children:(0,x.jsx)(W,{...E?{as:ev,allowPinchZoom:!0}:void 0,children:(0,x.jsx)(p.n,{asChild:!0,trapped:a,onMountAutoFocus:(0,o.m)(s,e=>{var t;e.preventDefault(),null==(t=N.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,x.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:b,onDismiss:w,children:(0,x.jsx)(T,{asChild:!0,...M,dir:k.dir,orientation:"vertical",loop:i,currentTabStopId:A,onCurrentTabStopIdChange:P,onEntryFocus:(0,o.m)(m,e=>{k.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,x.jsx)(h.UC,{role:"menu","aria-orientation":"vertical","data-state":eU(S.open),"data-radix-menu-content":"",dir:k.dir,...R,...C,ref:D,style:{outline:"none",...C.style},onKeyDown:(0,o.m)(C.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&K(e.key));let o=N.current;if(e.target!==o||!H.includes(e.key))return;e.preventDefault();let l=j().filter(e=>!e.disabled).map(e=>e.ref.current);G.includes(e.key)&&l.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(l)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(L.current),O.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,e$(e=>{let t=e.target,r=z.current!==e.clientX;e.currentTarget.contains(t)&&r&&(F.current=e.clientX>z.current?"right":"left",z.current=e.clientX)}))})})})})})})});ep.displayName=ec;var ey=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,x.jsx)(s.sG.div,{role:"group",...n,ref:t})});ey.displayName="MenuGroup";var eb=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,x.jsx)(s.sG.div,{...n,ref:t})});eb.displayName="MenuLabel";var ew="MenuItem",ex="menu.itemSelect",eE=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:i,...a}=e,u=n.useRef(null),c=en(ew,e.__scopeMenu),d=ef(ew,e.__scopeMenu),f=(0,l.s)(t,u),p=n.useRef(!1);return(0,x.jsx)(eC,{...a,ref:f,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=u.current;if(!r&&e){let t=new CustomEvent(ex,{bubbles:!0,cancelable:!0});e.addEventListener(ex,e=>null==i?void 0:i(e),{once:!0}),(0,s.hO)(e,t),t.defaultPrevented?p.current=!1:c.onClose()}}),onPointerDown:t=>{var r;null==(r=e.onPointerDown)||r.call(e,t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var t;p.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;r||t&&" "===e.key||W.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eE.displayName=ew;var eC=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:i=!1,textValue:a,...u}=e,c=ef(ew,r),d=Q(r),f=n.useRef(null),p=(0,l.s)(t,f),[m,h]=n.useState(!1),[v,g]=n.useState("");return n.useEffect(()=>{let e=f.current;if(e){var t;g((null!=(t=e.textContent)?t:"").trim())}},[u.children]),(0,x.jsx)(q.ItemSlot,{scope:r,disabled:i,textValue:null!=a?a:v,children:(0,x.jsx)(O,{asChild:!0,...d,focusable:!i,children:(0,x.jsx)(s.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...u,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,e$(e=>{i?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,e$(e=>c.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>h(!0)),onBlur:(0,o.m)(e.onBlur,()=>h(!1))})})})}),eS=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...l}=e;return(0,x.jsx)(eT,{scope:e.__scopeMenu,checked:r,children:(0,x.jsx)(eE,{role:"menuitemcheckbox","aria-checked":eV(r)?"mixed":r,...l,ref:t,"data-state":eq(r),onSelect:(0,o.m)(l.onSelect,()=>null==n?void 0:n(!!eV(r)||!r),{checkForDefaultPrevented:!1})})})});eS.displayName="MenuCheckboxItem";var ek="MenuRadioGroup",[eR,eM]=Y(ek,{value:void 0,onValueChange:()=>{}}),ej=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,l=(0,w.c)(n);return(0,x.jsx)(eR,{scope:e.__scopeMenu,value:r,onValueChange:l,children:(0,x.jsx)(ey,{...o,ref:t})})});ej.displayName=ek;var eA="MenuRadioItem",eP=n.forwardRef((e,t)=>{let{value:r,...n}=e,l=eM(eA,e.__scopeMenu),i=r===l.value;return(0,x.jsx)(eT,{scope:e.__scopeMenu,checked:i,children:(0,x.jsx)(eE,{role:"menuitemradio","aria-checked":i,...n,ref:t,"data-state":eq(i),onSelect:(0,o.m)(n.onSelect,()=>{var e;return null==(e=l.onValueChange)?void 0:e.call(l,r)},{checkForDefaultPrevented:!1})})})});eP.displayName=eA;var eN="MenuItemIndicator",[eT,eD]=Y(eN,{checked:!1}),eL=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,l=eD(eN,r);return(0,x.jsx)(y,{present:n||eV(l.checked)||!0===l.checked,children:(0,x.jsx)(s.sG.span,{...o,ref:t,"data-state":eq(l.checked)})})});eL.displayName=eN;var eO=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,x.jsx)(s.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});eO.displayName="MenuSeparator";var eI=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=J(r);return(0,x.jsx)(h.i3,{...o,...n,ref:t})});eI.displayName="MenuArrow";var e_="MenuSub",[eF,ez]=Y(e_),eB=e=>{let{__scopeMenu:t,children:r,open:o=!1,onOpenChange:l}=e,i=et(e_,t),a=J(t),[s,u]=n.useState(null),[c,d]=n.useState(null),f=(0,w.c)(l);return n.useEffect(()=>(!1===i.open&&f(!1),()=>f(!1)),[i.open,f]),(0,x.jsx)(h.bL,{...a,children:(0,x.jsx)(ee,{scope:t,open:o,onOpenChange:f,content:c,onContentChange:d,children:(0,x.jsx)(eF,{scope:t,contentId:(0,m.B)(),triggerId:(0,m.B)(),trigger:s,onTriggerChange:u,children:r})})})};eB.displayName=e_;var eW="MenuSubTrigger",eG=n.forwardRef((e,t)=>{let r=et(eW,e.__scopeMenu),i=en(eW,e.__scopeMenu),a=ez(eW,e.__scopeMenu),s=ef(eW,e.__scopeMenu),u=n.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=s,f={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,x.jsx)(el,{asChild:!0,...f,children:(0,x.jsx)(eC,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":a.contentId,"data-state":eU(r.open),...e,ref:(0,l.t)(t,a.onTriggerChange),onClick:t=>{var n;null==(n=e.onClick)||n.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,e$(t=>{s.onItemEnter(t),!t.defaultPrevented&&(e.disabled||r.open||u.current||(s.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,e$(e=>{var t,n;p();let o=null==(t=r.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(n=r.content)?void 0:n.dataset.side,l="right"===t,i=o[l?"left":"right"],a=o[l?"right":"left"];s.onPointerGraceIntentChange({area:[{x:e.clientX+(l?-5:5),y:e.clientY},{x:i,y:o.top},{x:a,y:o.top},{x:a,y:o.bottom},{x:i,y:o.bottom}],side:t}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(e),e.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let n=""!==s.searchRef.current;if(!e.disabled&&(!n||" "!==t.key)&&K[i.dir].includes(t.key)){var o;r.onOpenChange(!0),null==(o=r.content)||o.focus(),t.preventDefault()}})})})});eG.displayName=eW;var eH="MenuSubContent",eK=n.forwardRef((e,t)=>{let r=es(ec,e.__scopeMenu),{forceMount:i=r.forceMount,...a}=e,s=et(ec,e.__scopeMenu),u=en(ec,e.__scopeMenu),c=ez(eH,e.__scopeMenu),d=n.useRef(null),f=(0,l.s)(t,d);return(0,x.jsx)(q.Provider,{scope:e.__scopeMenu,children:(0,x.jsx)(y,{present:i||s.open,children:(0,x.jsx)(q.Slot,{scope:e.__scopeMenu,children:(0,x.jsx)(eg,{id:c.contentId,"aria-labelledby":c.triggerId,...a,ref:f,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;u.isUsingKeyboardRef.current&&(null==(t=d.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=U[u.dir].includes(e.key);if(t&&r){var n;s.onOpenChange(!1),null==(n=c.trigger)||n.focus(),e.preventDefault()}})})})})})});function eU(e){return e?"open":"closed"}function eV(e){return"indeterminate"===e}function eq(e){return eV(e)?"indeterminate":e?"checked":"unchecked"}function e$(e){return t=>"mouse"===t.pointerType?e(t):void 0}eK.displayName=eH;var eX="DropdownMenu",[eY,eZ]=(0,i.A)(eX,[Z]),eJ=Z(),[eQ,e0]=eY(eX),e1=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:l,defaultOpen:i,onOpenChange:s,modal:u=!0}=e,c=eJ(t),d=n.useRef(null),[f,p]=(0,a.i)({prop:l,defaultProp:null!=i&&i,onChange:s,caller:eX});return(0,x.jsx)(eQ,{scope:t,triggerId:(0,m.B)(),triggerRef:d,contentId:(0,m.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:u,children:(0,x.jsx)(eo,{...c,open:f,onOpenChange:p,dir:o,modal:u,children:r})})};e1.displayName=eX;var e2="DropdownMenuTrigger",e5=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...i}=e,a=e0(e2,r),u=eJ(r);return(0,x.jsx)(el,{asChild:!0,...u,children:(0,x.jsx)(s.sG.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...i,ref:(0,l.t)(t,a.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(a.onOpenToggle(),a.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&a.onOpenToggle(),"ArrowDown"===e.key&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e5.displayName=e2;var e9=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eJ(t);return(0,x.jsx)(eu,{...n,...r})};e9.displayName="DropdownMenuPortal";var e6="DropdownMenuContent",e3=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...l}=e,i=e0(e6,r),a=eJ(r),s=n.useRef(!1);return(0,x.jsx)(ep,{id:i.contentId,"aria-labelledby":i.triggerId,...a,...l,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;s.current||null==(t=i.triggerRef.current)||t.focus(),s.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!i.modal||n)&&(s.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e3.displayName=e6;var e4=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(ey,{...o,...n,ref:t})});e4.displayName="DropdownMenuGroup";var e8=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eb,{...o,...n,ref:t})});e8.displayName="DropdownMenuLabel";var e7=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eE,{...o,...n,ref:t})});e7.displayName="DropdownMenuItem";var te=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eS,{...o,...n,ref:t})});te.displayName="DropdownMenuCheckboxItem";var tt=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(ej,{...o,...n,ref:t})});tt.displayName="DropdownMenuRadioGroup";var tr=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eP,{...o,...n,ref:t})});tr.displayName="DropdownMenuRadioItem";var tn=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eL,{...o,...n,ref:t})});tn.displayName="DropdownMenuItemIndicator";var to=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eO,{...o,...n,ref:t})});to.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eI,{...o,...n,ref:t})}).displayName="DropdownMenuArrow";var tl=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eG,{...o,...n,ref:t})});tl.displayName="DropdownMenuSubTrigger";var ti=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eJ(r);return(0,x.jsx)(eK,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ti.displayName="DropdownMenuSubContent";var ta=e1,ts=e5,tu=e9,tc=e3,td=e4,tf=e8,tp=e7,tm=te,th=tt,tv=tr,tg=tn,ty=to,tb=e=>{let{__scopeDropdownMenu:t,children:r,open:n,onOpenChange:o,defaultOpen:l}=e,i=eJ(t),[s,u]=(0,a.i)({prop:n,defaultProp:null!=l&&l,onChange:o,caller:"DropdownMenuSub"});return(0,x.jsx)(eB,{...i,open:s,onOpenChange:u,children:r})},tw=tl,tx=ti},2085:(e,t,r)=>{r.d(t,{F:()=>i});var n=r(2596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=n.$,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:a}=t,s=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==a?void 0:a[e];if(null===t)return null;let l=o(t)||o(n);return i[e][l]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return l(e,s,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...u}[t]):({...a,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2098:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2293:(e,t,r)=>{r.d(t,{Oh:()=>l});var n=r(2115),o=0;function l(){n.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=r[0])?e:i()),document.body.insertAdjacentElement("beforeend",null!=(t=r[1])?t:i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2596:(e,t,r)=>{r.d(t,{$:()=>n});function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var l=t.length;for(r=0;r<l;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=t);return n}},2712:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(2115),o=globalThis?.document?n.useLayoutEffect:()=>{}},2815:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},2918:(e,t,r)=>{r.d(t,{UC:()=>eO,YJ:()=>e_,In:()=>eD,q7:()=>ez,VF:()=>eW,p4:()=>eB,JU:()=>eF,ZL:()=>eL,bL:()=>eP,wn:()=>eH,PP:()=>eG,wv:()=>eK,l9:()=>eN,WT:()=>eT,LM:()=>eI});var n=r(2115),o=r(7650),l=r(9367),i=r(5185),a=r(7328),s=r(6101),u=r(6081),c=r(4315),d=r(9178),f=r(2293),p=r(7900),m=r(1285),h=r(5152),v=r(4378),g=r(3655),y=r(9708),b=r(9033),w=r(5845),x=r(2712),E=r(5503),C=r(5155),S=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});n.forwardRef((e,t)=>(0,C.jsx)(g.sG.span,{...e,ref:t,style:{...S,...e.style}})).displayName="VisuallyHidden";var k=r(8168),R=r(3795),M=[" ","Enter","ArrowUp","ArrowDown"],j=[" ","Enter"],A="Select",[P,N,T]=(0,a.N)(A),[D,L]=(0,u.A)(A,[T,h.Bk]),O=(0,h.Bk)(),[I,_]=D(A),[F,z]=D(A),B=e=>{let{__scopeSelect:t,children:r,open:o,defaultOpen:l,onOpenChange:i,value:a,defaultValue:s,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:v,required:g,form:y}=e,b=O(t),[x,E]=n.useState(null),[S,k]=n.useState(null),[R,M]=n.useState(!1),j=(0,c.jH)(d),[N,T]=(0,w.i)({prop:o,defaultProp:null!=l&&l,onChange:i,caller:A}),[D,L]=(0,w.i)({prop:a,defaultProp:s,onChange:u,caller:A}),_=n.useRef(null),z=!x||y||!!x.closest("form"),[B,W]=n.useState(new Set),G=Array.from(B).map(e=>e.props.value).join(";");return(0,C.jsx)(h.bL,{...b,children:(0,C.jsxs)(I,{required:g,scope:t,trigger:x,onTriggerChange:E,valueNode:S,onValueNodeChange:k,valueNodeHasChildren:R,onValueNodeHasChildrenChange:M,contentId:(0,m.B)(),value:D,onValueChange:L,open:N,onOpenChange:T,dir:j,triggerPointerDownPosRef:_,disabled:v,children:[(0,C.jsx)(P.Provider,{scope:t,children:(0,C.jsx)(F,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{W(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{W(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),z?(0,C.jsxs)(eR,{"aria-hidden":!0,required:g,tabIndex:-1,name:f,autoComplete:p,value:D,onChange:e=>L(e.target.value),disabled:v,form:y,children:[void 0===D?(0,C.jsx)("option",{value:""}):null,Array.from(B)]},G):null]})})};B.displayName=A;var W="SelectTrigger",G=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:o=!1,...l}=e,a=O(r),u=_(W,r),c=u.disabled||o,d=(0,s.s)(t,u.onTriggerChange),f=N(r),p=n.useRef("touch"),[m,v,y]=ej(e=>{let t=f().filter(e=>!e.disabled),r=t.find(e=>e.value===u.value),n=eA(t,e,r);void 0!==n&&u.onValueChange(n.value)}),b=e=>{c||(u.onOpenChange(!0),y()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,C.jsx)(h.Mz,{asChild:!0,...a,children:(0,C.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":eM(u.value)?"":void 0,...l,ref:d,onClick:(0,i.m)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&b(e)}),onPointerDown:(0,i.m)(l.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(b(e),e.preventDefault())}),onKeyDown:(0,i.m)(l.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&M.includes(e.key)&&(b(),e.preventDefault())})})})});G.displayName=W;var H="SelectValue",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,children:l,placeholder:i="",...a}=e,u=_(H,r),{onValueNodeHasChildrenChange:c}=u,d=void 0!==l,f=(0,s.s)(t,u.onValueNodeChange);return(0,x.N)(()=>{c(d)},[c,d]),(0,C.jsx)(g.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:eM(u.value)?(0,C.jsx)(C.Fragment,{children:i}):l})});K.displayName=H;var U=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...o}=e;return(0,C.jsx)(g.sG.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});U.displayName="SelectIcon";var V=e=>(0,C.jsx)(v.Z,{asChild:!0,...e});V.displayName="SelectPortal";var q="SelectContent",$=n.forwardRef((e,t)=>{let r=_(q,e.__scopeSelect),[l,i]=n.useState();return((0,x.N)(()=>{i(new DocumentFragment)},[]),r.open)?(0,C.jsx)(J,{...e,ref:t}):l?o.createPortal((0,C.jsx)(X,{scope:e.__scopeSelect,children:(0,C.jsx)(P.Slot,{scope:e.__scopeSelect,children:(0,C.jsx)("div",{children:e.children})})}),l):null});$.displayName=q;var[X,Y]=D(q),Z=(0,y.TL)("SelectContent.RemoveScroll"),J=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:o="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:a,onPointerDownOutside:u,side:c,sideOffset:m,align:h,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:b,sticky:w,hideWhenDetached:x,avoidCollisions:E,...S}=e,M=_(q,r),[j,A]=n.useState(null),[P,T]=n.useState(null),D=(0,s.s)(t,e=>A(e)),[L,O]=n.useState(null),[I,F]=n.useState(null),z=N(r),[B,W]=n.useState(!1),G=n.useRef(!1);n.useEffect(()=>{if(j)return(0,k.Eq)(j)},[j]),(0,f.Oh)();let H=n.useCallback(e=>{let[t,...r]=z().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&P&&(P.scrollTop=0),r===n&&P&&(P.scrollTop=P.scrollHeight),null==r||r.focus(),document.activeElement!==o))return},[z,P]),K=n.useCallback(()=>H([L,j]),[H,L,j]);n.useEffect(()=>{B&&K()},[B,K]);let{onOpenChange:U,triggerPointerDownPosRef:V}=M;n.useEffect(()=>{if(j){let e={x:0,y:0},t=t=>{var r,n,o,l;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(r=V.current)?void 0:r.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(l=null==(n=V.current)?void 0:n.y)?l:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():j.contains(r.target)||U(!1),document.removeEventListener("pointermove",t),V.current=null};return null!==V.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[j,U,V]),n.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[$,Y]=ej(e=>{let t=z().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eA(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),J=n.useCallback((e,t,r)=>{let n=!G.current&&!r;(void 0!==M.value&&M.value===t||n)&&(O(e),n&&(G.current=!0))},[M.value]),et=n.useCallback(()=>null==j?void 0:j.focus(),[j]),er=n.useCallback((e,t,r)=>{let n=!G.current&&!r;(void 0!==M.value&&M.value===t||n)&&F(e)},[M.value]),en="popper"===o?ee:Q,eo=en===ee?{side:c,sideOffset:m,align:h,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:b,sticky:w,hideWhenDetached:x,avoidCollisions:E}:{};return(0,C.jsx)(X,{scope:r,content:j,viewport:P,onViewportChange:T,itemRefCallback:J,selectedItem:L,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:K,selectedItemText:I,position:o,isPositioned:B,searchRef:$,children:(0,C.jsx)(R.A,{as:Z,allowPinchZoom:!0,children:(0,C.jsx)(p.n,{asChild:!0,trapped:M.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,i.m)(l,e=>{var t;null==(t=M.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,C.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>M.onOpenChange(!1),children:(0,C.jsx)(en,{role:"listbox",id:M.contentId,"data-state":M.open?"open":"closed",dir:M.dir,onContextMenu:e=>e.preventDefault(),...S,...eo,onPlaced:()=>W(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...S.style},onKeyDown:(0,i.m)(S.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=z().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>H(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:o,...i}=e,a=_(q,r),u=Y(q,r),[c,d]=n.useState(null),[f,p]=n.useState(null),m=(0,s.s)(t,e=>p(e)),h=N(r),v=n.useRef(!1),y=n.useRef(!0),{viewport:b,selectedItem:w,selectedItemText:E,focusSelectedItem:S}=u,k=n.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&f&&b&&w&&E){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),r=a.valueNode.getBoundingClientRect(),n=E.getBoundingClientRect();if("rtl"!==a.dir){let o=n.left-t.left,i=r.left-o,a=e.left-i,s=e.width+a,u=Math.max(s,t.width),d=window.innerWidth-10,f=(0,l.q)(i,[10,Math.max(10,d-u)]);c.style.minWidth=s+"px",c.style.left=f+"px"}else{let o=t.right-n.right,i=window.innerWidth-r.right-o,a=window.innerWidth-e.right-i,s=e.width+a,u=Math.max(s,t.width),d=window.innerWidth-10,f=(0,l.q)(i,[10,Math.max(10,d-u)]);c.style.minWidth=s+"px",c.style.right=f+"px"}let i=h(),s=window.innerHeight-20,u=b.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),m=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),y=p+m+u+parseInt(d.paddingBottom,10)+g,x=Math.min(5*w.offsetHeight,y),C=window.getComputedStyle(b),S=parseInt(C.paddingTop,10),k=parseInt(C.paddingBottom,10),R=e.top+e.height/2-10,M=w.offsetHeight/2,j=p+m+(w.offsetTop+M);if(j<=R){let e=i.length>0&&w===i[i.length-1].ref.current;c.style.bottom="0px";let t=Math.max(s-R,M+(e?k:0)+(f.clientHeight-b.offsetTop-b.offsetHeight)+g);c.style.height=j+t+"px"}else{let e=i.length>0&&w===i[0].ref.current;c.style.top="0px";let t=Math.max(R,p+b.offsetTop+(e?S:0)+M);c.style.height=t+(y-j)+"px",b.scrollTop=j-R+b.offsetTop}c.style.margin="".concat(10,"px 0"),c.style.minHeight=x+"px",c.style.maxHeight=s+"px",null==o||o(),requestAnimationFrame(()=>v.current=!0)}},[h,a.trigger,a.valueNode,c,f,b,w,E,a.dir,o]);(0,x.N)(()=>k(),[k]);let[R,M]=n.useState();(0,x.N)(()=>{f&&M(window.getComputedStyle(f).zIndex)},[f]);let j=n.useCallback(e=>{e&&!0===y.current&&(k(),null==S||S(),y.current=!1)},[k,S]);return(0,C.jsx)(et,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:v,onScrollButtonChange:j,children:(0,C.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,C.jsx)(g.sG.div,{...i,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...l}=e,i=O(r);return(0,C.jsx)(h.UC,{...i,...l,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=D(q,{}),en="SelectViewport",eo=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:o,...l}=e,a=Y(en,r),u=er(en,r),c=(0,s.s)(t,a.onViewportChange),d=n.useRef(0);return(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,C.jsx)(P.Slot,{scope:r,children:(0,C.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,i.m)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=u;if((null==n?void 0:n.current)&&r){let e=Math.abs(d.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let l=o+e,i=Math.min(n,l),a=l-i;r.style.height=i+"px","0px"===r.style.bottom&&(t.scrollTop=a>0?a:0,r.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});eo.displayName=en;var el="SelectGroup",[ei,ea]=D(el),es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=(0,m.B)();return(0,C.jsx)(ei,{scope:r,id:o,children:(0,C.jsx)(g.sG.div,{role:"group","aria-labelledby":o,...n,ref:t})})});es.displayName=el;var eu="SelectLabel",ec=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=ea(eu,r);return(0,C.jsx)(g.sG.div,{id:o.id,...n,ref:t})});ec.displayName=eu;var ed="SelectItem",[ef,ep]=D(ed),em=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:o,disabled:l=!1,textValue:a,...u}=e,c=_(ed,r),d=Y(ed,r),f=c.value===o,[p,h]=n.useState(null!=a?a:""),[v,y]=n.useState(!1),b=(0,s.s)(t,e=>{var t;return null==(t=d.itemRefCallback)?void 0:t.call(d,e,o,l)}),w=(0,m.B)(),x=n.useRef("touch"),E=()=>{l||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,C.jsx)(ef,{scope:r,value:o,disabled:l,textId:w,isSelected:f,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!=(r=null==e?void 0:e.textContent)?r:"").trim()})},[]),children:(0,C.jsx)(P.ItemSlot,{scope:r,value:o,disabled:l,textValue:p,children:(0,C.jsx)(g.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...u,ref:b,onFocus:(0,i.m)(u.onFocus,()=>y(!0)),onBlur:(0,i.m)(u.onBlur,()=>y(!1)),onClick:(0,i.m)(u.onClick,()=>{"mouse"!==x.current&&E()}),onPointerUp:(0,i.m)(u.onPointerUp,()=>{"mouse"===x.current&&E()}),onPointerDown:(0,i.m)(u.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,i.m)(u.onPointerMove,e=>{if(x.current=e.pointerType,l){var t;null==(t=d.onItemLeave)||t.call(d)}else"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,i.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=d.onItemLeave)||t.call(d)}}),onKeyDown:(0,i.m)(u.onKeyDown,e=>{var t;((null==(t=d.searchRef)?void 0:t.current)===""||" "!==e.key)&&(j.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});em.displayName=ed;var eh="SelectItemText",ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:l,style:i,...a}=e,u=_(eh,r),c=Y(eh,r),d=ep(eh,r),f=z(eh,r),[p,m]=n.useState(null),h=(0,s.s)(t,e=>m(e),d.onItemTextChange,e=>{var t;return null==(t=c.itemTextRefCallback)?void 0:t.call(c,e,d.value,d.disabled)}),v=null==p?void 0:p.textContent,y=n.useMemo(()=>(0,C.jsx)("option",{value:d.value,disabled:d.disabled,children:v},d.value),[d.disabled,d.value,v]),{onNativeOptionAdd:b,onNativeOptionRemove:w}=f;return(0,x.N)(()=>(b(y),()=>w(y)),[b,w,y]),(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(g.sG.span,{id:d.textId,...a,ref:h}),d.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(a.children,u.valueNode):null]})});ev.displayName=eh;var eg="SelectItemIndicator",ey=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ep(eg,r).isSelected?(0,C.jsx)(g.sG.span,{"aria-hidden":!0,...n,ref:t}):null});ey.displayName=eg;var eb="SelectScrollUpButton",ew=n.forwardRef((e,t)=>{let r=Y(eb,e.__scopeSelect),o=er(eb,e.__scopeSelect),[l,i]=n.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,x.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){i(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,C.jsx)(eC,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=eb;var ex="SelectScrollDownButton",eE=n.forwardRef((e,t)=>{let r=Y(ex,e.__scopeSelect),o=er(ex,e.__scopeSelect),[l,i]=n.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,x.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,C.jsx)(eC,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eE.displayName=ex;var eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:o,...l}=e,a=Y("SelectScrollButton",r),s=n.useRef(null),u=N(r),c=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>c(),[c]),(0,x.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,C.jsx)(g.sG.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,i.m)(l.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,i.m)(l.onPointerMove,()=>{var e;null==(e=a.onItemLeave)||e.call(a),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,i.m)(l.onPointerLeave,()=>{c()})})}),eS=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,C.jsx)(g.sG.div,{"aria-hidden":!0,...n,ref:t})});eS.displayName="SelectSeparator";var ek="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=O(r),l=_(ek,r),i=Y(ek,r);return l.open&&"popper"===i.position?(0,C.jsx)(h.i3,{...o,...n,ref:t}):null}).displayName=ek;var eR=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:o,...l}=e,i=n.useRef(null),a=(0,s.s)(t,i),u=(0,E.Z)(o);return n.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==o&&t){let r=new Event("change",{bubbles:!0});t.call(e,o),e.dispatchEvent(r)}},[u,o]),(0,C.jsx)(g.sG.select,{...l,style:{...S,...l.style},ref:a,defaultValue:o})});function eM(e){return""===e||void 0===e}function ej(e){let t=(0,b.c)(e),r=n.useRef(""),o=n.useRef(0),l=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),i=n.useCallback(()=>{r.current="",window.clearTimeout(o.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),[r,l,i]}function eA(e,t,r){var n,o;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=r?e.indexOf(r):-1,a=(n=e,o=Math.max(i,0),n.map((e,t)=>n[(o+t)%n.length]));1===l.length&&(a=a.filter(e=>e!==r));let s=a.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return s!==r?s:void 0}eR.displayName="SelectBubbleInput";var eP=B,eN=G,eT=K,eD=U,eL=V,eO=$,eI=eo,e_=es,eF=ec,ez=em,eB=ev,eW=ey,eG=ew,eH=eE,eK=eS},3052:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3227:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},3509:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},3655:(e,t,r)=>{r.d(t,{hO:()=>s,sG:()=>a});var n=r(2115),o=r(7650),l=r(9708),i=r(5155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,l.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?r:t,{...l,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},3795:(e,t,r)=>{r.d(t,{A:()=>V});var n,o,l=function(){return(l=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,r(2115)),s="right-scroll-bar-position",u="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,f=new WeakMap;function p(e){return e}var m=function(e){void 0===e&&(e={});var t,r,n,o,i=(t=null,void 0===r&&(r=p),n=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,o);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){o=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var l=function(){var r=t;t=[],r.forEach(e)},i=function(){return Promise.resolve().then(l)};i(),n={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),n}}}});return i.options=l({async:!0,ssr:!1},e),i}(),h=function(){},v=a.forwardRef(function(e,t){var r,n,o,s,u=a.useRef(null),p=a.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),v=p[0],g=p[1],y=e.forwardProps,b=e.children,w=e.className,x=e.removeScrollBar,E=e.enabled,C=e.shards,S=e.sideCar,k=e.noRelative,R=e.noIsolation,M=e.inert,j=e.allowPinchZoom,A=e.as,P=e.gapMode,N=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),T=(r=[u,t],n=function(e){return r.forEach(function(t){return c(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,s=o.facade,d(function(){var e=f.get(s);if(e){var t=new Set(e),n=new Set(r),o=s.current;t.forEach(function(e){n.has(e)||c(e,null)}),n.forEach(function(e){t.has(e)||c(e,o)})}f.set(s,r)},[r]),s),D=l(l({},N),v);return a.createElement(a.Fragment,null,E&&a.createElement(S,{sideCar:m,removeScrollBar:x,shards:C,noRelative:k,noIsolation:R,inert:M,setCallbacks:g,allowPinchZoom:!!j,lockRef:u,gapMode:P}),y?a.cloneElement(a.Children.only(b),l(l({},D),{ref:T})):a.createElement(void 0===A?"div":A,l({},D,{className:w,ref:T}),b))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:u,zeroRight:s};var g=function(e){var t=e.sideCar,r=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return a.createElement(n,l({},r))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||r.nc;return t&&e.setAttribute("nonce",t),e}())){var l,i;(l=t).styleSheet?l.styleSheet.cssText=n:l.appendChild(document.createTextNode(n)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=y();return function(t,r){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},w=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},C=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(r),E(n),E(o)]},S=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=C(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},k=w(),R="data-scroll-locked",M=function(e,t,r,n){var o=e.left,l=e.top,i=e.right,a=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(a,"px ").concat(n,";\n  }\n  body[").concat(R,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(l,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(a,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(s," {\n    right: ").concat(a,"px ").concat(n,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(a,"px ").concat(n,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(R,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},j=function(){var e=parseInt(document.body.getAttribute(R)||"0",10);return isFinite(e)?e:0},A=function(){a.useEffect(function(){return document.body.setAttribute(R,(j()+1).toString()),function(){var e=j()-1;e<=0?document.body.removeAttribute(R):document.body.setAttribute(R,e.toString())}},[])},P=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;A();var l=a.useMemo(function(){return S(o)},[o]);return a.createElement(k,{styles:M(l,!t,o,r?"":"!important")})},N=!1;if("undefined"!=typeof window)try{var T=Object.defineProperty({},"passive",{get:function(){return N=!0,!0}});window.addEventListener("test",T,T),window.removeEventListener("test",T,T)}catch(e){N=!1}var D=!!N&&{passive:!1},L=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},O=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),I(e,n)){var o=_(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},I=function(e,t){return"v"===e?L(t,"overflowY"):L(t,"overflowX")},_=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,r,n,o){var l,i=(l=window.getComputedStyle(t).direction,"h"===e&&"rtl"===l?-1:1),a=i*n,s=r.target,u=t.contains(s),c=!1,d=a>0,f=0,p=0;do{if(!s)break;var m=_(e,s),h=m[0],v=m[1]-m[2]-i*h;(h||v)&&I(e,s)&&(f+=v,p+=h);var g=s.parentNode;s=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!u&&s!==document.body||u&&(t.contains(s)||t===s));return d&&(o&&1>Math.abs(f)||!o&&a>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(c=!0),c},z=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},W=function(e){return e&&"current"in e?e.current:e},G=0,H=[];let K=(n=function(e){var t=a.useRef([]),r=a.useRef([0,0]),n=a.useRef(),o=a.useState(G++)[0],l=a.useState(w)[0],i=a.useRef(e);a.useEffect(function(){i.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,o=0,l=t.length;o<l;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(W),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,l=z(e),a=r.current,s="deltaX"in e?e.deltaX:a[0]-l[0],u="deltaY"in e?e.deltaY:a[1]-l[1],c=e.target,d=Math.abs(s)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=O(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=O(d,c)),!f)return!1;if(!n.current&&"changedTouches"in e&&(s||u)&&(n.current=o),!o)return!0;var p=n.current||o;return F(p,t,e,"h"===p?s:u,!0)},[]),u=a.useCallback(function(e){if(H.length&&H[H.length-1]===l){var r="deltaY"in e?B(e):z(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(i.current.shards||[]).map(W).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=a.useCallback(function(e,r,n,o){var l={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(l),setTimeout(function(){t.current=t.current.filter(function(e){return e!==l})},1)},[]),d=a.useCallback(function(e){r.current=z(e),n.current=void 0},[]),f=a.useCallback(function(t){c(t.type,B(t),t.target,s(t,e.lockRef.current))},[]),p=a.useCallback(function(t){c(t.type,z(t),t.target,s(t,e.lockRef.current))},[]);a.useEffect(function(){return H.push(l),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",u,D),document.addEventListener("touchmove",u,D),document.addEventListener("touchstart",d,D),function(){H=H.filter(function(e){return e!==l}),document.removeEventListener("wheel",u,D),document.removeEventListener("touchmove",u,D),document.removeEventListener("touchstart",d,D)}},[]);var m=e.removeScrollBar,h=e.inert;return a.createElement(a.Fragment,null,h?a.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?a.createElement(P,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},m.useMedium(n),g);var U=a.forwardRef(function(e,t){return a.createElement(v,l({},e,{ref:t,sideCar:K}))});U.classNames=v.classNames;let V=U},4073:(e,t,r)=>{r.d(t,{CC:()=>H,Q6:()=>K,bL:()=>G,zi:()=>U});var n=r(2115),o=r(9367),l=r(5185),i=r(6101),a=r(6081),s=r(5845),u=r(4315),c=r(5503),d=r(1275),f=r(3655),p=r(7328),m=r(5155),h=["PageUp","PageDown"],v=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],g={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},y="Slider",[b,w,x]=(0,p.N)(y),[E,C]=(0,a.A)(y,[x]),[S,k]=E(y),R=n.forwardRef((e,t)=>{let{name:r,min:i=0,max:a=100,step:u=1,orientation:c="horizontal",disabled:d=!1,minStepsBetweenThumbs:f=0,defaultValue:p=[i],value:g,onValueChange:y=()=>{},onValueCommit:w=()=>{},inverted:x=!1,form:E,...C}=e,k=n.useRef(new Set),R=n.useRef(0),M="horizontal"===c,[j=[],N]=(0,s.i)({prop:g,defaultProp:p,onChange:e=>{var t;null==(t=[...k.current][R.current])||t.focus(),y(e)}}),T=n.useRef(j);function D(e,t){let{commit:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{commit:!1};let n=(String(u).split(".")[1]||"").length,l=function(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}(Math.round((e-i)/u)*u+i,n),s=(0,o.q)(l,[i,a]);N(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,r=arguments.length>2?arguments[2]:void 0,n=[...e];return n[r]=t,n.sort((e,t)=>e-t)}(e,s,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,r)=>e[r+1]-t))>=t;return!0}(n,f*u))return e;{R.current=n.indexOf(s);let t=String(n)!==String(e);return t&&r&&w(n),t?n:e}})}return(0,m.jsx)(S,{scope:e.__scopeSlider,name:r,disabled:d,min:i,max:a,valueIndexToChangeRef:R,thumbs:k.current,values:j,orientation:c,form:E,children:(0,m.jsx)(b.Provider,{scope:e.__scopeSlider,children:(0,m.jsx)(b.Slot,{scope:e.__scopeSlider,children:(0,m.jsx)(M?A:P,{"aria-disabled":d,"data-disabled":d?"":void 0,...C,ref:t,onPointerDown:(0,l.m)(C.onPointerDown,()=>{d||(T.current=j)}),min:i,max:a,inverted:x,onSlideStart:d?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let r=e.map(e=>Math.abs(e-t)),n=Math.min(...r);return r.indexOf(n)}(j,e);D(e,t)},onSlideMove:d?void 0:function(e){D(e,R.current)},onSlideEnd:d?void 0:function(){let e=T.current[R.current];j[R.current]!==e&&w(j)},onHomeKeyDown:()=>!d&&D(i,0,{commit:!0}),onEndKeyDown:()=>!d&&D(a,j.length-1,{commit:!0}),onStepKeyDown:e=>{let{event:t,direction:r}=e;if(!d){let e=h.includes(t.key)||t.shiftKey&&v.includes(t.key),n=R.current;D(j[n]+u*(e?10:1)*r,n,{commit:!0})}}})})})})});R.displayName=y;var[M,j]=E(y,{startEdge:"left",endEdge:"right",size:"width",direction:1}),A=n.forwardRef((e,t)=>{let{min:r,max:o,dir:l,inverted:a,onSlideStart:s,onSlideMove:c,onSlideEnd:d,onStepKeyDown:f,...p}=e,[h,v]=n.useState(null),y=(0,i.s)(t,e=>v(e)),b=n.useRef(void 0),w=(0,u.jH)(l),x="ltr"===w,E=x&&!a||!x&&a;function C(e){let t=b.current||h.getBoundingClientRect(),n=W([0,t.width],E?[r,o]:[o,r]);return b.current=t,n(e-t.left)}return(0,m.jsx)(M,{scope:e.__scopeSlider,startEdge:E?"left":"right",endEdge:E?"right":"left",direction:E?1:-1,size:"width",children:(0,m.jsx)(N,{dir:w,"data-orientation":"horizontal",...p,ref:y,style:{...p.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=C(e.clientX);null==s||s(t)},onSlideMove:e=>{let t=C(e.clientX);null==c||c(t)},onSlideEnd:()=>{b.current=void 0,null==d||d()},onStepKeyDown:e=>{let t=g[E?"from-left":"from-right"].includes(e.key);null==f||f({event:e,direction:t?-1:1})}})})}),P=n.forwardRef((e,t)=>{let{min:r,max:o,inverted:l,onSlideStart:a,onSlideMove:s,onSlideEnd:u,onStepKeyDown:c,...d}=e,f=n.useRef(null),p=(0,i.s)(t,f),h=n.useRef(void 0),v=!l;function y(e){let t=h.current||f.current.getBoundingClientRect(),n=W([0,t.height],v?[o,r]:[r,o]);return h.current=t,n(e-t.top)}return(0,m.jsx)(M,{scope:e.__scopeSlider,startEdge:v?"bottom":"top",endEdge:v?"top":"bottom",size:"height",direction:v?1:-1,children:(0,m.jsx)(N,{"data-orientation":"vertical",...d,ref:p,style:{...d.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=y(e.clientY);null==a||a(t)},onSlideMove:e=>{let t=y(e.clientY);null==s||s(t)},onSlideEnd:()=>{h.current=void 0,null==u||u()},onStepKeyDown:e=>{let t=g[v?"from-bottom":"from-top"].includes(e.key);null==c||c({event:e,direction:t?-1:1})}})})}),N=n.forwardRef((e,t)=>{let{__scopeSlider:r,onSlideStart:n,onSlideMove:o,onSlideEnd:i,onHomeKeyDown:a,onEndKeyDown:s,onStepKeyDown:u,...c}=e,d=k(y,r);return(0,m.jsx)(f.sG.span,{...c,ref:t,onKeyDown:(0,l.m)(e.onKeyDown,e=>{"Home"===e.key?(a(e),e.preventDefault()):"End"===e.key?(s(e),e.preventDefault()):h.concat(v).includes(e.key)&&(u(e),e.preventDefault())}),onPointerDown:(0,l.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),d.thumbs.has(t)?t.focus():n(e)}),onPointerMove:(0,l.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&o(e)}),onPointerUp:(0,l.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),i(e))})})}),T="SliderTrack",D=n.forwardRef((e,t)=>{let{__scopeSlider:r,...n}=e,o=k(T,r);return(0,m.jsx)(f.sG.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...n,ref:t})});D.displayName=T;var L="SliderRange",O=n.forwardRef((e,t)=>{let{__scopeSlider:r,...o}=e,l=k(L,r),a=j(L,r),s=n.useRef(null),u=(0,i.s)(t,s),c=l.values.length,d=l.values.map(e=>B(e,l.min,l.max)),p=c>1?Math.min(...d):0,h=100-Math.max(...d);return(0,m.jsx)(f.sG.span,{"data-orientation":l.orientation,"data-disabled":l.disabled?"":void 0,...o,ref:u,style:{...e.style,[a.startEdge]:p+"%",[a.endEdge]:h+"%"}})});O.displayName=L;var I="SliderThumb",_=n.forwardRef((e,t)=>{let r=w(e.__scopeSlider),[o,l]=n.useState(null),a=(0,i.s)(t,e=>l(e)),s=n.useMemo(()=>o?r().findIndex(e=>e.ref.current===o):-1,[r,o]);return(0,m.jsx)(F,{...e,ref:a,index:s})}),F=n.forwardRef((e,t)=>{let{__scopeSlider:r,index:o,name:a,...s}=e,u=k(I,r),c=j(I,r),[p,h]=n.useState(null),v=(0,i.s)(t,e=>h(e)),g=!p||u.form||!!p.closest("form"),y=(0,d.X)(p),w=u.values[o],x=void 0===w?0:B(w,u.min,u.max),E=function(e,t){return t>2?"Value ".concat(e+1," of ").concat(t):2===t?["Minimum","Maximum"][e]:void 0}(o,u.values.length),C=null==y?void 0:y[c.size],S=C?function(e,t,r){let n=e/2,o=W([0,50],[0,n]);return(n-o(t)*r)*r}(C,x,c.direction):0;return n.useEffect(()=>{if(p)return u.thumbs.add(p),()=>{u.thumbs.delete(p)}},[p,u.thumbs]),(0,m.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[c.startEdge]:"calc(".concat(x,"% + ").concat(S,"px)")},children:[(0,m.jsx)(b.ItemSlot,{scope:e.__scopeSlider,children:(0,m.jsx)(f.sG.span,{role:"slider","aria-label":e["aria-label"]||E,"aria-valuemin":u.min,"aria-valuenow":w,"aria-valuemax":u.max,"aria-orientation":u.orientation,"data-orientation":u.orientation,"data-disabled":u.disabled?"":void 0,tabIndex:u.disabled?void 0:0,...s,ref:v,style:void 0===w?{display:"none"}:e.style,onFocus:(0,l.m)(e.onFocus,()=>{u.valueIndexToChangeRef.current=o})})}),g&&(0,m.jsx)(z,{name:null!=a?a:u.name?u.name+(u.values.length>1?"[]":""):void 0,form:u.form,value:w},o)]})});_.displayName=I;var z=n.forwardRef((e,t)=>{let{__scopeSlider:r,value:o,...l}=e,a=n.useRef(null),s=(0,i.s)(a,t),u=(0,c.Z)(o);return n.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(u!==o&&t){let r=new Event("input",{bubbles:!0});t.call(e,o),e.dispatchEvent(r)}},[u,o]),(0,m.jsx)(f.sG.input,{style:{display:"none"},...l,ref:s,defaultValue:o})});function B(e,t,r){return(0,o.q)(100/(r-t)*(e-t),[0,100])}function W(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}z.displayName="RadioBubbleInput";var G=R,H=D,K=O,U=_},4315:(e,t,r)=>{r.d(t,{jH:()=>l});var n=r(2115);r(5155);var o=n.createContext(void 0);function l(e){let t=n.useContext(o);return e||t||"ltr"}},4378:(e,t,r)=>{r.d(t,{Z:()=>s});var n=r(2115),o=r(7650),l=r(3655),i=r(2712),a=r(5155),s=n.forwardRef((e,t)=>{var r,s;let{container:u,...c}=e,[d,f]=n.useState(!1);(0,i.N)(()=>f(!0),[]);let p=u||d&&(null==(s=globalThis)||null==(r=s.document)?void 0:r.body);return p?o.createPortal((0,a.jsx)(l.sG.div,{...c,ref:t}),p):null});s.displayName="Portal"},4516:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4783:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},4884:(e,t,r)=>{r.d(t,{bL:()=>E,zi:()=>C});var n=r(2115),o=r(5185),l=r(6101),i=r(6081),a=r(5845),s=r(5503),u=r(1275),c=r(3655),d=r(5155),f="Switch",[p,m]=(0,i.A)(f),[h,v]=p(f),g=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:i,checked:s,defaultChecked:u,required:p,disabled:m,value:v="on",onCheckedChange:g,form:y,...b}=e,[E,C]=n.useState(null),S=(0,l.s)(t,e=>C(e)),k=n.useRef(!1),R=!E||y||!!E.closest("form"),[M,j]=(0,a.i)({prop:s,defaultProp:null!=u&&u,onChange:g,caller:f});return(0,d.jsxs)(h,{scope:r,checked:M,disabled:m,children:[(0,d.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":M,"aria-required":p,"data-state":x(M),"data-disabled":m?"":void 0,disabled:m,value:v,...b,ref:S,onClick:(0,o.m)(e.onClick,e=>{j(e=>!e),R&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),R&&(0,d.jsx)(w,{control:E,bubbles:!k.current,name:i,value:v,checked:M,required:p,disabled:m,form:y,style:{transform:"translateX(-100%)"}})]})});g.displayName=f;var y="SwitchThumb",b=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=v(y,r);return(0,d.jsx)(c.sG.span,{"data-state":x(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});b.displayName=y;var w=n.forwardRef((e,t)=>{let{__scopeSwitch:r,control:o,checked:i,bubbles:a=!0,...c}=e,f=n.useRef(null),p=(0,l.s)(f,t),m=(0,s.Z)(i),h=(0,u.X)(o);return n.useEffect(()=>{let e=f.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==i&&t){let r=new Event("click",{bubbles:a});t.call(e,i),e.dispatchEvent(r)}},[m,i,a]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i,...c,tabIndex:-1,ref:p,style:{...c.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}w.displayName="SwitchBubbleInput";var E=g,C=b},5152:(e,t,r)=>{r.d(t,{Mz:()=>eY,i3:()=>eJ,UC:()=>eZ,bL:()=>eX,Bk:()=>eD});var n=r(2115);let o=["top","right","bottom","left"],l=Math.min,i=Math.max,a=Math.round,s=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>d[e])}function b(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function w(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function E(e,t,r){let n,{reference:o,floating:l}=e,i=g(t),a=h(g(t)),s=v(a),u=p(t),c="y"===i,d=o.x+o.width/2-l.width/2,f=o.y+o.height/2-l.height/2,y=o[s]/2-l[s]/2;switch(u){case"top":n={x:d,y:o.y-l.height};break;case"bottom":n={x:d,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-l.width,y:f};break;default:n={x:o.x,y:o.y}}switch(m(t)){case"start":n[a]-=y*(r&&c?-1:1);break;case"end":n[a]+=y*(r&&c?-1:1)}return n}let C=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:l=[],platform:i}=r,a=l.filter(Boolean),s=await (null==i.isRTL?void 0:i.isRTL(t)),u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=E(u,n,s),f=n,p={},m=0;for(let r=0;r<a.length;r++){let{name:l,fn:h}=a[r],{x:v,y:g,data:y,reset:b}=await h({x:c,y:d,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:u,platform:i,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[l]:{...p[l],...y}},b&&m<=50&&(m++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(u=!0===b.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:c,y:d}=E(u,f,s)),r=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function S(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:l,rects:i,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:m=0}=f(t,e),h=w(m),v=a[p?"floating"===d?"reference":"floating":d],g=x(await l.getClippingRect({element:null==(r=await (null==l.isElement?void 0:l.isElement(v)))||r?v:v.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),y="floating"===d?{x:n,y:o,width:i.floating.width,height:i.floating.height}:i.reference,b=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),E=await (null==l.isElement?void 0:l.isElement(b))&&await (null==l.getScale?void 0:l.getScale(b))||{x:1,y:1},C=x(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:b,strategy:s}):y);return{top:(g.top-C.top+h.top)/E.y,bottom:(C.bottom-g.bottom+h.bottom)/E.y,left:(g.left-C.left+h.left)/E.x,right:(C.right-g.right+h.right)/E.x}}function k(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function R(e){return o.some(t=>e[t]>=0)}async function M(e,t){let{placement:r,platform:n,elements:o}=e,l=await (null==n.isRTL?void 0:n.isRTL(o.floating)),i=p(r),a=m(r),s="y"===g(r),u=["left","top"].includes(i)?-1:1,c=l&&s?-1:1,d=f(t,e),{mainAxis:h,crossAxis:v,alignmentAxis:y}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof y&&(v="end"===a?-1*y:y),s?{x:v*c,y:h*u}:{x:h*u,y:v*c}}function j(){return"undefined"!=typeof window}function A(e){return T(e)?(e.nodeName||"").toLowerCase():"#document"}function P(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function N(e){var t;return null==(t=(T(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function T(e){return!!j()&&(e instanceof Node||e instanceof P(e).Node)}function D(e){return!!j()&&(e instanceof Element||e instanceof P(e).Element)}function L(e){return!!j()&&(e instanceof HTMLElement||e instanceof P(e).HTMLElement)}function O(e){return!!j()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof P(e).ShadowRoot)}function I(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=W(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(o)}function _(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function F(e){let t=z(),r=D(e)?W(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function B(e){return["html","body","#document"].includes(A(e))}function W(e){return P(e).getComputedStyle(e)}function G(e){return D(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function H(e){if("html"===A(e))return e;let t=e.assignedSlot||e.parentNode||O(e)&&e.host||N(e);return O(t)?t.host:t}function K(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=H(t);return B(r)?t.ownerDocument?t.ownerDocument.body:t.body:L(r)&&I(r)?r:e(r)}(e),l=o===(null==(n=e.ownerDocument)?void 0:n.body),i=P(o);if(l){let e=U(i);return t.concat(i,i.visualViewport||[],I(o)?o:[],e&&r?K(e):[])}return t.concat(o,K(o,[],r))}function U(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function V(e){let t=W(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=L(e),l=o?e.offsetWidth:r,i=o?e.offsetHeight:n,s=a(r)!==l||a(n)!==i;return s&&(r=l,n=i),{width:r,height:n,$:s}}function q(e){return D(e)?e:e.contextElement}function $(e){let t=q(e);if(!L(t))return u(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:l}=V(t),i=(l?a(r.width):r.width)/n,s=(l?a(r.height):r.height)/o;return i&&Number.isFinite(i)||(i=1),s&&Number.isFinite(s)||(s=1),{x:i,y:s}}let X=u(0);function Y(e){let t=P(e);return z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:X}function Z(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let l=e.getBoundingClientRect(),i=q(e),a=u(1);t&&(n?D(n)&&(a=$(n)):a=$(e));let s=(void 0===(o=r)&&(o=!1),n&&(!o||n===P(i))&&o)?Y(i):u(0),c=(l.left+s.x)/a.x,d=(l.top+s.y)/a.y,f=l.width/a.x,p=l.height/a.y;if(i){let e=P(i),t=n&&D(n)?P(n):n,r=e,o=U(r);for(;o&&n&&t!==r;){let e=$(o),t=o.getBoundingClientRect(),n=W(o),l=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=l,d+=i,o=U(r=P(o))}}return x({width:f,height:p,x:c,y:d})}function J(e,t){let r=G(e).scrollLeft;return t?t.left+r:Z(N(e)).left+r}function Q(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:J(e,n)),y:n.top+t.scrollTop}}function ee(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=P(e),n=N(e),o=r.visualViewport,l=n.clientWidth,i=n.clientHeight,a=0,s=0;if(o){l=o.width,i=o.height;let e=z();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:l,height:i,x:a,y:s}}(e,r);else if("document"===t)n=function(e){let t=N(e),r=G(e),n=e.ownerDocument.body,o=i(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),l=i(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),a=-r.scrollLeft+J(e),s=-r.scrollTop;return"rtl"===W(n).direction&&(a+=i(t.clientWidth,n.clientWidth)-o),{width:o,height:l,x:a,y:s}}(N(e));else if(D(t))n=function(e,t){let r=Z(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,l=L(e)?$(e):u(1),i=e.clientWidth*l.x,a=e.clientHeight*l.y;return{width:i,height:a,x:o*l.x,y:n*l.y}}(t,r);else{let r=Y(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return x(n)}function et(e){return"static"===W(e).position}function er(e,t){if(!L(e)||"fixed"===W(e).position)return null;if(t)return t(e);let r=e.offsetParent;return N(e)===r&&(r=r.ownerDocument.body),r}function en(e,t){let r=P(e);if(_(e))return r;if(!L(e)){let t=H(e);for(;t&&!B(t);){if(D(t)&&!et(t))return t;t=H(t)}return r}let n=er(e,t);for(;n&&["table","td","th"].includes(A(n))&&et(n);)n=er(n,t);return n&&B(n)&&et(n)&&!F(n)?r:n||function(e){let t=H(e);for(;L(t)&&!B(t);){if(F(t))return t;if(_(t))break;t=H(t)}return null}(e)||r}let eo=async function(e){let t=this.getOffsetParent||en,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=L(t),o=N(t),l="fixed"===r,i=Z(e,!0,l,t),a={scrollLeft:0,scrollTop:0},s=u(0);if(n||!n&&!l)if(("body"!==A(t)||I(o))&&(a=G(t)),n){let e=Z(t,!0,l,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=J(o));l&&!n&&o&&(s.x=J(o));let c=!o||n||l?u(0):Q(o,a);return{x:i.left+a.scrollLeft-s.x-c.x,y:i.top+a.scrollTop-s.y-c.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},el={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,l="fixed"===o,i=N(n),a=!!t&&_(t.floating);if(n===i||a&&l)return r;let s={scrollLeft:0,scrollTop:0},c=u(1),d=u(0),f=L(n);if((f||!f&&!l)&&(("body"!==A(n)||I(i))&&(s=G(n)),L(n))){let e=Z(n);c=$(n),d.x=e.x+n.clientLeft,d.y=e.y+n.clientTop}let p=!i||f||l?u(0):Q(i,s,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-s.scrollLeft*c.x+d.x+p.x,y:r.y*c.y-s.scrollTop*c.y+d.y+p.y}},getDocumentElement:N,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,a=[..."clippingAncestors"===r?_(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=K(e,[],!1).filter(e=>D(e)&&"body"!==A(e)),o=null,l="fixed"===W(e).position,i=l?H(e):e;for(;D(i)&&!B(i);){let t=W(i),r=F(i);r||"fixed"!==t.position||(o=null),(l?!r&&!o:!r&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||I(i)&&!r&&function e(t,r){let n=H(t);return!(n===r||!D(n)||B(n))&&("fixed"===W(n).position||e(n,r))}(e,i))?n=n.filter(e=>e!==i):o=t,i=H(i)}return t.set(e,n),n}(t,this._c):[].concat(r),n],s=a[0],u=a.reduce((e,r)=>{let n=ee(t,r,o);return e.top=i(n.top,e.top),e.right=l(n.right,e.right),e.bottom=l(n.bottom,e.bottom),e.left=i(n.left,e.left),e},ee(t,s,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:en,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=V(e);return{width:t,height:r}},getScale:$,isElement:D,isRTL:function(e){return"rtl"===W(e).direction}};function ei(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:a,platform:s,elements:u,middlewareData:c}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let y=w(p),b={x:r,y:n},x=h(g(o)),E=v(x),C=await s.getDimensions(d),S="y"===x,k=S?"clientHeight":"clientWidth",R=a.reference[E]+a.reference[x]-b[x]-a.floating[E],M=b[x]-a.reference[x],j=await (null==s.getOffsetParent?void 0:s.getOffsetParent(d)),A=j?j[k]:0;A&&await (null==s.isElement?void 0:s.isElement(j))||(A=u.floating[k]||a.floating[E]);let P=A/2-C[E]/2-1,N=l(y[S?"top":"left"],P),T=l(y[S?"bottom":"right"],P),D=A-C[E]-T,L=A/2-C[E]/2+(R/2-M/2),O=i(N,l(L,D)),I=!c.arrow&&null!=m(o)&&L!==O&&a.reference[E]/2-(L<N?N:T)-C[E]/2<0,_=I?L<N?L-N:L-D:0;return{[x]:b[x]+_,data:{[x]:O,centerOffset:L-O-_,...I&&{alignmentOffset:_}},reset:I}}}),es=(e,t,r)=>{let n=new Map,o={platform:el,...r},l={...o.platform,_c:n};return C(e,t,{...o,platform:l})};var eu=r(7650),ec="undefined"!=typeof document?n.useLayoutEffect:function(){};function ed(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!ed(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!ed(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let r=ef(e);return Math.round(t*r)/r}function em(e){let t=n.useRef(e);return ec(()=>{t.current=e}),t}let eh=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?ea({element:r.current,padding:n}).fn(t):{}:r?ea({element:r,padding:n}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:l,placement:i,middlewareData:a}=t,s=await M(t,e);return i===(null==(r=a.offset)?void 0:r.placement)&&null!=(n=a.arrow)&&n.alignmentOffset?{}:{x:o+s.x,y:l+s.y,data:{...s,placement:i}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:a=!0,crossAxis:s=!1,limiter:u={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...c}=f(e,t),d={x:r,y:n},m=await S(t,c),v=g(p(o)),y=h(v),b=d[y],w=d[v];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",r=b+m[e],n=b-m[t];b=i(r,l(b,n))}if(s){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",r=w+m[e],n=w-m[t];w=i(r,l(w,n))}let x=u.fn({...t,[y]:b,[v]:w});return{...x,data:{x:x.x-r,y:x.y-n,enabled:{[y]:a,[v]:s}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:l,middlewareData:i}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=f(e,t),c={x:r,y:n},d=g(o),m=h(d),v=c[m],y=c[d],b=f(a,t),w="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(s){let e="y"===m?"height":"width",t=l.reference[m]-l.floating[e]+w.mainAxis,r=l.reference[m]+l.reference[e]-w.mainAxis;v<t?v=t:v>r&&(v=r)}if(u){var x,E;let e="y"===m?"width":"height",t=["top","left"].includes(p(o)),r=l.reference[d]-l.floating[e]+(t&&(null==(x=i.offset)?void 0:x[d])||0)+(t?0:w.crossAxis),n=l.reference[d]+l.reference[e]+(t?0:(null==(E=i.offset)?void 0:E[d])||0)-(t?w.crossAxis:0);y<r?y=r:y>n&&(y=n)}return{[m]:v,[d]:y}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,l,i;let{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:d,elements:w}=t,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:C,fallbackStrategy:k="bestFit",fallbackAxisSideDirection:R="none",flipAlignment:M=!0,...j}=f(e,t);if(null!=(r=s.arrow)&&r.alignmentOffset)return{};let A=p(a),P=g(c),N=p(c)===c,T=await (null==d.isRTL?void 0:d.isRTL(w.floating)),D=C||(N||!M?[b(c)]:function(e){let t=b(e);return[y(e),t,y(t)]}(c)),L="none"!==R;!C&&L&&D.push(...function(e,t,r,n){let o=m(e),l=function(e,t,r){let n=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(r)return t?o:n;return t?n:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===r,n);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(y)))),l}(c,M,R,T));let O=[c,...D],I=await S(t,j),_=[],F=(null==(n=s.flip)?void 0:n.overflows)||[];if(x&&_.push(I[A]),E){let e=function(e,t,r){void 0===r&&(r=!1);let n=m(e),o=h(g(e)),l=v(o),i="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[l]>t.floating[l]&&(i=b(i)),[i,b(i)]}(a,u,T);_.push(I[e[0]],I[e[1]])}if(F=[...F,{placement:a,overflows:_}],!_.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=O[e];if(t&&("alignment"!==E||P===g(t)||F.every(e=>e.overflows[0]>0&&g(e.placement)===P)))return{data:{index:e,overflows:F},reset:{placement:t}};let r=null==(l=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!r)switch(k){case"bestFit":{let e=null==(i=F.filter(e=>{if(L){let t=g(e.placement);return t===P||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(r=e);break}case"initialPlacement":r=c}if(a!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,a,{placement:s,rects:u,platform:c,elements:d}=t,{apply:h=()=>{},...v}=f(e,t),y=await S(t,v),b=p(s),w=m(s),x="y"===g(s),{width:E,height:C}=u.floating;"top"===b||"bottom"===b?(o=b,a=w===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(a=b,o="end"===w?"top":"bottom");let k=C-y.top-y.bottom,R=E-y.left-y.right,M=l(C-y[o],k),j=l(E-y[a],R),A=!t.middlewareData.shift,P=M,N=j;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(N=R),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(P=k),A&&!w){let e=i(y.left,0),t=i(y.right,0),r=i(y.top,0),n=i(y.bottom,0);x?N=E-2*(0!==e||0!==t?e+t:i(y.left,y.right)):P=C-2*(0!==r||0!==n?r+n:i(y.top,y.bottom))}await h({...t,availableWidth:N,availableHeight:P});let T=await c.getDimensions(d.floating);return E!==T.width||C!==T.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=f(e,t);switch(n){case"referenceHidden":{let e=k(await S(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:R(e)}}}case"escaped":{let e=k(await S(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:R(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...eh(e),options:[e,t]});var eC=r(3655),eS=r(5155),ek=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...l}=e;return(0,eS.jsx)(eC.sG.svg,{...l,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,eS.jsx)("polygon",{points:"0,0 30,0 15,10"})})});ek.displayName="Arrow";var eR=r(6101),eM=r(6081),ej=r(9033),eA=r(2712),eP=r(1275),eN="Popper",[eT,eD]=(0,eM.A)(eN),[eL,eO]=eT(eN),eI=e=>{let{__scopePopper:t,children:r}=e,[o,l]=n.useState(null);return(0,eS.jsx)(eL,{scope:t,anchor:o,onAnchorChange:l,children:r})};eI.displayName=eN;var e_="PopperAnchor",eF=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...l}=e,i=eO(e_,r),a=n.useRef(null),s=(0,eR.s)(t,a);return n.useEffect(()=>{i.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,eS.jsx)(eC.sG.div,{...l,ref:s})});eF.displayName=e_;var ez="PopperContent",[eB,eW]=eT(ez),eG=n.forwardRef((e,t)=>{var r,o,a,u,c,d,f,p;let{__scopePopper:m,side:h="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:b=0,avoidCollisions:w=!0,collisionBoundary:x=[],collisionPadding:E=0,sticky:C="partial",hideWhenDetached:S=!1,updatePositionStrategy:k="optimized",onPlaced:R,...M}=e,j=eO(ez,m),[A,P]=n.useState(null),T=(0,eR.s)(t,e=>P(e)),[D,L]=n.useState(null),O=(0,eP.X)(D),I=null!=(f=null==O?void 0:O.width)?f:0,_=null!=(p=null==O?void 0:O.height)?p:0,F="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},z=Array.isArray(x)?x:[x],B=z.length>0,W={padding:F,boundary:z.filter(eV),altBoundary:B},{refs:G,floatingStyles:H,placement:U,isPositioned:V,middlewareData:$}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:o=[],platform:l,elements:{reference:i,floating:a}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[d,f]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=n.useState(o);ed(p,o)||m(o);let[h,v]=n.useState(null),[g,y]=n.useState(null),b=n.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),w=n.useCallback(e=>{e!==S.current&&(S.current=e,y(e))},[]),x=i||h,E=a||g,C=n.useRef(null),S=n.useRef(null),k=n.useRef(d),R=null!=u,M=em(u),j=em(l),A=em(c),P=n.useCallback(()=>{if(!C.current||!S.current)return;let e={placement:t,strategy:r,middleware:p};j.current&&(e.platform=j.current),es(C.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==A.current};N.current&&!ed(k.current,t)&&(k.current=t,eu.flushSync(()=>{f(t)}))})},[p,t,r,j,A]);ec(()=>{!1===c&&k.current.isPositioned&&(k.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let N=n.useRef(!1);ec(()=>(N.current=!0,()=>{N.current=!1}),[]),ec(()=>{if(x&&(C.current=x),E&&(S.current=E),x&&E){if(M.current)return M.current(x,E,P);P()}},[x,E,P,M,R]);let T=n.useMemo(()=>({reference:C,floating:S,setReference:b,setFloating:w}),[b,w]),D=n.useMemo(()=>({reference:x,floating:E}),[x,E]),L=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!D.floating)return e;let t=ep(D.floating,d.x),n=ep(D.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+n+"px)",...ef(D.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,s,D.floating,d.x,d.y]);return n.useMemo(()=>({...d,update:P,refs:T,elements:D,floatingStyles:L}),[d,P,T,D,L])}({strategy:"fixed",placement:h+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=n,p=q(e),m=a||u?[...p?K(p):[],...K(t)]:[];m.forEach(e=>{a&&e.addEventListener("scroll",r,{passive:!0}),u&&e.addEventListener("resize",r)});let h=p&&d?function(e,t){let r,n=null,o=N(e);function a(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),a();let f=e.getBoundingClientRect(),{left:p,top:m,width:h,height:v}=f;if(c||t(),!h||!v)return;let g=s(m),y=s(o.clientWidth-(p+h)),b={rootMargin:-g+"px "+-y+"px "+-s(o.clientHeight-(m+v))+"px "+-s(p)+"px",threshold:i(0,l(1,d))||1},w=!0;function x(t){let n=t[0].intersectionRatio;if(n!==d){if(!w)return u();n?u(!1,n):r=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==n||ei(f,e.getBoundingClientRect())||u(),w=!1}try{n=new IntersectionObserver(x,{...b,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(x,b)}n.observe(e)}(!0),a}(p,r):null,v=-1,g=null;c&&(g=new ResizeObserver(e=>{let[n]=e;n&&n.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),r()}),p&&!f&&g.observe(p),g.observe(t));let y=f?Z(e):null;return f&&function t(){let n=Z(e);y&&!ei(y,n)&&r(),y=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;m.forEach(e=>{a&&e.removeEventListener("scroll",r),u&&e.removeEventListener("resize",r)}),null==h||h(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===k})},elements:{reference:j.anchor},middleware:[ev({mainAxis:v+_,alignmentAxis:y}),w&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===C?ey():void 0,...W}),w&&eb({...W}),ew({...W,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:o}=e,{width:l,height:i}=r.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(n,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(l,"px")),a.setProperty("--radix-popper-anchor-height","".concat(i,"px"))}}),D&&eE({element:D,padding:b}),eq({arrowWidth:I,arrowHeight:_}),S&&ex({strategy:"referenceHidden",...W})]}),[X,Y]=e$(U),J=(0,ej.c)(R);(0,eA.N)(()=>{V&&(null==J||J())},[V,J]);let Q=null==(r=$.arrow)?void 0:r.x,ee=null==(o=$.arrow)?void 0:o.y,et=(null==(a=$.arrow)?void 0:a.centerOffset)!==0,[er,en]=n.useState();return(0,eA.N)(()=>{A&&en(window.getComputedStyle(A).zIndex)},[A]),(0,eS.jsx)("div",{ref:G.setFloating,"data-radix-popper-content-wrapper":"",style:{...H,transform:V?H.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:er,"--radix-popper-transform-origin":[null==(u=$.transformOrigin)?void 0:u.x,null==(c=$.transformOrigin)?void 0:c.y].join(" "),...(null==(d=$.hide)?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eS.jsx)(eB,{scope:m,placedSide:X,onArrowChange:L,arrowX:Q,arrowY:ee,shouldHideArrow:et,children:(0,eS.jsx)(eC.sG.div,{"data-side":X,"data-align":Y,...M,ref:T,style:{...M.style,animation:V?void 0:"none"}})})})});eG.displayName=ez;var eH="PopperArrow",eK={top:"bottom",right:"left",bottom:"top",left:"right"},eU=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=eW(eH,r),l=eK[o.placedSide];return(0,eS.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eS.jsx)(ek,{...n,ref:t,style:{...n.style,display:"block"}})})});function eV(e){return null!==e}eU.displayName=eH;var eq=e=>({name:"transformOrigin",options:e,fn(t){var r,n,o,l,i;let{placement:a,rects:s,middlewareData:u}=t,c=(null==(r=u.arrow)?void 0:r.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,m]=e$(a),h={start:"0%",center:"50%",end:"100%"}[m],v=(null!=(l=null==(n=u.arrow)?void 0:n.x)?l:0)+d/2,g=(null!=(i=null==(o=u.arrow)?void 0:o.y)?i:0)+f/2,y="",b="";return"bottom"===p?(y=c?h:"".concat(v,"px"),b="".concat(-f,"px")):"top"===p?(y=c?h:"".concat(v,"px"),b="".concat(s.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),b=c?h:"".concat(g,"px")):"left"===p&&(y="".concat(s.floating.width+f,"px"),b=c?h:"".concat(g,"px")),{data:{x:y,y:b}}}});function e$(e){let[t,r="center"]=e.split("-");return[t,r]}var eX=eI,eY=eF,eZ=eG,eJ=eU},5185:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},5503:(e,t,r)=>{r.d(t,{Z:()=>o});var n=r(2115);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},5845:(e,t,r)=>{r.d(t,{i:()=>a});var n,o=r(2115),l=r(2712),i=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||l.N;function a({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[l,a,s]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),l=o.useRef(r),a=o.useRef(t);return i(()=>{a.current=t},[t]),o.useEffect(()=>{l.current!==r&&(a.current?.(r),l.current=r)},[r,l]),[r,n,a]}({defaultProp:t,onChange:r}),u=void 0!==e,c=u?e:l;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,n])}return[c,o.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&s.current?.(r)}else a(t)},[u,e,a,s])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(2115),o=r(5155);function l(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return i.scopeName=e,[function(t,l){let i=n.createContext(l),a=r.length;r=[...r,l];let s=t=>{let{scope:r,children:l,...s}=t,u=r?.[e]?.[a]||i,c=n.useMemo(()=>s,Object.values(s));return(0,o.jsx)(u.Provider,{value:c,children:l})};return s.displayName=t+"Provider",[s,function(r,o){let s=o?.[e]?.[a]||i,u=n.useContext(s);if(u)return u;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(i,...t)]}},6101:(e,t,r)=>{r.d(t,{s:()=>i,t:()=>l});var n=r(2115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function i(...e){return n.useCallback(l(...e),e)}},6474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7328:(e,t,r)=>{function n(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function o(e,t){var r=n(e,t,"get");return r.get?r.get.call(e):r.value}function l(e,t,r){var o=n(e,t,"set");if(o.set)o.set.call(e,r);else{if(!o.writable)throw TypeError("attempted to set read only private field");o.value=r}return r}r.d(t,{N:()=>f});var i,a=r(2115),s=r(6081),u=r(6101),c=r(9708),d=r(5155);function f(e){let t=e+"CollectionProvider",[r,n]=(0,s.A)(t),[o,l]=r(t,{collectionRef:{current:null},itemMap:new Map}),i=e=>{let{scope:t,children:r}=e,n=a.useRef(null),l=a.useRef(new Map).current;return(0,d.jsx)(o,{scope:t,itemMap:l,collectionRef:n,children:r})};i.displayName=t;let f=e+"CollectionSlot",p=(0,c.TL)(f),m=a.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=l(f,r),i=(0,u.s)(t,o.collectionRef);return(0,d.jsx)(p,{ref:i,children:n})});m.displayName=f;let h=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,c.TL)(h),y=a.forwardRef((e,t)=>{let{scope:r,children:n,...o}=e,i=a.useRef(null),s=(0,u.s)(t,i),c=l(h,r);return a.useEffect(()=>(c.itemMap.set(i,{ref:i,...o}),()=>void c.itemMap.delete(i))),(0,d.jsx)(g,{...{[v]:""},ref:s,children:n})});return y.displayName=h,[{Provider:i,Slot:m,ItemSlot:y},function(t){let r=l(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}var p=new WeakMap;function m(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=h(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function h(e){return e!=e||0===e?0:Math.trunc(e)}i=new WeakMap},7863:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},7900:(e,t,r)=>{r.d(t,{n:()=>d});var n=r(2115),o=r(6101),l=r(3655),i=r(9033),a=r(5155),s="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[b,w]=n.useState(null),x=(0,i.c)(v),E=(0,i.c)(g),C=n.useRef(null),S=(0,o.s)(t,e=>w(e)),k=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(d){let e=function(e){if(k.paused||!b)return;let t=e.target;b.contains(t)?C.current=t:m(C.current,{select:!0})},t=function(e){if(k.paused||!b)return;let t=e.relatedTarget;null!==t&&(b.contains(t)||m(C.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(b)});return b&&r.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[d,b,k.paused]),n.useEffect(()=>{if(b){h.add(k);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(s,c);b.addEventListener(s,x),b.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(m(n,{select:t}),document.activeElement!==r)return}(f(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(b))}return()=>{b.removeEventListener(s,x),setTimeout(()=>{let t=new CustomEvent(u,c);b.addEventListener(u,E),b.dispatchEvent(t),t.defaultPrevented||m(null!=e?e:document.body,{select:!0}),b.removeEventListener(u,E),h.remove(k)},0)}}},[b,x,E,k]);let R=n.useCallback(e=>{if(!r&&!d||k.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[o,l]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&l?e.shiftKey||n!==l?e.shiftKey&&n===o&&(e.preventDefault(),r&&m(l,{select:!0})):(e.preventDefault(),r&&m(o,{select:!0})):n===t&&e.preventDefault()}},[r,d,k.paused]);return(0,a.jsx)(l.sG.div,{tabIndex:-1,...y,ref:S,onKeyDown:R})});function f(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function p(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function m(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}d.displayName="FocusScope";var h=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=v(e,t)).unshift(t)},remove(t){var r;null==(r=(e=v(e,t))[0])||r.resume()}}}();function v(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},7924:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},8168:(e,t,r)=>{r.d(t,{Eq:()=>c});var n=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,l=new WeakMap,i={},a=0,s=function(e){return e&&(e.host||s(e.parentNode))},u=function(e,t,r,n){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=s(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[r]||(i[r]=new WeakMap);var c=i[r],d=[],f=new Set,p=new Set(u),m=function(e){!e||f.has(e)||(f.add(e),m(e.parentNode))};u.forEach(m);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(n),i=null!==t&&"false"!==t,a=(o.get(e)||0)+1,s=(c.get(e)||0)+1;o.set(e,a),c.set(e,s),d.push(e),1===a&&i&&l.set(e,!0),1===s&&e.setAttribute(r,"true"),i||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),a++,function(){d.forEach(function(e){var t=o.get(e)-1,i=c.get(e)-1;o.set(e,t),c.set(e,i),t||(l.has(e)||e.removeAttribute(n),l.delete(e)),i||e.removeAttribute(r)}),--a||(o=new WeakMap,o=new WeakMap,l=new WeakMap,i={})}},c=function(e,t,r){void 0===r&&(r="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),l=t||n(e);return l?(o.push.apply(o,Array.from(l.querySelectorAll("[aria-live], script"))),u(o,l,r,"aria-hidden")):function(){return null}}},8564:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9033:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(2115);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},9178:(e,t,r)=>{r.d(t,{qW:()=>f});var n,o=r(2115),l=r(5185),i=r(3655),a=r(6101),s=r(9033),u=r(5155),c="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var r,f;let{disableOutsidePointerEvents:h=!1,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:b,onDismiss:w,...x}=e,E=o.useContext(d),[C,S]=o.useState(null),k=null!=(f=null==C?void 0:C.ownerDocument)?f:null==(r=globalThis)?void 0:r.document,[,R]=o.useState({}),M=(0,a.s)(t,e=>S(e)),j=Array.from(E.layers),[A]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),P=j.indexOf(A),N=C?j.indexOf(C):-1,T=E.layersWithOutsidePointerEventsDisabled.size>0,D=N>=P,L=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,s.c)(e),l=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!l.current){let t=function(){m("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",i.current),i.current=t,r.addEventListener("click",i.current,{once:!0})):t()}else r.removeEventListener("click",i.current);l.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",i.current)}},[r,n]),{onPointerDownCapture:()=>l.current=!0}}(e=>{let t=e.target,r=[...E.branches].some(e=>e.contains(t));D&&!r&&(null==g||g(e),null==b||b(e),e.defaultPrevented||null==w||w())},k),O=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,s.c)(e),l=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!l.current&&m("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>l.current=!0,onBlurCapture:()=>l.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==y||y(e),null==b||b(e),e.defaultPrevented||null==w||w())},k);return!function(e,t=globalThis?.document){let r=(0,s.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{N===E.layers.size-1&&(null==v||v(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},k),o.useEffect(()=>{if(C)return h&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(n=k.body.style.pointerEvents,k.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(C)),E.layers.add(C),p(),()=>{h&&1===E.layersWithOutsidePointerEventsDisabled.size&&(k.body.style.pointerEvents=n)}},[C,k,h,E]),o.useEffect(()=>()=>{C&&(E.layers.delete(C),E.layersWithOutsidePointerEventsDisabled.delete(C),p())},[C,E]),o.useEffect(()=>{let e=()=>R({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(i.sG.div,{...x,ref:M,style:{pointerEvents:T?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,l.m)(e.onFocusCapture,O.onFocusCapture),onBlurCapture:(0,l.m)(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:(0,l.m)(e.onPointerDownCapture,L.onPointerDownCapture)})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function m(e,t,r,n){let{discrete:o}=n,l=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&l.addEventListener(e,t,{once:!0}),o?(0,i.hO)(l,a):l.dispatchEvent(a)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(d),n=o.useRef(null),l=(0,a.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(i.sG.div,{...e,ref:l})}).displayName="DismissableLayerBranch"},9367:(e,t,r)=>{r.d(t,{q:()=>n});function n(e,[t,r]){return Math.min(r,Math.max(t,e))}},9428:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},9688:(e,t,r)=>{r.d(t,{QP:()=>X});let n=e=>{let t=a(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),o(r,t)||i(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),l=n?o(e.slice(1),n):void 0;if(l)return l;if(0===t.validators.length)return;let i=e.join("-");return t.validators.find(({validator:e})=>e(i))?.classGroupId},l=/^\[(.+)\]$/,i=e=>{if(l.test(e)){let t=l.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},a=e=>{let{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return d(Object.entries(e.classGroups),r).forEach(([e,r])=>{s(r,n,e,t)}),n},s=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e)return c(e)?void s(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,o])=>{s(o,u(t,e),r,n)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,d=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,l)=>{r.set(o,l),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},p=e=>{let{separator:t,experimentalParseClassName:r}=e,n=1===t.length,o=t[0],l=t.length,i=e=>{let r,i=[],a=0,s=0;for(let u=0;u<e.length;u++){let c=e[u];if(0===a){if(c===o&&(n||e.slice(u,u+l)===t)){i.push(e.slice(s,u)),s=u+l;continue}if("/"===c){r=u;continue}}"["===c?a++:"]"===c&&a--}let u=0===i.length?e:e.substring(s),c=u.startsWith("!"),d=c?u.substring(1):u;return{modifiers:i,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:r&&r>s?r-s:void 0}};return r?e=>r({className:e,parseClassName:i}):i},m=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},h=e=>({cache:f(e.cacheSize),parseClassName:p(e),...n(e)}),v=/\s+/,g=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o}=t,l=[],i=e.trim().split(v),a="";for(let e=i.length-1;e>=0;e-=1){let t=i[e],{modifiers:s,hasImportantModifier:u,baseClassName:c,maybePostfixModifierPosition:d}=r(t),f=!!d,p=n(f?c.substring(0,d):c);if(!p){if(!f||!(p=n(c))){a=t+(a.length>0?" "+a:a);continue}f=!1}let h=m(s).join(":"),v=u?h+"!":h,g=v+p;if(l.includes(g))continue;l.push(g);let y=o(p,f);for(let e=0;e<y.length;++e){let t=y[e];l.push(v+t)}a=t+(a.length>0?" "+a:a)}return a};function y(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=b(e))&&(n&&(n+=" "),n+=t);return n}let b=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=b(e[n]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:([a-z-]+):)?(.+)\]$/i,E=/^\d+\/\d+$/,C=new Set(["px","full","screen"]),S=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,k=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,R=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,M=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,j=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,A=e=>N(e)||C.has(e)||E.test(e),P=e=>K(e,"length",U),N=e=>!!e&&!Number.isNaN(Number(e)),T=e=>K(e,"number",N),D=e=>!!e&&Number.isInteger(Number(e)),L=e=>e.endsWith("%")&&N(e.slice(0,-1)),O=e=>x.test(e),I=e=>S.test(e),_=new Set(["length","size","percentage"]),F=e=>K(e,_,V),z=e=>K(e,"position",V),B=new Set(["image","url"]),W=e=>K(e,B,$),G=e=>K(e,"",q),H=()=>!0,K=(e,t,r)=>{let n=x.exec(e);return!!n&&(n[1]?"string"==typeof t?n[1]===t:t.has(n[1]):r(n[2]))},U=e=>k.test(e)&&!R.test(e),V=()=>!1,q=e=>M.test(e),$=e=>j.test(e);Symbol.toStringTag;let X=function(e,...t){let r,n,o,l=function(a){return n=(r=h(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,l=i,i(a)};function i(e){let t=n(e);if(t)return t;let l=g(e,r);return o(e,l),l}return function(){return l(y.apply(null,arguments))}}(()=>{let e=w("colors"),t=w("spacing"),r=w("blur"),n=w("brightness"),o=w("borderColor"),l=w("borderRadius"),i=w("borderSpacing"),a=w("borderWidth"),s=w("contrast"),u=w("grayscale"),c=w("hueRotate"),d=w("invert"),f=w("gap"),p=w("gradientColorStops"),m=w("gradientColorStopPositions"),h=w("inset"),v=w("margin"),g=w("opacity"),y=w("padding"),b=w("saturate"),x=w("scale"),E=w("sepia"),C=w("skew"),S=w("space"),k=w("translate"),R=()=>["auto","contain","none"],M=()=>["auto","hidden","clip","visible","scroll"],j=()=>["auto",O,t],_=()=>[O,t],B=()=>["",A,P],K=()=>["auto",N,O],U=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],V=()=>["solid","dashed","dotted","double","none"],q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],$=()=>["start","end","center","between","around","evenly","stretch"],X=()=>["","0",O],Y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Z=()=>[N,O];return{cacheSize:500,separator:":",theme:{colors:[H],spacing:[A,P],blur:["none","",I,O],brightness:Z(),borderColor:[e],borderRadius:["none","","full",I,O],borderSpacing:_(),borderWidth:B(),contrast:Z(),grayscale:X(),hueRotate:Z(),invert:X(),gap:_(),gradientColorStops:[e],gradientColorStopPositions:[L,P],inset:j(),margin:j(),opacity:Z(),padding:_(),saturate:Z(),scale:Z(),sepia:X(),skew:Z(),space:_(),translate:_()},classGroups:{aspect:[{aspect:["auto","square","video",O]}],container:["container"],columns:[{columns:[I]}],"break-after":[{"break-after":Y()}],"break-before":[{"break-before":Y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...U(),O]}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:R()}],"overscroll-x":[{"overscroll-x":R()}],"overscroll-y":[{"overscroll-y":R()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",D,O]}],basis:[{basis:j()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",O]}],grow:[{grow:X()}],shrink:[{shrink:X()}],order:[{order:["first","last","none",D,O]}],"grid-cols":[{"grid-cols":[H]}],"col-start-end":[{col:["auto",{span:["full",D,O]},O]}],"col-start":[{"col-start":K()}],"col-end":[{"col-end":K()}],"grid-rows":[{"grid-rows":[H]}],"row-start-end":[{row:["auto",{span:[D,O]},O]}],"row-start":[{"row-start":K()}],"row-end":[{"row-end":K()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",O]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",O]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...$()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...$(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...$(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[y]}],px:[{px:[y]}],py:[{py:[y]}],ps:[{ps:[y]}],pe:[{pe:[y]}],pt:[{pt:[y]}],pr:[{pr:[y]}],pb:[{pb:[y]}],pl:[{pl:[y]}],m:[{m:[v]}],mx:[{mx:[v]}],my:[{my:[v]}],ms:[{ms:[v]}],me:[{me:[v]}],mt:[{mt:[v]}],mr:[{mr:[v]}],mb:[{mb:[v]}],ml:[{ml:[v]}],"space-x":[{"space-x":[S]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[S]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",O,t]}],"min-w":[{"min-w":[O,t,"min","max","fit"]}],"max-w":[{"max-w":[O,t,"none","full","min","max","fit","prose",{screen:[I]},I]}],h:[{h:[O,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[O,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[O,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[O,t,"auto","min","max","fit"]}],"font-size":[{text:["base",I,P]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",T]}],"font-family":[{font:[H]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",O]}],"line-clamp":[{"line-clamp":["none",N,T]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",A,O]}],"list-image":[{"list-image":["none",O]}],"list-style-type":[{list:["none","disc","decimal",O]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...V(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",A,P]}],"underline-offset":[{"underline-offset":["auto",A,O]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:_()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",O]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",O]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...U(),z]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",F]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},W]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[l]}],"rounded-s":[{"rounded-s":[l]}],"rounded-e":[{"rounded-e":[l]}],"rounded-t":[{"rounded-t":[l]}],"rounded-r":[{"rounded-r":[l]}],"rounded-b":[{"rounded-b":[l]}],"rounded-l":[{"rounded-l":[l]}],"rounded-ss":[{"rounded-ss":[l]}],"rounded-se":[{"rounded-se":[l]}],"rounded-ee":[{"rounded-ee":[l]}],"rounded-es":[{"rounded-es":[l]}],"rounded-tl":[{"rounded-tl":[l]}],"rounded-tr":[{"rounded-tr":[l]}],"rounded-br":[{"rounded-br":[l]}],"rounded-bl":[{"rounded-bl":[l]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...V(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:V()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...V()]}],"outline-offset":[{"outline-offset":[A,O]}],"outline-w":[{outline:[A,P]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:B()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[A,P]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",I,G]}],"shadow-color":[{shadow:[H]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...q(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":q()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[s]}],"drop-shadow":[{"drop-shadow":["","none",I,O]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[b]}],sepia:[{sepia:[E]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[s]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[E]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",O]}],duration:[{duration:Z()}],ease:[{ease:["linear","in","out","in-out",O]}],delay:[{delay:Z()}],animate:[{animate:["none","spin","ping","pulse","bounce",O]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[D,O]}],"translate-x":[{"translate-x":[k]}],"translate-y":[{"translate-y":[k]}],"skew-x":[{"skew-x":[C]}],"skew-y":[{"skew-y":[C]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",O]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",O]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":_()}],"scroll-mx":[{"scroll-mx":_()}],"scroll-my":[{"scroll-my":_()}],"scroll-ms":[{"scroll-ms":_()}],"scroll-me":[{"scroll-me":_()}],"scroll-mt":[{"scroll-mt":_()}],"scroll-mr":[{"scroll-mr":_()}],"scroll-mb":[{"scroll-mb":_()}],"scroll-ml":[{"scroll-ml":_()}],"scroll-p":[{"scroll-p":_()}],"scroll-px":[{"scroll-px":_()}],"scroll-py":[{"scroll-py":_()}],"scroll-ps":[{"scroll-ps":_()}],"scroll-pe":[{"scroll-pe":_()}],"scroll-pt":[{"scroll-pt":_()}],"scroll-pr":[{"scroll-pr":_()}],"scroll-pb":[{"scroll-pb":_()}],"scroll-pl":[{"scroll-pl":_()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",O]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[A,P,T]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})},9708:(e,t,r)=>{r.d(t,{TL:()=>i});var n=r(2115),o=r(6101),l=r(5155);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){var i;let e,a,s=(i=r,(a=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(a=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),u=function(e,t){let r={...t};for(let n in t){let o=e[n],l=t[n];/^on[A-Z]/.test(n)?o&&l?r[n]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...l}:"className"===n&&(r[n]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(u.ref=t?(0,o.t)(t,s):s),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...i}=e,a=n.Children.toArray(o),u=a.find(s);if(u){let e=u.props.children,o=a.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,l.jsx)(t,{...i,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var a=Symbol("radix.slottable");function s(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},9946:(e,t,r)=>{r.d(t,{A:()=>s});var n=r(2115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:s,className:u="",children:c,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:t,...i,width:o,height:o,stroke:r,strokeWidth:s?24*Number(a)/Number(o):a,className:l("lucide",u),...f},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),s=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:s,...u}=r;return(0,n.createElement)(a,{ref:i,iconNode:t,className:l("lucide-".concat(o(e)),s),...u})});return r.displayName="".concat(e),r}}}]);