exports.id=923,exports.ids=[923],exports.modules={4536:(e,t,r)=>{let{createProxy:s}=r(39844);e.exports=s("C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\next\\dist\\client\\app-dir\\link.js")},7512:(e,t,r)=>{"use strict";r.d(t,{useSuspenseQuery:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call useSuspenseQuery() from the server but useSuspenseQuery is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\useSuspenseQuery.js","useSuspenseQuery")},10240:(e,t,r)=>{"use strict";r.d(t,{useSuspenseQueries:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call useSuspenseQueries() from the server but useSuspenseQueries is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\useSuspenseQueries.js","useSuspenseQueries")},12470:(e,t,r)=>{"use strict";r.d(t,{Cp:()=>p,EN:()=>f,Eh:()=>c,F$:()=>h,MK:()=>l,S$:()=>s,ZM:()=>R,ZZ:()=>w,Zw:()=>i,d2:()=>u,gn:()=>o,hT:()=>O,j3:()=>a,lQ:()=>n,nJ:()=>d,pl:()=>v,y9:()=>C,yy:()=>g});var s="undefined"==typeof window||"Deno"in globalThis;function n(){}function i(e,t){return"function"==typeof e?e(t):e}function o(e){return"number"==typeof e&&e>=0&&e!==1/0}function a(e,t){return Math.max(e+(t||0)-Date.now(),0)}function u(e,t){return"function"==typeof e?e(t):e}function c(e,t){return"function"==typeof e?e(t):e}function l(e,t){let{type:r="all",exact:s,fetchStatus:n,predicate:i,queryKey:o,stale:a}=e;if(o){if(s){if(t.queryHash!==h(o,t.options))return!1}else if(!p(t.queryKey,o))return!1}if("all"!==r){let e=t.isActive();if("active"===r&&!e||"inactive"===r&&e)return!1}return("boolean"!=typeof a||t.isStale()===a)&&(!n||n===t.state.fetchStatus)&&(!i||!!i(t))}function d(e,t){let{exact:r,status:s,predicate:n,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(r){if(f(t.options.mutationKey)!==f(i))return!1}else if(!p(t.options.mutationKey,i))return!1}return(!s||t.state.status===s)&&(!n||!!n(t))}function h(e,t){return(t?.queryKeyHashFn||f)(e)}function f(e){return JSON.stringify(e,(e,t)=>m(t)?Object.keys(t).sort().reduce((e,r)=>(e[r]=t[r],e),{}):t)}function p(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&Object.keys(t).every(r=>p(e[r],t[r]))}function y(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function m(e){if(!b(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!!b(r)&&!!r.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function b(e){return"[object Object]"===Object.prototype.toString.call(e)}function g(e){return new Promise(t=>{setTimeout(t,e)})}function v(e,t,r){return"function"==typeof r.structuralSharing?r.structuralSharing(e,t):!1!==r.structuralSharing?function e(t,r){if(t===r)return t;let s=y(t)&&y(r);if(s||m(t)&&m(r)){let n=s?t:Object.keys(t),i=n.length,o=s?r:Object.keys(r),a=o.length,u=s?[]:{},c=0;for(let i=0;i<a;i++){let a=s?i:o[i];(!s&&n.includes(a)||s)&&void 0===t[a]&&void 0===r[a]?(u[a]=void 0,c++):(u[a]=e(t[a],r[a]),u[a]===t[a]&&void 0!==t[a]&&c++)}return i===a&&c===i?t:u}return r}(e,t):t}function C(e,t,r=0){let s=[...e,t];return r&&s.length>r?s.slice(1):s}function w(e,t,r=0){let s=[t,...e];return r&&s.length>r?s.slice(0,-1):s}var O=Symbol();function R(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==O?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}},19100:(e,t,r)=>{"use strict";r.d(t,{useIsFetching:()=>o});var s=r(43210),n=r(33465),i=r(8693);function o(e,t){let r=(0,i.useQueryClient)(t),o=r.getQueryCache();return s.useSyncExternalStore(s.useCallback(e=>o.subscribe(n.jG.batchCalls(e)),[o]),()=>r.isFetching(e),()=>r.isFetching(e))}},20665:(e,t,r)=>{"use strict";r.d(t,{HydrationBoundary:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call HydrationBoundary() from the server but HydrationBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\HydrationBoundary.js","HydrationBoundary")},25080:(e,t,r)=>{"use strict";r.d(t,{useQuery:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call useQuery() from the server but useQuery is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\useQuery.js","useQuery")},25480:(e,t,r)=>{"use strict";r.d(t,{useSuspenseInfiniteQuery:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call useSuspenseInfiniteQuery() from the server but useSuspenseInfiniteQuery is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\useSuspenseInfiniteQuery.js","useSuspenseInfiniteQuery")},35917:(e,t,r)=>{"use strict";r.d(t,{useMutation:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call useMutation() from the server but useMutation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\useMutation.js","useMutation")},38124:(e,t,r)=>{"use strict";r.d(t,{i:()=>d});var s=r(49821),n=r(20665),i=r(69648);r(58830),r(6894),r(122),r(8096),r(41443),r(63334),r(43949),r(23271),r(59376),r(62505),r(72736),r(58218),r(90043),r(7982);var o=r(13495);r(14406),r(55194),r(3271),r(35355),r(28681),r(46049),r(63590),r(30785);var a=r(61120),u=r(12470);function c(e,t,r){let s=e.flatMap(e=>e.split("."));if(!t&&(!r||"any"===r))return s.length?[s]:[];if("infinite"===r&&(0,o.Gv)(t)&&("direction"in t||"cursor"in t)){let{cursor:e,direction:r,...n}=t;return[s,{input:n,type:"infinite"}]}return[s,{...void 0!==t&&t!==u.hT&&{input:t},...r&&"any"!==r&&{type:r}}]}let l=["prefetch","prefetchInfinite"];function d(e,t){return{trpc:(0,i.v)(async r=>{let s=[...r.path],n=[...r.args],i=s.reduce((e,t)=>l.includes(t)?e:e[t],e),o=n[0],a=i(o),u=s.pop();if("prefetch"===u){let e=n[1];return t().prefetchQuery({...e,queryKey:c(s,o,"query"),queryFn:()=>a})}if("prefetchInfinite"===u){let e=n[1];return t().prefetchInfiniteQuery({...e,queryKey:c(s,o,"infinite"),queryFn:()=>a,initialPageParam:e?.initialCursor??null})}return a}),HydrateClient:function(e){let r=(0,s.hw)(t());return a.createElement(n.HydrationBoundary,{state:r},e.children)}}}},39295:(e,t,r)=>{"use strict";r.d(t,{HydrationBoundary:()=>a});var s=r(43210),n=r(72083),i=r(8693),o=(e,t)=>"object"==typeof e&&null!==e&&t in e,a=({children:e,options:t={},state:r,queryClient:a})=>{let u=(0,i.useQueryClient)(a),[c,l]=s.useState(),d=s.useRef(t);return d.current=t,s.useMemo(()=>{if(r){if("object"!=typeof r)return;let e=u.getQueryCache(),t=r.queries||[],s=[],i=[];for(let r of t){let t=e.get(r.queryHash);if(t){let e=r.state.dataUpdatedAt>t.state.dataUpdatedAt||o(r.promise,"status")&&o(t.promise,"status")&&r.promise.status!==t.promise.status,s=c?.find(e=>e.queryHash===r.queryHash);e&&(!s||r.state.dataUpdatedAt>s.state.dataUpdatedAt)&&i.push(r)}else s.push(r)}s.length>0&&(0,n.Qv)(u,{queries:s},d.current),i.length>0&&l(e=>e?[...e,...i]:i)}},[u,c,r]),s.useEffect(()=>{c&&((0,n.Qv)(u,{queries:c},d.current),l(void 0))},[u,c]),e}},44999:(e,t,r)=>{"use strict";r.d(t,{b3:()=>s.b}),r(99933);var s=r(86280);r(73913)},49821:(e,t,r)=>{"use strict";function s(e){return e}function n(e){return e.state.isPaused}function i(e){return"success"===e.state.status}function o(e){return!0}function a(e,t={}){let r=t.shouldDehydrateMutation??e.getDefaultOptions().dehydrate?.shouldDehydrateMutation??n,u=e.getMutationCache().getAll().flatMap(e=>r(e)?[{mutationKey:e.options.mutationKey,state:e.state,...e.options.scope&&{scope:e.options.scope},...e.meta&&{meta:e.meta}}]:[]),c=t.shouldDehydrateQuery??e.getDefaultOptions().dehydrate?.shouldDehydrateQuery??i,l=t.shouldRedactErrors??e.getDefaultOptions().dehydrate?.shouldRedactErrors??o,d=t.serializeData??e.getDefaultOptions().dehydrate?.serializeData??s;return{mutations:u,queries:e.getQueryCache().getAll().flatMap(e=>c(e)?[{state:{...e.state,...void 0!==e.state.data&&{data:d(e.state.data)}},queryKey:e.queryKey,queryHash:e.queryHash,..."pending"===e.state.status&&{promise:e.promise?.then(d).catch(e=>l(e)?Promise.reject(Error("redacted")):Promise.reject(e))},...e.meta&&{meta:e.meta}}]:[])}}r.d(t,{XS:()=>i,hw:()=>a})},51503:(e,t,r)=>{"use strict";r.r(t),r.d(t,{QueryClientContext:()=>n,QueryClientProvider:()=>i,useQueryClient:()=>o});var s=r(12907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call QueryClientContext() from the server but QueryClientContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\QueryClientProvider.js","QueryClientContext"),i=(0,s.registerClientReference)(function(){throw Error("Attempted to call QueryClientProvider() from the server but QueryClientProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\QueryClientProvider.js","QueryClientProvider"),o=(0,s.registerClientReference)(function(){throw Error("Attempted to call useQueryClient() from the server but useQueryClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\QueryClientProvider.js","useQueryClient")},55465:(e,t,r)=>{"use strict";r.d(t,{IsRestoringProvider:()=>n,useIsRestoring:()=>i});var s=r(12907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call IsRestoringProvider() from the server but IsRestoringProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\IsRestoringProvider.js","IsRestoringProvider"),i=(0,s.registerClientReference)(function(){throw Error("Attempted to call useIsRestoring() from the server but useIsRestoring is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\IsRestoringProvider.js","useIsRestoring")},58920:(e,t,r)=>{"use strict";r.d(t,{useInfiniteQuery:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call useInfiniteQuery() from the server but useInfiniteQuery is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\useInfiniteQuery.js","useInfiniteQuery")},64480:(e,t,r)=>{"use strict";r.d(t,{useQueries:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call useQueries() from the server but useQueries is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\useQueries.js","useQueries")},73913:(e,t,r)=>{"use strict";let s=r(63033),n=r(29294),i=r(84971),o=r(76926),a=r(80023),u=r(98479);function c(){let e=n.workAsyncStorage.getStore(),t=s.workUnitAsyncStorage.getStore();switch((!e||!t)&&(0,s.throwForMissingRequestStore)("draftMode"),t.type){case"request":return l(t.draftMode,e);case"cache":case"unstable-cache":let r=(0,s.getDraftModeProviderForCacheScope)(e,t);if(r)return l(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return h(null);default:return t}}function l(e,t){let r,s=d.get(c);return s||(r=h(e),d.set(e,r),r)}let d=new WeakMap;function h(e){let t=new f(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class f{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){y("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){y("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let p=(0,o.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function y(e){let t=n.workAsyncStorage.getStore(),r=s.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new a.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let s=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,i.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,s,r)}else if("prerender-ppr"===r.type)(0,i.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let s=Object.defineProperty(new u.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=s.stack,s}}}}},74052:(e,t,r)=>{"use strict";r.d(t,{useIsMutating:()=>n,useMutationState:()=>i});var s=r(12907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call useIsMutating() from the server but useIsMutating is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\useMutationState.js","useIsMutating"),i=(0,s.registerClientReference)(function(){throw Error("Attempted to call useMutationState() from the server but useMutationState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\useMutationState.js","useMutationState")},82246:(e,t,r)=>{"use strict";r.d(t,{QueryErrorResetBoundary:()=>n,useQueryErrorResetBoundary:()=>i});var s=r(12907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call QueryErrorResetBoundary() from the server but QueryErrorResetBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\QueryErrorResetBoundary.js","QueryErrorResetBoundary"),i=(0,s.registerClientReference)(function(){throw Error("Attempted to call useQueryErrorResetBoundary() from the server but useQueryErrorResetBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\QueryErrorResetBoundary.js","useQueryErrorResetBoundary")},86280:(e,t,r)=>{"use strict";Object.defineProperty(t,"b",{enumerable:!0,get:function(){return d}});let s=r(92584),n=r(29294),i=r(63033),o=r(84971),a=r(80023),u=r(68388),c=r(76926),l=(r(44523),r(8719));function d(){let e=n.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,l.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return f(s.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new a.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,c=t;let s=h.get(c);if(s)return s;let n=(0,u.makeHangingPromise)(c.renderSignal,"`headers()`");return h.set(c,n),Object.defineProperties(n,{append:{value:function(){let e=`\`headers().append(${p(arguments[0])}, ...)\``,t=m(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},delete:{value:function(){let e=`\`headers().delete(${p(arguments[0])})\``,t=m(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},get:{value:function(){let e=`\`headers().get(${p(arguments[0])})\``,t=m(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},has:{value:function(){let e=`\`headers().has(${p(arguments[0])})\``,t=m(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},set:{value:function(){let e=`\`headers().set(${p(arguments[0])}, ...)\``,t=m(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=m(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=m(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},keys:{value:function(){let e="`headers().keys()`",t=m(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},values:{value:function(){let e="`headers().values()`",t=m(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},entries:{value:function(){let e="`headers().entries()`",t=m(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=m(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}}}),n}else"prerender-ppr"===t.type?(0,o.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,o.throwToInterruptStaticGeneration)("headers",e,t);(0,o.trackDynamicDataInDynamicRender)(e,t)}return f((0,i.getExpectedRequestStore)("headers").headers)}let h=new WeakMap;function f(e){let t=h.get(e);if(t)return t;let r=Promise.resolve(e);return h.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function p(e){return"string"==typeof e?`'${e}'`:"..."}let y=(0,c.createDedupedByCallsiteServerErrorLoggerDev)(m);function m(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}},92584:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return i},ReadonlyHeadersError:function(){return n}});let s=r(43763);class n extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new n}}class i extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,n){if("symbol"==typeof r)return s.ReflectAdapter.get(t,r,n);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==o)return s.ReflectAdapter.get(t,o,n)},set(t,r,n,i){if("symbol"==typeof r)return s.ReflectAdapter.set(t,r,n,i);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);return s.ReflectAdapter.set(t,a??r,n,i)},has(t,r){if("symbol"==typeof r)return s.ReflectAdapter.has(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0!==i&&s.ReflectAdapter.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return s.ReflectAdapter.deleteProperty(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0===i||s.ReflectAdapter.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return n.callable;default:return s.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new i(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,s]of this.entries())e.call(t,s,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},93250:(e,t,r)=>{"use strict";r.d(t,{useIsFetching:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call useIsFetching() from the server but useIsFetching is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\node_modules\\@tanstack\\react-query\\build\\modern\\useIsFetching.js","useIsFetching")},94069:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return h},ReadonlyRequestCookiesError:function(){return a},RequestCookiesAdapter:function(){return u},appendMutableCookies:function(){return d},areCookiesMutableInCurrentPhase:function(){return p},getModifiedCookieValues:function(){return l},responseCookiesToRequestCookies:function(){return m},wrapWithMutableAccessCheck:function(){return f}});let s=r(23158),n=r(43763),i=r(29294),o=r(63033);class a extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new a}}class u{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return a.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}}let c=Symbol.for("next.mutated.cookies");function l(e){let t=e[c];return t&&Array.isArray(t)&&0!==t.length?t:[]}function d(e,t){let r=l(t);if(0===r.length)return!1;let n=new s.ResponseCookies(e),i=n.getAll();for(let e of r)n.set(e);for(let e of i)n.set(e);return!0}class h{static wrap(e,t){let r=new s.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let o=[],a=new Set,u=()=>{let e=i.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),o=r.getAll().filter(e=>a.has(e.name)),t){let e=[];for(let t of o){let r=new s.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},l=new Proxy(r,{get(e,t,r){switch(t){case c:return o;case"delete":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),l}finally{u()}};case"set":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),l}finally{u()}};default:return n.ReflectAdapter.get(e,t,r)}}});return l}}function f(e){let t=new Proxy(e,{get(e,r,s){switch(r){case"delete":return function(...r){return y("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return y("cookies().set"),e.set(...r),t};default:return n.ReflectAdapter.get(e,r,s)}}});return t}function p(e){return"action"===e.phase}function y(e){if(!p((0,o.getExpectedRequestStore)(e)))throw new a}function m(e){let t=new s.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},94532:(e,t,r)=>{"use strict";r.d(t,{E:()=>O});var s=r(12470),n=e=>setTimeout(e,0),i=function(){let e=[],t=0,r=e=>{e()},s=e=>{e()},i=n,o=s=>{t?e.push(s):i(()=>{r(s)})},a=()=>{let t=e;e=[],t.length&&i(()=>{s(()=>{t.forEach(e=>{r(e)})})})};return{batch:e=>{let r;t++;try{r=e()}finally{--t||a()}return r},batchCalls:e=>(...t)=>{o(()=>{e(...t)})},schedule:o,setNotifyFunction:e=>{r=e},setBatchNotifyFunction:e=>{s=e},setScheduler:e=>{i=e}}}(),o=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},a=new class extends o{#e;#t;#r;constructor(){super(),this.#r=e=>{if(!s.S$&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#e?this.#e:globalThis.document?.visibilityState!=="hidden"}},u=new class extends o{#s=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!s.S$&&window.addEventListener){let t=()=>e(!0),r=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#s!==e&&(this.#s=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#s}};function c(e){return Math.min(1e3*2**e,3e4)}function l(e){return(e??"online")!=="online"||u.isOnline()}var d=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function h(e){return e instanceof d}function f(e){let t,r=!1,n=0,i=!1,o=function(){let e,t,r=new Promise((r,s)=>{e=r,t=s});function s(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch(()=>{}),r.resolve=t=>{s({status:"fulfilled",value:t}),e(t)},r.reject=e=>{s({status:"rejected",reason:e}),t(e)},r}(),h=()=>a.isFocused()&&("always"===e.networkMode||u.isOnline())&&e.canRun(),f=()=>l(e.networkMode)&&e.canRun(),p=r=>{i||(i=!0,e.onSuccess?.(r),t?.(),o.resolve(r))},y=r=>{i||(i=!0,e.onError?.(r),t?.(),o.reject(r))},m=()=>new Promise(r=>{t=e=>{(i||h())&&r(e)},e.onPause?.()}).then(()=>{t=void 0,i||e.onContinue?.()}),b=()=>{let t;if(i)return;let o=0===n?e.initialPromise:void 0;try{t=o??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(p).catch(t=>{if(i)return;let o=e.retry??3*!s.S$,a=e.retryDelay??c,u="function"==typeof a?a(n,t):a,l=!0===o||"number"==typeof o&&n<o||"function"==typeof o&&o(n,t);if(r||!l)return void y(t);n++,e.onFail?.(n,t),(0,s.yy)(u).then(()=>h()?void 0:m()).then(()=>{r?y(t):b()})})};return{promise:o,cancel:t=>{i||(y(new d(t)),e.abort?.())},continue:()=>(t?.(),o),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart:f,start:()=>(f()?b():m().then(b),o)}}var p=class{#n;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,s.gn)(this.gcTime)&&(this.#n=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(s.S$?1/0:3e5))}clearGcTimeout(){this.#n&&(clearTimeout(this.#n),this.#n=void 0)}},y=class extends p{#i;#o;#a;#u;#c;#l;#d;constructor(e){super(),this.#d=!1,this.#l=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#u=e.client,this.#a=this.#u.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#i=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,s=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#i,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#c?.promise}setOptions(e){this.options={...this.#l,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#a.remove(this)}setData(e,t){let r=(0,s.pl)(this.state.data,e,this.options);return this.#h({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#h({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#c?.promise;return this.#c?.cancel(e),t?t.then(s.lQ).catch(s.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#i)}isActive(){return this.observers.some(e=>!1!==(0,s.Eh)(e.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===s.hT||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!(0,s.j3)(this.state.dataUpdatedAt,e)}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#c?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#c?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#a.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#c&&(this.#d?this.#c.cancel({revert:!0}):this.#c.cancelRetry()),this.scheduleGc()),this.#a.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#h({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#c)return this.#c.continueRetry(),this.#c.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let r=new AbortController,n=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#d=!0,r.signal)})},i={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#u,state:this.state,fetchFn:()=>{let e=(0,s.ZM)(this.options,t),r={client:this.#u,queryKey:this.queryKey,meta:this.meta};return(n(r),this.#d=!1,this.options.persister)?this.options.persister(e,r,this):e(r)}};n(i),this.options.behavior?.onFetch(i,this),this.#o=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==i.fetchOptions?.meta)&&this.#h({type:"fetch",meta:i.fetchOptions?.meta});let o=e=>{h(e)&&e.silent||this.#h({type:"error",error:e}),h(e)||(this.#a.config.onError?.(e,this),this.#a.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#c=f({initialPromise:t?.initialPromise,fn:i.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0===e)return void o(Error(`${this.queryHash} data is undefined`));try{this.setData(e)}catch(e){o(e);return}this.#a.config.onSuccess?.(e,this),this.#a.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:o,onFail:(e,t)=>{this.#h({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#h({type:"pause"})},onContinue:()=>{this.#h({type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0}),this.#c.start()}#h(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":var r;return{...t,...(r=t.data,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:l(this.options.networkMode)?"fetching":"paused",...void 0===r&&{error:null,status:"pending"}}),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let s=e.error;if(h(s)&&s.revert&&this.#o)return{...this.#o,fetchStatus:"idle"};return{...t,error:s,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),i.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#a.notify({query:this,type:"updated",action:e})})}},m=class extends o{constructor(e={}){super(),this.config=e,this.#f=new Map}#f;build(e,t,r){let n=t.queryKey,i=t.queryHash??(0,s.F$)(n,t),o=this.get(i);return o||(o=new y({client:e,queryKey:n,queryHash:i,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(n)}),this.add(o)),o}add(e){this.#f.has(e.queryHash)||(this.#f.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#f.get(e.queryHash);t&&(e.destroy(),t===e&&this.#f.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){i.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#f.get(e)}getAll(){return[...this.#f.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,s.MK)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,s.MK)(e,t)):t}notify(e){i.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){i.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){i.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},b=class extends p{#p;#y;#c;constructor(e){super(),this.mutationId=e.mutationId,this.#y=e.mutationCache,this.#p=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#p.includes(e)||(this.#p.push(e),this.clearGcTimeout(),this.#y.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#p=this.#p.filter(t=>t!==e),this.scheduleGc(),this.#y.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#y.remove(this))}continue(){return this.#c?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#h({type:"continue"})};this.#c=f({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#h({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#h({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#y.canRun(this)});let r="pending"===this.state.status,s=!this.#c.canStart();try{if(r)t();else{this.#h({type:"pending",variables:e,isPaused:s}),await this.#y.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#h({type:"pending",context:t,variables:e,isPaused:s})}let n=await this.#c.start();return await this.#y.config.onSuccess?.(n,e,this.state.context,this),await this.options.onSuccess?.(n,e,this.state.context),await this.#y.config.onSettled?.(n,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(n,null,e,this.state.context),this.#h({type:"success",data:n}),n}catch(t){try{throw await this.#y.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#y.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#h({type:"error",error:t})}}finally{this.#y.runNext(this)}}#h(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),i.batch(()=>{this.#p.forEach(t=>{t.onMutationUpdate(e)}),this.#y.notify({mutation:this,type:"updated",action:e})})}},g=class extends o{constructor(e={}){super(),this.config=e,this.#m=new Set,this.#b=new Map,this.#g=0}#m;#b;#g;build(e,t,r){let s=new b({mutationCache:this,mutationId:++this.#g,options:e.defaultMutationOptions(t),state:r});return this.add(s),s}add(e){this.#m.add(e);let t=v(e);if("string"==typeof t){let r=this.#b.get(t);r?r.push(e):this.#b.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#m.delete(e)){let t=v(e);if("string"==typeof t){let r=this.#b.get(t);if(r)if(r.length>1){let t=r.indexOf(e);-1!==t&&r.splice(t,1)}else r[0]===e&&this.#b.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=v(e);if("string"!=typeof t)return!0;{let r=this.#b.get(t),s=r?.find(e=>"pending"===e.state.status);return!s||s===e}}runNext(e){let t=v(e);if("string"!=typeof t)return Promise.resolve();{let r=this.#b.get(t)?.find(t=>t!==e&&t.state.isPaused);return r?.continue()??Promise.resolve()}}clear(){i.batch(()=>{this.#m.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#m.clear(),this.#b.clear()})}getAll(){return Array.from(this.#m)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,s.nJ)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,s.nJ)(e,t))}notify(e){i.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return i.batch(()=>Promise.all(e.map(e=>e.continue().catch(s.lQ))))}};function v(e){return e.options.scope?.id}function C(e){return{onFetch:(t,r)=>{let n=t.options,i=t.fetchOptions?.meta?.fetchMore?.direction,o=t.state.data?.pages||[],a=t.state.data?.pageParams||[],u={pages:[],pageParams:[]},c=0,l=async()=>{let r=!1,l=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",()=>{r=!0}),t.signal)})},d=(0,s.ZM)(t.options,t.fetchOptions),h=async(e,n,i)=>{if(r)return Promise.reject();if(null==n&&e.pages.length)return Promise.resolve(e);let o={client:t.client,queryKey:t.queryKey,pageParam:n,direction:i?"backward":"forward",meta:t.options.meta};l(o);let a=await d(o),{maxPages:u}=t.options,c=i?s.ZZ:s.y9;return{pages:c(e.pages,a,u),pageParams:c(e.pageParams,n,u)}};if(i&&o.length){let e="backward"===i,t={pages:o,pageParams:a},r=(e?function(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}:w)(n,t);u=await h(t,r,e)}else{let t=e??o.length;do{let e=0===c?a[0]??n.initialPageParam:w(n,u);if(c>0&&null==e)break;u=await h(u,e),c++}while(c<t)}return u};t.options.persister?t.fetchFn=()=>t.options.persister?.(l,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=l}}}function w(e,{pages:t,pageParams:r}){let s=t.length-1;return t.length>0?e.getNextPageParam(t[s],t,r[s],r):void 0}var O=class{#v;#y;#l;#C;#w;#O;#R;#E;constructor(e={}){this.#v=e.queryCache||new m,this.#y=e.mutationCache||new g,this.#l=e.defaultOptions||{},this.#C=new Map,this.#w=new Map,this.#O=0}mount(){this.#O++,1===this.#O&&(this.#R=a.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#v.onFocus())}),this.#E=u.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#v.onOnline())}))}unmount(){this.#O--,0===this.#O&&(this.#R?.(),this.#R=void 0,this.#E?.(),this.#E=void 0)}isFetching(e){return this.#v.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#y.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#v.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),r=this.#v.build(this,t),n=r.state.data;return void 0===n?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime((0,s.d2)(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(n))}getQueriesData(e){return this.#v.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,r){let n=this.defaultQueryOptions({queryKey:e}),i=this.#v.get(n.queryHash),o=i?.state.data,a=(0,s.Zw)(t,o);if(void 0!==a)return this.#v.build(this,n).setData(a,{...r,manual:!0})}setQueriesData(e,t,r){return i.batch(()=>this.#v.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,r)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#v.get(t.queryHash)?.state}removeQueries(e){let t=this.#v;i.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let r=this.#v;return i.batch(()=>(r.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let r={revert:!0,...t};return Promise.all(i.batch(()=>this.#v.findAll(e).map(e=>e.cancel(r)))).then(s.lQ).catch(s.lQ)}invalidateQueries(e,t={}){return i.batch(()=>(this.#v.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let r={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(i.batch(()=>this.#v.findAll(e).filter(e=>!e.isDisabled()).map(e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(s.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(s.lQ)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let r=this.#v.build(this,t);return r.isStaleByTime((0,s.d2)(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(s.lQ).catch(s.lQ)}fetchInfiniteQuery(e){return e.behavior=C(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(s.lQ).catch(s.lQ)}ensureInfiniteQueryData(e){return e.behavior=C(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return u.isOnline()?this.#y.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#v}getMutationCache(){return this.#y}getDefaultOptions(){return this.#l}setDefaultOptions(e){this.#l=e}setQueryDefaults(e,t){this.#C.set((0,s.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#C.values()],r={};return t.forEach(t=>{(0,s.Cp)(e,t.queryKey)&&Object.assign(r,t.defaultOptions)}),r}setMutationDefaults(e,t){this.#w.set((0,s.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#w.values()],r={};return t.forEach(t=>{(0,s.Cp)(e,t.mutationKey)&&Object.assign(r,t.defaultOptions)}),r}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#l.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,s.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===s.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#l.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#v.clear(),this.#y.clear()}}},97322:(e,t,r)=>{"use strict";r.d(t,{useIsMutating:()=>a,useMutationState:()=>c});var s=r(43210),n=r(31212),i=r(33465),o=r(8693);function a(e,t){let r=(0,o.useQueryClient)(t);return c({filters:{...e,status:"pending"}},r).length}function u(e,t){return e.findAll(t.filters).map(e=>t.select?t.select(e):e.state)}function c(e={},t){let r=(0,o.useQueryClient)(t).getMutationCache(),a=s.useRef(e),l=s.useRef(null);return l.current||(l.current=u(r,e)),s.useEffect(()=>{a.current=e}),s.useSyncExternalStore(s.useCallback(e=>r.subscribe(()=>{let t=(0,n.BH)(l.current,u(r,a.current));l.current!==t&&(l.current=t,i.jG.schedule(e))}),[r]),()=>l.current,()=>l.current)}},99933:(e,t,r)=>{"use strict";let s=r(94069),n=r(23158),i=r(29294),o=r(63033),a=r(84971),u=r(80023),c=r(68388),l=r(76926),d=(r(44523),r(8719)),h=new WeakMap;function f(e){let t=h.get(e);if(t)return t;let r=Promise.resolve(e);return h.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):b.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):g.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function p(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let y=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(m);function m(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function b(){return this.getAll().map(e=>[e.name,e]).values()}function g(e){for(let e of this.getAll())this.delete(e.name);return e}}};