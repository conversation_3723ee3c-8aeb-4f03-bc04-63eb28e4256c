"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { Card, CardContent } from "~/components/ui/card"
import { Label } from "~/components/ui/label"
import { Slider } from "~/components/ui/slider"
import { Switch } from "~/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "~/components/ui/dropdown-menu"
import { Moon, Sun, MapPin, Building2, DollarSign, Star, Search, Menu } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { api } from "~/trpc/react"

export default function DataScraperLanding() {
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [priceRange, setPriceRange] = useState([500, 5000])
  const [rating, setRating] = useState([3.5])
  const [location, setLocation] = useState("")
  const [category, setCategory] = useState("")
  const [country, setCountry] = useState("India")
  const router = useRouter()

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode)
    document.documentElement.classList.toggle("dark")
  }

  // tRPC mutation for scraping
  const scrapeMutation = api.scraper.scrapeBusiness.useMutation({
    onSuccess: (data) => {
      console.log("Scraping successful:", data)
      // Redirect to results page with parameters
      const params = new URLSearchParams({
        type: "data",
        location,
        category,
      })
      router.push(`/results?${params.toString()}`)
    },
    onError: (error) => {
      console.error("Scraping failed:", error)
      alert("Scraping failed. Please try again.")
    },
  })

  const handleStartScraping = () => {
    if (!location || !category) {
      alert("Please select location and category")
      return
    }

    scrapeMutation.mutate({
      location,
      category,
      country,
    })
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${isDarkMode ? "dark" : ""}`}>
      {/* Navigation Bar */}
      <nav className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">DataScraper</h1>
              </div>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-8">
                <Link
                  href="#"
                  className="text-gray-900 dark:text-white hover:text-green-600 dark:hover:text-green-400 px-3 py-2 text-sm font-medium transition-colors"
                >
                  Home
                </Link>
                <Link
                  href="#"
                  className="text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 px-3 py-2 text-sm font-medium transition-colors"
                >
                  Categories
                </Link>
                <Link
                  href="#"
                  className="text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 px-3 py-2 text-sm font-medium transition-colors"
                >
                  Pricing
                </Link>
                <Link
                  href="#"
                  className="text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 px-3 py-2 text-sm font-medium transition-colors"
                >
                  How It Works
                </Link>
                <Link
                  href="#"
                  className="text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 px-3 py-2 text-sm font-medium transition-colors"
                >
                  Login
                </Link>
                <Button className="bg-green-600 hover:bg-green-700 text-white">Sign Up</Button>
              </div>
            </div>

            {/* Dark Mode Toggle & Mobile Menu */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Sun className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                <Switch
                  checked={isDarkMode}
                  onCheckedChange={toggleDarkMode}
                  className="data-[state=checked]:bg-green-600"
                />
                <Moon className="h-4 w-4 text-gray-600 dark:text-gray-300" />
              </div>

              {/* Mobile menu button */}
              <div className="md:hidden">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <Menu className="h-5 w-5" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem>Home</DropdownMenuItem>
                    <DropdownMenuItem>Categories</DropdownMenuItem>
                    <DropdownMenuItem>Pricing</DropdownMenuItem>
                    <DropdownMenuItem>How It Works</DropdownMenuItem>
                    <DropdownMenuItem>Login</DropdownMenuItem>
                    <DropdownMenuItem>Sign Up</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Image with Blur */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('/placeholder.svg?height=1080&width=1920')`,
            filter: "blur(8px)",
            transform: "scale(1.1)",
          }}
        />

        {/* Overlay */}
        <div className="absolute inset-0 bg-black/40 dark:bg-black/60" />

        {/* Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            {/* Main Headline */}
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-8 leading-tight">
              Find the Data You Need
              <span className="block text-green-400">Instantly</span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-200 mb-12 max-w-3xl mx-auto">
              Powerful web scraping platform that helps you extract valuable business data with precision and speed.
            </p>

            {/* Filter Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              {/* Location Filter */}
              <Card className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <MapPin className="h-5 w-5 text-green-600 mr-2" />
                    <Label className="text-sm font-semibold text-gray-700 dark:text-gray-200">Location</Label>
                  </div>
                  <Select value={location} onValueChange={setLocation}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select City/State" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="mumbai">Mumbai, Maharashtra</SelectItem>
                      <SelectItem value="delhi">Delhi, NCR</SelectItem>
                      <SelectItem value="bangalore">Bangalore, Karnataka</SelectItem>
                      <SelectItem value="chennai">Chennai, Tamil Nadu</SelectItem>
                      <SelectItem value="kolkata">Kolkata, West Bengal</SelectItem>
                    </SelectContent>
                  </Select>
                </CardContent>
              </Card>

              {/* Business Category Filter */}
              <Card className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <Building2 className="h-5 w-5 text-green-600 mr-2" />
                    <Label className="text-sm font-semibold text-gray-700 dark:text-gray-200">Category</Label>
                  </div>
                  <Select value={category} onValueChange={setCategory}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Business Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="restaurant">Restaurant</SelectItem>
                      <SelectItem value="gym">Gym & Fitness</SelectItem>
                      <SelectItem value="retail">Retail Store</SelectItem>
                      <SelectItem value="healthcare">Healthcare</SelectItem>
                      <SelectItem value="education">Education</SelectItem>
                    </SelectContent>
                  </Select>
                </CardContent>
              </Card>

              {/* Price Range Filter */}
              <Card className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <DollarSign className="h-5 w-5 text-green-600 mr-2" />
                    <Label className="text-sm font-semibold text-gray-700 dark:text-gray-200">Price Range</Label>
                  </div>
                  <div className="space-y-3">
                    <Slider
                      value={priceRange}
                      onValueChange={setPriceRange}
                      max={5000}
                      min={500}
                      step={100}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400">
                      <span>₹{priceRange[0]}</span>
                      <span>₹{priceRange[1]}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Rating Filter */}
              <Card className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <Star className="h-5 w-5 text-green-600 mr-2" />
                    <Label className="text-sm font-semibold text-gray-700 dark:text-gray-200">Rating</Label>
                  </div>
                  <div className="space-y-3">
                    <Slider value={rating} onValueChange={setRating} max={5} min={1} step={0.5} className="w-full" />
                    <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400">
                      <span>{rating[0]} stars</span>
                      <span>5 stars</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Start Scraping Button */}
            <Button
              size="lg"
              className="bg-green-600 hover:bg-green-700 text-white px-12 py-6 text-xl font-semibold rounded-xl shadow-2xl hover:shadow-green-500/25 transition-all duration-300 transform hover:scale-105"
              onClick={handleStartScraping}
              disabled={scrapeMutation.isPending}
            >
              <Search className="mr-3 h-6 w-6" />
              {scrapeMutation.isPending ? "Scraping..." : "Start Scraping"}
            </Button>

            {/* Additional Info */}
            <p className="text-gray-300 mt-6 text-sm">
              No credit card required • Free trial available • 99.9% uptime guarantee
            </p>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">Why Choose Our Platform?</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Advanced web scraping technology that delivers accurate, real-time data for your business needs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="bg-white dark:bg-gray-800 border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Search className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Fast & Accurate</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Extract data with 99.9% accuracy using our advanced algorithms and machine learning technology.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Building2 className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Scalable Solution</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  From small businesses to enterprise-level operations, our platform scales with your needs.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Star className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">24/7 Support</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Get round-the-clock support from our expert team to ensure your data extraction never stops.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}
