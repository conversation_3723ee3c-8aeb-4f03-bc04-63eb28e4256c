(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[791],{2490:(e,t,s)=>{"use strict";s.d(t,{useIsFetching:()=>l});var i=s(2115),a=s(7165),r=s(6715);function l(e,t){let s=(0,r.useQueryClient)(t),l=s.getQueryCache();return i.useSyncExternalStore(i.useCallback(e=>l.subscribe(a.jG.batchCalls(e)),[l]),()=>s.isFetching(e),()=>s.isFetching(e))}},3964:(e,t,s)=>{"use strict";let i;s.d(t,{TRPCReactProvider:()=>x,F:()=>g});var a=s(5155),r=s(6715),l=s(1213),n=s(7566),o=s(2115),c=s(9177),d=s(2775),u=s(1451);let m=()=>new d.E({defaultOptions:{queries:{staleTime:3e4},dehydrate:{serializeData:c.Ay.serialize,shouldDehydrateQuery:e=>(0,u.XS)(e)||"pending"===e.state.status},hydrate:{deserializeData:c.Ay.deserialize}}});s(9509);let h=()=>(null!=i||(i=m()),i),g=(0,n.pY)();function x(e){let t=h(),[s]=(0,o.useState)(()=>g.createClient({links:[(0,l.$H)({enabled:e=>"down"===e.direction&&e.result instanceof Error}),(0,l.N9)({transformer:c.Ay,url:window.location.origin+"/api/trpc",headers:()=>{let e=new Headers;return e.set("x-trpc-source","nextjs-react"),e}})]}));return(0,a.jsx)(r.QueryClientProvider,{client:t,children:(0,a.jsx)(g.Provider,{client:s,queryClient:t,children:e.children})})}},7573:(e,t,s)=>{"use strict";s.d(t,{HydrationBoundary:()=>n});var i=s(2115),a=s(1451),r=s(6715),l=(e,t)=>"object"==typeof e&&null!==e&&t in e,n=e=>{let{children:t,options:s={},state:n,queryClient:o}=e,c=(0,r.useQueryClient)(o),[d,u]=i.useState(),m=i.useRef(s);return m.current=s,i.useMemo(()=>{if(n){if("object"!=typeof n)return;let e=c.getQueryCache(),t=n.queries||[],s=[],i=[];for(let a of t){let t=e.get(a.queryHash);if(t){let e=a.state.dataUpdatedAt>t.state.dataUpdatedAt||l(a.promise,"status")&&l(t.promise,"status")&&a.promise.status!==t.promise.status,s=null==d?void 0:d.find(e=>e.queryHash===a.queryHash);e&&(!s||a.state.dataUpdatedAt>s.state.dataUpdatedAt)&&i.push(a)}else s.push(a)}s.length>0&&(0,a.Qv)(c,{queries:s},m.current),i.length>0&&u(e=>e?[...e,...i]:i)}},[c,d,n]),i.useEffect(()=>{d&&((0,a.Qv)(c,{queries:d},m.current),u(void 0))},[c,d]),t}},9092:(e,t,s)=>{Promise.resolve().then(s.bind(s,7573)),Promise.resolve().then(s.bind(s,1581)),Promise.resolve().then(s.bind(s,6715)),Promise.resolve().then(s.bind(s,382)),Promise.resolve().then(s.bind(s,8822)),Promise.resolve().then(s.bind(s,2490)),Promise.resolve().then(s.bind(s,5041)),Promise.resolve().then(s.bind(s,9138)),Promise.resolve().then(s.bind(s,1610)),Promise.resolve().then(s.bind(s,5838)),Promise.resolve().then(s.bind(s,5490)),Promise.resolve().then(s.bind(s,1142)),Promise.resolve().then(s.bind(s,3666)),Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.bind(s,9223))},9138:(e,t,s)=>{"use strict";s.d(t,{useIsMutating:()=>n,useMutationState:()=>c});var i=s(2115),a=s(2020),r=s(7165),l=s(6715);function n(e,t){let s=(0,l.useQueryClient)(t);return c({filters:{...e,status:"pending"}},s).length}function o(e,t){return e.findAll(t.filters).map(e=>t.select?t.select(e):e.state)}function c(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,s=(0,l.useQueryClient)(t).getMutationCache(),n=i.useRef(e),c=i.useRef(null);return c.current||(c.current=o(s,e)),i.useEffect(()=>{n.current=e}),i.useSyncExternalStore(i.useCallback(e=>s.subscribe(()=>{let t=(0,a.BH)(c.current,o(s,n.current));c.current!==t&&(c.current=t,r.jG.schedule(e))}),[s]),()=>c.current,()=>c.current)}},9223:(e,t,s)=>{"use strict";s.d(t,{MapsScraperForm:()=>c});var i=s(5155),a=s(2115);function r(e){let{data:t}=e,[s,r]=(0,a.useState)(1),[l,n]=(0,a.useState)("name"),[o,c]=(0,a.useState)("asc"),d=e=>{e===l?c("asc"===o?"desc":"asc"):(n(e),c("asc"))},u=(0,a.useMemo)(()=>[...t].sort((e,t)=>{let s=0;switch(l){case"name":s=e.name.localeCompare(t.name);break;case"city":s=e.city.localeCompare(t.city);break;case"category":s=e.category.localeCompare(t.category);break;case"rating":s=e.rating-t.rating;break;case"reviews":s=e.reviews-t.reviews;break;case"mostVisited":s=e.mostVisited===t.mostVisited?0:e.mostVisited?1:-1}return"asc"===o?s:-s}),[t,l,o]),m=Math.ceil(u.length/10),h=(s-1)*10,g=Math.min(h+10,u.length),x=u.slice(h,g);return 0===t.length?(0,i.jsx)("div",{className:"rounded-lg bg-white/10 p-6 text-center",children:(0,i.jsx)("p",{className:"text-lg",children:"No results found"})}):(0,i.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold",children:"Scraped Results"}),(0,i.jsx)("button",{onClick:()=>{let e=new Blob([["Name,City,Category,Rating,Reviews,Most Visited,Address,Phone,Website",...u.map(e=>[e.name,e.city,e.category,e.rating.toString(),e.reviews.toString(),e.mostVisited?"Yes":"No",e.address||"",e.phone||"",e.website||""]).map(e=>e.map(e=>'"'.concat(String(e).replace(/"/g,'""'),'"')).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),t=URL.createObjectURL(e),s=document.createElement("a");s.setAttribute("href",t),s.setAttribute("download","google_maps_results.csv"),document.body.appendChild(s),s.click(),document.body.removeChild(s)},className:"rounded-lg bg-[hsl(280,100%,70%)] px-4 py-2 font-medium text-white transition hover:bg-[hsl(280,100%,60%)]",children:"Download CSV"})]}),(0,i.jsx)("div",{className:"overflow-x-auto rounded-lg bg-white/5",children:(0,i.jsxs)("table",{className:"w-full border-collapse",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{className:"border-b border-white/10 bg-white/10 text-left",children:[(0,i.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>d("name"),children:["Name ","name"===l&&("asc"===o?"↑":"↓")]}),(0,i.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>d("city"),children:["City ","city"===l&&("asc"===o?"↑":"↓")]}),(0,i.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>d("category"),children:["Category ","category"===l&&("asc"===o?"↑":"↓")]}),(0,i.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>d("rating"),children:["Rating ","rating"===l&&("asc"===o?"↑":"↓")]}),(0,i.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>d("reviews"),children:["Reviews ","reviews"===l&&("asc"===o?"↑":"↓")]}),(0,i.jsxs)("th",{className:"cursor-pointer p-4 font-semibold",onClick:()=>d("mostVisited"),children:["Most Visited ","mostVisited"===l&&("asc"===o?"↑":"↓")]})]})}),(0,i.jsx)("tbody",{children:x.map((e,t)=>(0,i.jsxs)("tr",{className:"border-b border-white/10 transition hover:bg-white/5",children:[(0,i.jsx)("td",{className:"p-4",children:e.name}),(0,i.jsx)("td",{className:"p-4",children:e.city}),(0,i.jsx)("td",{className:"p-4",children:e.category}),(0,i.jsx)("td",{className:"p-4",children:e.rating.toFixed(1)}),(0,i.jsx)("td",{className:"p-4",children:e.reviews}),(0,i.jsx)("td",{className:"p-4",children:e.mostVisited?"Yes":"No"})]},t))})]})}),m>1&&(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"text-sm text-white/70",children:["Showing ",h+1,"-",g," of ",u.length," results"]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)("button",{onClick:()=>{r(e=>Math.max(e-1,1))},disabled:1===s,className:"rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50",children:"Previous"}),(0,i.jsxs)("div",{className:"flex items-center justify-center rounded-lg bg-white/10 px-4 py-2",children:[s," / ",m]}),(0,i.jsx)("button",{onClick:()=>{r(e=>Math.min(e+1,m))},disabled:s===m,className:"rounded-lg bg-white/10 px-4 py-2 transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50",children:"Next"})]})]})]})}function l(e){let{isProcessingMultiple:t,progress:s}=e;return(0,i.jsxs)("div",{className:"flex flex-col items-center justify-center py-8",children:[(0,i.jsx)("div",{className:"mb-4 h-12 w-12 animate-spin rounded-full border-4 border-white/20 border-t-[hsl(280,100%,70%)]"}),t&&s?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("p",{className:"text-lg font-medium",children:["Scraping Google Maps... (",s.current+1,"/",s.total,")"]}),(0,i.jsx)("div",{className:"mt-3 h-2 w-64 overflow-hidden rounded-full bg-white/10",children:(0,i.jsx)("div",{className:"h-full bg-[hsl(280,100%,70%)]",style:{width:"".concat(Math.round(s.current/s.total*100),"%")}})}),(0,i.jsx)("p",{className:"mt-2 text-sm text-white/70",children:"Processing multiple cities. Please wait..."})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("p",{className:"text-lg font-medium",children:"Scraping Google Maps..."}),(0,i.jsx)("p",{className:"mt-2 text-sm text-white/70",children:"This may take a few moments depending on the number of results."})]})]})}var n=s(3964);let o=[{code:"us",name:"United States",cities:["New York","Los Angeles","Chicago","Houston","Phoenix","Philadelphia","San Antonio","San Diego","Dallas","San Jose"]},{code:"uk",name:"United Kingdom",cities:["London","Birmingham","Manchester","Glasgow","Liverpool","Bristol","Edinburgh","Leeds","Sheffield","Newcastle"]},{code:"ca",name:"Canada",cities:["Toronto","Montreal","Vancouver","Calgary","Edmonton","Ottawa","Winnipeg","Quebec City","Hamilton","Kitchener"]},{code:"au",name:"Australia",cities:["Sydney","Melbourne","Brisbane","Perth","Adelaide","Gold Coast","Newcastle","Canberra","Wollongong","Hobart"]},{code:"in",name:"India",cities:["Mumbai","Delhi","Bangalore","Hyderabad","Chennai","Kolkata","Pune","Ahmedabad","Jaipur","Surat"]},{code:"de",name:"Germany",cities:["Berlin","Hamburg","Munich","Cologne","Frankfurt","Stuttgart","D\xfcsseldorf","Leipzig","Dortmund","Essen"]},{code:"fr",name:"France",cities:["Paris","Marseille","Lyon","Toulouse","Nice","Nantes","Strasbourg","Montpellier","Bordeaux","Lille"]},{code:"es",name:"Spain",cities:["Madrid","Barcelona","Valencia","Seville","Zaragoza","M\xe1laga","Murcia","Palma","Las Palmas","Bilbao"]},{code:"it",name:"Italy",cities:["Rome","Milan","Naples","Turin","Palermo","Genoa","Bologna","Florence","Bari","Catania"]},{code:"jp",name:"Japan",cities:["Tokyo","Yokohama","Osaka","Nagoya","Sapporo","Fukuoka","Kobe","Kyoto","Kawasaki","Saitama"]},{code:"br",name:"Brazil",cities:["S\xe3o Paulo","Rio de Janeiro","Bras\xedlia","Salvador","Fortaleza","Belo Horizonte","Manaus","Curitiba","Recife","Porto Alegre"]},{code:"mx",name:"Mexico",cities:["Mexico City","Guadalajara","Monterrey","Puebla","Tijuana","Le\xf3n","Ju\xe1rez","Zapopan","M\xe9rida","Canc\xfan"]}];function c(){let[e,t]=(0,a.useState)([]),[s,c]=(0,a.useState)(""),[d,u]=(0,a.useState)(""),[m,h]=(0,a.useState)("us"),[g,x]=(0,a.useState)(3),[p,b]=(0,a.useState)(10),[f,y]=(0,a.useState)(!1),[v,j]=(0,a.useState)(null),[N,w]=(0,a.useState)(null),[C,S]=(0,a.useState)(!1),[k,M]=(0,a.useState)({current:0,total:0}),P=o.find(e=>e.code===m),R=(null==P?void 0:P.cities)||[],F=n.F.scraper.scrapeGoogleMaps.useMutation({onSuccess:t=>{C?(M(e=>({...e,current:e.current+1})),k.current+1>=k.total?(S(!1),w(e=>[...e||[],...t])):A(e[k.current+1])):w(t)},onError:e=>{j(e.message),S(!1)}}),A=e=>{F.mutate({city:e.trim(),category:d.trim(),country:m,minRating:g,minReviews:p,sortByMostVisited:f})},B=e=>{F.mutate({city:e[0].trim(),cities:e.map(e=>e.trim()),category:d.trim(),country:m,minRating:g,minReviews:p,sortByMostVisited:f})},V=e=>{t(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},E=()=>{t([]),c(""),u(""),h("us"),x(3),b(10),y(!1),j(null),w(null),S(!1),M({current:0,total:0})};return(0,i.jsxs)("div",{className:"flex flex-col gap-6",children:[!F.isPending&&!N&&(0,i.jsxs)("form",{onSubmit:t=>(t.preventDefault(),j(null),0===e.length)?void j("At least one city must be selected"):d.trim()?void(w(null),B(e)):void j("Category is required"),className:"flex flex-col gap-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,i.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,i.jsx)("label",{htmlFor:"country",className:"font-medium",children:"Country"}),(0,i.jsx)("select",{id:"country",value:m,onChange:e=>{h(e.target.value),t([])},className:"rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]",children:o.map(e=>(0,i.jsx)("option",{value:e.code,children:e.name},e.code))})]}),(0,i.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,i.jsxs)("label",{htmlFor:"category",className:"font-medium",children:["Category ",(0,i.jsx)("span",{className:"text-red-400",children:"*"})]}),(0,i.jsx)("input",{id:"category",type:"text",value:d,onChange:e=>u(e.target.value),placeholder:"e.g., restaurants, hotels, etc.",className:"rounded-lg bg-white/10 px-4 py-2 text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]",required:!0})]}),(0,i.jsxs)("div",{className:"col-span-1 flex flex-col gap-2 md:col-span-2",children:[(0,i.jsxs)("label",{className:"font-medium",children:["Cities ",(0,i.jsx)("span",{className:"text-red-400",children:"*"}),(0,i.jsx)("span",{className:"ml-2 text-sm text-white/70",children:"(Select cities to scrape or add your own)"})]}),(0,i.jsx)("div",{className:"grid grid-cols-2 gap-2 rounded-lg bg-white/5 p-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5",children:R.map(t=>(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("input",{type:"checkbox",id:"city-".concat(t),checked:e.includes(t),onChange:()=>V(t),className:"h-4 w-4 rounded border-white/30 bg-white/10 text-[hsl(280,100%,70%)] focus:ring-[hsl(280,100%,70%)]"}),(0,i.jsx)("label",{htmlFor:"city-".concat(t),className:"text-sm",children:t})]},t))}),(0,i.jsxs)("div",{className:"mt-2 flex gap-2",children:[(0,i.jsx)("input",{type:"text",value:s,onChange:e=>c(e.target.value),placeholder:"Add a custom city",className:"flex-1 rounded-lg bg-white/10 px-4 py-2 text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]"}),(0,i.jsx)("button",{type:"button",onClick:()=>{s.trim()&&!e.includes(s.trim())&&(t(e=>[...e,s.trim()]),c(""))},disabled:!s.trim(),className:"rounded-lg bg-white/10 px-4 py-2 font-medium transition hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50",children:"Add"})]}),e.length>0&&(0,i.jsxs)("div",{className:"mt-2",children:[(0,i.jsx)("p",{className:"mb-1 text-sm text-white/70",children:"Selected Cities:"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2",children:e.map(e=>(0,i.jsxs)("span",{className:"flex items-center gap-1 rounded-full bg-[hsl(280,100%,70%)]/20 px-3 py-1 text-sm",children:[e,(0,i.jsx)("button",{type:"button",onClick:()=>V(e),className:"ml-1 rounded-full p-1 text-white/70 hover:bg-white/10 hover:text-white",children:"✕"})]},e))})]})]}),(0,i.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,i.jsx)("label",{htmlFor:"minRating",className:"font-medium",children:"Minimum Star Rating"}),(0,i.jsx)("input",{id:"minRating",type:"number",min:"1",max:"5",step:"0.1",value:g,onChange:e=>x(parseFloat(e.target.value)),className:"rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]"})]}),(0,i.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,i.jsx)("label",{htmlFor:"minReviews",className:"font-medium",children:"Minimum Number of Reviews"}),(0,i.jsx)("input",{id:"minReviews",type:"number",min:"0",value:p,onChange:e=>b(parseInt(e.target.value)),className:"rounded-lg bg-white/10 px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[hsl(280,100%,70%)]"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("input",{id:"sortByMostVisited",type:"checkbox",checked:f,onChange:e=>y(e.target.checked),className:"h-5 w-5 rounded border-white/30 bg-white/10 text-[hsl(280,100%,70%)] focus:ring-[hsl(280,100%,70%)]"}),(0,i.jsx)("label",{htmlFor:"sortByMostVisited",className:"font-medium",children:"Sort by Most Visited"})]})]}),v&&(0,i.jsx)("div",{className:"rounded-lg bg-red-500/20 p-4 text-red-200",children:(0,i.jsx)("p",{children:v})}),(0,i.jsxs)("div",{className:"mt-4 flex gap-4",children:[(0,i.jsx)("button",{type:"submit",className:"flex-1 rounded-lg bg-[hsl(280,100%,70%)] px-6 py-3 font-semibold text-white transition hover:bg-[hsl(280,100%,60%)]",children:"Start Scraping"}),(0,i.jsx)("button",{type:"button",onClick:E,className:"rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20",children:"Reset"})]})]}),F.isPending&&(0,i.jsx)(l,{isProcessingMultiple:C,progress:C?k:void 0}),N&&(0,i.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,i.jsx)(r,{data:N}),(0,i.jsx)("div",{className:"flex gap-4",children:(0,i.jsx)("button",{onClick:E,className:"rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20",children:"New Search"})})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[874,616,308,441,684,358],()=>t(9092)),_N_E=e.O()}]);