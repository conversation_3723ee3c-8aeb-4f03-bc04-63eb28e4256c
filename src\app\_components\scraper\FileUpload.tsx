import { useState, useRef, type ChangeEvent, type DragEvent } from "react";
import { LoadingIndicator } from "./LoadingIndicator";
import { ResultsTable } from "./ResultsTable";
import { api } from "~/trpc/react";

type ScrapedData = {
  url: string;
  name: string;
  address: string;
  phone: string;
  email: string;
};

export function FileUpload() {
  const [file, setFile] = useState<File | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<ScrapedData[] | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const uploadMutation = api.scraper.uploadCsv.useMutation({
    onSuccess: (data) => {
      setResults(data);
    },
    onError: (error) => {
      setError(error.message);
    },
  });

  const validateAndSetFile = (selectedFile: File | null) => {
    setError(null);
    if (!selectedFile) return;

    if (!selectedFile.name.endsWith(".csv")) {
      setError("Please upload a CSV file");
      return;
    }

    setFile(selectedFile);
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] ?? null;
    validateAndSetFile(selectedFile);
  };

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    const droppedFile = e.dataTransfer.files?.[0] ?? null;
    validateAndSetFile(droppedFile);
  };

  const handleSubmit = () => {
    if (!file) {
      setError("Please select a file first");
      return;
    }

    const reader = new FileReader();
    reader.onload = (event) => {
      const result = event.target?.result as string;
      if (!result) {
        setError("Failed to read file");
        return;
      }

      const base64String = result.split(",")[1];
      if (!base64String) {
        setError("Invalid file encoding");
        return;
      }

      uploadMutation.mutate({
        fileName: file.name,
        fileContent: base64String,
      });
    };

    reader.onerror = () => {
      setError("Failed to read file");
    };

    reader.readAsDataURL(file);
  };

  const handleReset = () => {
    setFile(null);
    setError(null);
    setResults(null);
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  const handleDownloadCsv = () => {
    if (!results) return;

    // Create CSV content
    const headers = ["URL", "Name", "Address", "Phone", "Email"];
    const rows = results.map(item => [
      item.url,
      item.name,
      item.address,
      item.phone,
      item.email
    ]);

    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
    ].join('\n');

    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'scraped_website_data.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="flex flex-col gap-6">
      {!results && (
        <>
          <div
            className={`flex min-h-40 cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors ${
              isDragging
                ? "border-[hsl(280,100%,70%)] bg-[hsl(280,100%,70%)]/10"
                : "border-white/30 hover:border-white/50"
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              type="file"
              accept=".csv"
              className="hidden"
              onChange={handleFileChange}
              ref={fileInputRef}
            />
            <svg
              className="mb-4 h-10 w-10 text-white/70"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
              ></path>
            </svg>
            <p className="mb-2 text-center text-lg font-medium">
              {file ? file.name : "Click to select or drag and drop a CSV file"}
            </p>
            {file && (
              <p className="text-sm text-white/70">
                {(file.size / 1024).toFixed(2)} KB
              </p>
            )}
          </div>

          {error && (
            <div className="rounded-lg bg-red-500/20 p-4 text-red-200">
              <p>{error}</p>
            </div>
          )}

          <div className="flex gap-4">
            <button
              onClick={handleSubmit}
              disabled={!file || uploadMutation.isPending}
              className="flex-1 rounded-lg bg-[hsl(280,100%,70%)] px-6 py-3 font-semibold text-white transition hover:bg-[hsl(280,100%,60%)] disabled:cursor-not-allowed disabled:opacity-50"
            >
              {uploadMutation.isPending ? "Processing..." : "Submit"}
            </button>
            {file && (
              <button
                onClick={handleReset}
                className="rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20"
              >
                Reset
              </button>
            )}
          </div>
        </>
      )}

      {uploadMutation.isPending && <LoadingIndicator />}

      {results && (
        <div className="flex flex-col gap-6">
          <ResultsTable data={results} />
          <div className="flex gap-4">
            <button
              onClick={handleDownloadCsv}
              className="flex-1 rounded-lg bg-[hsl(280,100%,70%)] px-6 py-3 font-semibold text-white transition hover:bg-[hsl(280,100%,60%)]"
            >
              Download CSV
            </button>
            <button
              onClick={handleReset}
              className="rounded-lg bg-white/10 px-6 py-3 font-semibold transition hover:bg-white/20"
            >
              Upload Another File
            </button>
          </div>
        </div>
      )}
    </div>
  );
}


