(()=>{var e={};e.id=124,e.ids=[124],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19370:(e,s,r)=>{Promise.resolve().then(r.bind(r,87197))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30072:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var a=r(37413),t=r(87197);function l(){return(0,a.jsx)(t.default,{})}},33873:e=>{"use strict";e.exports=require("path")},45149:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>o,pages:()=>x,routeModule:()=>h,tree:()=>c});var a=r(65239),t=r(48088),l=r(88170),n=r.n(l),i=r(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(s,d);let c={children:["",{children:["data-scraper",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,30072)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\data-scraper\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,x=["C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\data-scraper\\page.tsx"],o={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/data-scraper/page",pathname:"/data-scraper",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},76246:(e,s,r)=>{"use strict";r.d(s,{default:()=>f});var a=r(60687),t=r(43210),l=r(29523),n=r(44493),i=r(80013),d=r(18116),c=r(54987),x=r(15079),o=r(21342),h=r(21134),m=r(363),g=r(12941),u=r(97992),p=r(17313);let b=(0,r(62688).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);var j=r(64398),v=r(99270),y=r(85814),N=r.n(y);function f(){let[e,s]=(0,t.useState)(!1),[r,y]=(0,t.useState)([500,5e3]),[f,k]=(0,t.useState)([3.5]);return(0,a.jsxs)("div",{className:`min-h-screen transition-colors duration-300 ${e?"dark":""}`,children:[(0,a.jsx)("nav",{className:"bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"DataScraper"})})}),(0,a.jsx)("div",{className:"hidden md:block",children:(0,a.jsxs)("div",{className:"ml-10 flex items-baseline space-x-8",children:[(0,a.jsx)(N(),{href:"#",className:"text-gray-900 dark:text-white hover:text-green-600 dark:hover:text-green-400 px-3 py-2 text-sm font-medium transition-colors",children:"Home"}),(0,a.jsx)(N(),{href:"#",className:"text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 px-3 py-2 text-sm font-medium transition-colors",children:"Categories"}),(0,a.jsx)(N(),{href:"#",className:"text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 px-3 py-2 text-sm font-medium transition-colors",children:"Pricing"}),(0,a.jsx)(N(),{href:"#",className:"text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 px-3 py-2 text-sm font-medium transition-colors",children:"How It Works"}),(0,a.jsx)(N(),{href:"#",className:"text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 px-3 py-2 text-sm font-medium transition-colors",children:"Login"}),(0,a.jsx)(l.$,{className:"bg-green-600 hover:bg-green-700 text-white",children:"Sign Up"})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 text-gray-600 dark:text-gray-300"}),(0,a.jsx)(c.d,{checked:e,onCheckedChange:()=>{s(!e),document.documentElement.classList.toggle("dark")},className:"data-[state=checked]:bg-green-600"}),(0,a.jsx)(m.A,{className:"h-4 w-4 text-gray-600 dark:text-gray-300"})]}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsxs)(o.rI,{children:[(0,a.jsx)(o.ty,{asChild:!0,children:(0,a.jsx)(l.$,{variant:"ghost",size:"icon",children:(0,a.jsx)(g.A,{className:"h-5 w-5"})})}),(0,a.jsxs)(o.SQ,{align:"end",className:"w-48",children:[(0,a.jsx)(o._2,{children:"Home"}),(0,a.jsx)(o._2,{children:"Categories"}),(0,a.jsx)(o._2,{children:"Pricing"}),(0,a.jsx)(o._2,{children:"How It Works"}),(0,a.jsx)(o._2,{children:"Login"}),(0,a.jsx)(o._2,{children:"Sign Up"})]})]})})]})]})})}),(0,a.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-cover bg-center bg-no-repeat",style:{backgroundImage:"url('/placeholder.svg?height=1080&width=1920')",filter:"blur(8px)",transform:"scale(1.1)"}}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black/40 dark:bg-black/60"}),(0,a.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("h1",{className:"text-5xl md:text-7xl font-bold text-white mb-8 leading-tight",children:["Find the Data You Need",(0,a.jsx)("span",{className:"block text-green-400",children:"Instantly"})]}),(0,a.jsx)("p",{className:"text-xl md:text-2xl text-gray-200 mb-12 max-w-3xl mx-auto",children:"Powerful web scraping platform that helps you extract valuable business data with precision and speed."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12",children:[(0,a.jsx)(n.Zp,{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300",children:(0,a.jsxs)(n.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,a.jsx)(i.J,{className:"text-sm font-semibold text-gray-700 dark:text-gray-200",children:"Location"})]}),(0,a.jsxs)(x.l6,{children:[(0,a.jsx)(x.bq,{className:"w-full",children:(0,a.jsx)(x.yv,{placeholder:"Select City/State"})}),(0,a.jsxs)(x.gC,{children:[(0,a.jsx)(x.eb,{value:"mumbai",children:"Mumbai, Maharashtra"}),(0,a.jsx)(x.eb,{value:"delhi",children:"Delhi, NCR"}),(0,a.jsx)(x.eb,{value:"bangalore",children:"Bangalore, Karnataka"}),(0,a.jsx)(x.eb,{value:"chennai",children:"Chennai, Tamil Nadu"}),(0,a.jsx)(x.eb,{value:"kolkata",children:"Kolkata, West Bengal"})]})]})]})}),(0,a.jsx)(n.Zp,{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300",children:(0,a.jsxs)(n.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,a.jsx)(i.J,{className:"text-sm font-semibold text-gray-700 dark:text-gray-200",children:"Category"})]}),(0,a.jsxs)(x.l6,{children:[(0,a.jsx)(x.bq,{className:"w-full",children:(0,a.jsx)(x.yv,{placeholder:"Business Type"})}),(0,a.jsxs)(x.gC,{children:[(0,a.jsx)(x.eb,{value:"restaurant",children:"Restaurant"}),(0,a.jsx)(x.eb,{value:"gym",children:"Gym & Fitness"}),(0,a.jsx)(x.eb,{value:"retail",children:"Retail Store"}),(0,a.jsx)(x.eb,{value:"healthcare",children:"Healthcare"}),(0,a.jsx)(x.eb,{value:"education",children:"Education"})]})]})]})}),(0,a.jsx)(n.Zp,{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300",children:(0,a.jsxs)(n.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(b,{className:"h-5 w-5 text-green-600 mr-2"}),(0,a.jsx)(i.J,{className:"text-sm font-semibold text-gray-700 dark:text-gray-200",children:"Price Range"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(d.A,{value:r,onValueChange:y,max:5e3,min:500,step:100,className:"w-full"}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-600 dark:text-gray-400",children:[(0,a.jsxs)("span",{children:["₹",r[0]]}),(0,a.jsxs)("span",{children:["₹",r[1]]})]})]})]})}),(0,a.jsx)(n.Zp,{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300",children:(0,a.jsxs)(n.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,a.jsx)(i.J,{className:"text-sm font-semibold text-gray-700 dark:text-gray-200",children:"Rating"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(d.A,{value:f,onValueChange:k,max:5,min:1,step:.5,className:"w-full"}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-600 dark:text-gray-400",children:[(0,a.jsxs)("span",{children:[f[0]," stars"]}),(0,a.jsx)("span",{children:"5 stars"})]})]})]})})]}),(0,a.jsxs)(l.$,{size:"lg",className:"bg-green-600 hover:bg-green-700 text-white px-12 py-6 text-xl font-semibold rounded-xl shadow-2xl hover:shadow-green-500/25 transition-all duration-300 transform hover:scale-105",children:[(0,a.jsx)(v.A,{className:"mr-3 h-6 w-6"}),"Start Scraping"]}),(0,a.jsx)("p",{className:"text-gray-300 mt-6 text-sm",children:"No credit card required • Free trial available • 99.9% uptime guarantee"})]})})]}),(0,a.jsx)("section",{className:"py-20 bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsx)("h2",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Why Choose Our Platform?"}),(0,a.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"Advanced web scraping technology that delivers accurate, real-time data for your business needs."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,a.jsx)(n.Zp,{className:"bg-white dark:bg-gray-800 border-0 shadow-lg hover:shadow-xl transition-all duration-300",children:(0,a.jsxs)(n.Wu,{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)(v.A,{className:"h-8 w-8 text-green-600"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"Fast & Accurate"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Extract data with 99.9% accuracy using our advanced algorithms and machine learning technology."})]})}),(0,a.jsx)(n.Zp,{className:"bg-white dark:bg-gray-800 border-0 shadow-lg hover:shadow-xl transition-all duration-300",children:(0,a.jsxs)(n.Wu,{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)(p.A,{className:"h-8 w-8 text-blue-600"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"Scalable Solution"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"From small businesses to enterprise-level operations, our platform scales with your needs."})]})}),(0,a.jsx)(n.Zp,{className:"bg-white dark:bg-gray-800 border-0 shadow-lg hover:shadow-xl transition-all duration-300",children:(0,a.jsxs)(n.Wu,{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)(j.A,{className:"h-8 w-8 text-purple-600"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"24/7 Support"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Get round-the-clock support from our expert team to ensure your data extraction never stops."})]})})]})]})})]})}},82418:(e,s,r)=>{Promise.resolve().then(r.bind(r,76246))},87197:(e,s,r)=>{"use strict";r.d(s,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\abcdwer\\\\scraper_king\\\\src\\\\components\\\\landing\\\\DataScraperLanding.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\components\\landing\\DataScraperLanding.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[719,338,814,274,649],()=>r(45149));module.exports=a})();