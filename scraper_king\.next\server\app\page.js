(()=>{var e={};e.id=974,e.ids=[974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7457:(e,s,t)=>{"use strict";t.d(s,{LatestPost:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call LatestPost() from the server but LatestPost is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\_components\\post.tsx","LatestPost")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(37413),n=t(4536),i=t.n(n),o=t(7457),a=t(60250);async function l(){let e=await a.F.post.hello({text:"from tRPC"});return a.F.post.getLatest.prefetch(),(0,r.jsx)(a.d,{children:(0,r.jsx)("main",{className:"flex min-h-screen flex-col items-center justify-center bg-gradient-to-b from-[#2e026d] to-[#15162c] text-white",children:(0,r.jsxs)("div",{className:"container flex flex-col items-center justify-center gap-12 px-4 py-16",children:[(0,r.jsxs)("h1",{className:"text-5xl font-extrabold tracking-tight sm:text-[5rem]",children:["Scraper ",(0,r.jsx)("span",{className:"text-[hsl(280,100%,70%)]",children:"King"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 md:gap-8",children:[(0,r.jsxs)(i(),{className:"flex max-w-xs flex-col gap-4 rounded-xl bg-white/10 p-4 hover:bg-white/20",href:"/scraper",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold",children:"CSV Scraper →"}),(0,r.jsx)("div",{className:"text-lg",children:"Upload a CSV file with URLs to extract business address, contact info, and email."})]}),(0,r.jsxs)(i(),{className:"flex max-w-xs flex-col gap-4 rounded-xl bg-white/10 p-4 hover:bg-white/20",href:"/business-scraper",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold",children:"Business Scraper →"}),(0,r.jsx)("div",{className:"text-lg",children:"Scrape business data including address, contact info, and email."})]}),(0,r.jsxs)(i(),{className:"flex max-w-xs flex-col gap-4 rounded-xl bg-white/10 p-4 hover:bg-white/20",href:"/data-scraper",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold",children:"Data Scraper →"}),(0,r.jsx)("div",{className:"text-lg",children:"Professional data scraping platform with advanced filtering and analytics."})]}),(0,r.jsxs)(i(),{className:"flex max-w-xs flex-col gap-4 rounded-xl bg-white/10 p-4 hover:bg-white/20",href:"/wine-scraper",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold",children:"Wine Scraper →"}),(0,r.jsx)("div",{className:"text-lg",children:"Extract comprehensive wine data, ratings, and pricing information."})]}),(0,r.jsxs)(i(),{className:"flex max-w-xs flex-col gap-4 rounded-xl bg-white/10 p-4 hover:bg-white/20",href:"https://github.com/your-username/scraper-king",target:"_blank",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold",children:"About →"}),(0,r.jsx)("div",{className:"text-lg",children:"Learn more about Scraper King, how it works, and how to contribute to the project."})]})]}),(0,r.jsx)("div",{className:"flex flex-col items-center gap-2",children:(0,r.jsx)("p",{className:"text-2xl text-white",children:e?e.greeting:"Loading tRPC query..."})}),(0,r.jsx)(o.LatestPost,{})]})})})}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},46465:(e,s,t)=>{Promise.resolve().then(t.bind(t,20665)),Promise.resolve().then(t.bind(t,55465)),Promise.resolve().then(t.bind(t,51503)),Promise.resolve().then(t.bind(t,82246)),Promise.resolve().then(t.bind(t,58920)),Promise.resolve().then(t.bind(t,93250)),Promise.resolve().then(t.bind(t,35917)),Promise.resolve().then(t.bind(t,74052)),Promise.resolve().then(t.bind(t,64480)),Promise.resolve().then(t.bind(t,25080)),Promise.resolve().then(t.bind(t,25480)),Promise.resolve().then(t.bind(t,10240)),Promise.resolve().then(t.bind(t,7512)),Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.bind(t,7457))},51637:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=t(65239),n=t(48088),i=t(88170),o=t.n(i),a=t(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(s,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,21204)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\abcdwer\\scraper_king\\src\\app\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79646:e=>{"use strict";e.exports=require("child_process")},82913:(e,s,t)=>{Promise.resolve().then(t.bind(t,39295)),Promise.resolve().then(t.bind(t,24903)),Promise.resolve().then(t.bind(t,8693)),Promise.resolve().then(t.bind(t,18228)),Promise.resolve().then(t.bind(t,87394)),Promise.resolve().then(t.bind(t,19100)),Promise.resolve().then(t.bind(t,54050)),Promise.resolve().then(t.bind(t,97322)),Promise.resolve().then(t.bind(t,93425)),Promise.resolve().then(t.bind(t,12030)),Promise.resolve().then(t.bind(t,46674)),Promise.resolve().then(t.bind(t,35522)),Promise.resolve().then(t.bind(t,45806)),Promise.resolve().then(t.t.bind(t,85814,23)),Promise.resolve().then(t.bind(t,89407))},89407:(e,s,t)=>{"use strict";t.d(s,{LatestPost:()=>o});var r=t(60687),n=t(43210),i=t(79755);function o(){let[e]=i.F.post.getLatest.useSuspenseQuery(),s=i.F.useUtils(),[t,o]=(0,n.useState)(""),a=i.F.post.create.useMutation({onSuccess:async()=>{await s.post.invalidate(),o("")}});return(0,r.jsxs)("div",{className:"w-full max-w-xs",children:[e?(0,r.jsxs)("p",{className:"truncate",children:["Your most recent post: ",e.name]}):(0,r.jsx)("p",{children:"You have no posts yet."}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),a.mutate({name:t})},className:"flex flex-col gap-2",children:[(0,r.jsx)("input",{type:"text",placeholder:"Title",value:t,onChange:e=>o(e.target.value),className:"w-full rounded-full bg-white/10 px-4 py-2 text-white"}),(0,r.jsx)("button",{type:"submit",className:"rounded-full bg-white/10 px-10 py-3 font-semibold transition hover:bg-white/20",disabled:a.isPending,children:a.isPending?"Submitting...":"Submit"})]})]})}},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var s=require("../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[719,338,814,788,923,240],()=>t(51637));module.exports=r})();