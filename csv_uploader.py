import csv
import requests
import asyncio
import aiohttp
import time
import re
import os
from bs4 import BeautifulSoup
from urllib.parse import urlparse, parse_qs, unquote, quote
from selenium import webdriver

# --- Global HTTP Headers ---
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
}
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException, StaleElementReferenceException

# --- Social Platforms List ---
SOCIAL_PLATFORMS = [
    "facebook.com", "twitter.com", "linkedin.com", "instagram.com", "youtube.com",
    "pinterest.com", "t.me", "telegram.me", "wa.me", "whatsapp.com", "snapchat.com",
    "reddit.com", "medium.com", "tumblr.com", "github.com", "slack.com", "discord.gg",
    "discord.com", "tiktok.com", "weibo.com", "vk.com", "line.me"
]

# --- Global Constants ---
PAGE_LOAD_TIMEOUT = 20  # seconds, adjust as needed
ELEMENT_WAIT_TIMEOUT = 10  # seconds, adjust as needed
REQUEST_TIMEOUT = 15  # seconds, adjust as needed for requests.get

# --- File Paths ---
CSV_INPUT_FILE = "input.csv"  # Change this to your input CSV filename
OUTPUT_FILE = "output.csv"    # Change this to your desired output CSV filename

def initialize_driver():
    """Initialize the WebDriver with enhanced settings for reliability."""
    try:
        options = webdriver.FirefoxOptions()

        # Improve performance and reliability
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-extensions")
        options.add_argument("--dns-prefetch-disable")
        options.set_preference("dom.ipc.processCount", 8)  # Increase process count
        options.set_preference("browser.cache.disk.enable", False)
        options.set_preference("browser.cache.memory.enable", False)
        options.set_preference("browser.cache.offline.enable", False)
        options.set_preference("network.http.pipelining", True)
        options.set_preference("network.http.proxy.pipelining", True)

        # Uncomment to use headless mode in production
        # options.add_argument("--headless")
        # options.add_argument("--window-size=1920,1080") # Often needed for headless

        driver = webdriver.Firefox(options=options)

        # Set page load timeout using the global constant
        driver.set_page_load_timeout(PAGE_LOAD_TIMEOUT)

        print("✅ WebDriver initialized with enhanced settings.")
        return driver
    except Exception as e:
        print(f"❌ Error initializing WebDriver: {e}")
        print("Please ensure your WebDriver (e.g., geckodriver for Firefox) is installed and in your system's PATH.")
        return None

# --- Helper Functions ---
def clean_url(url):
    """
    Cleans and normalizes URLs, including removing tracking parameters
    and handling redirects.
    """
    if not url or not isinstance(url, str):
        return None

    url = url.strip()
    if not url:
        return None

    # Handle DuckDuckGo redirect URLs
    if "uddg=" in url and "duckduckgo.com" in urlparse(url).netloc:
        parsed = urlparse(url)
        query = parse_qs(parsed.query)
        real_url = query.get("uddg", [None])[0]
        if real_url:
            real_url = unquote(real_url)
            if not urlparse(real_url).scheme:
                real_url = "https://" + real_url.lstrip('/')
            return real_url.strip()

    # Handle Google redirect URLs
    elif "url=" in url and "google" in urlparse(url).netloc:
        parsed = urlparse(url)
        query = parse_qs(parsed.query)
        real_url = query.get("url", [None])[0] or query.get("q", [None])[0]
        if real_url:
            real_url = unquote(real_url)
            if not urlparse(real_url).scheme:
                real_url = "https://" + real_url.lstrip('/')
            return real_url.strip()

    # Handle protocol-relative URLs (starting with //)
    elif url.startswith("//"):
        return "https:" + url

    # Add scheme if missing
    elif not urlparse(url).scheme:
        return "https://" + url.lstrip('/')

    return url

def safe_find_element(driver_instance, by, value, timeout=None):
    """Safely finds a single element with timeout."""
    if timeout is None:
        timeout = ELEMENT_WAIT_TIMEOUT
    try:
        wait = WebDriverWait(driver_instance, timeout, poll_frequency=0.5)
        return wait.until(EC.presence_of_element_located((by, value)))
    except (NoSuchElementException, TimeoutException, StaleElementReferenceException):
        return None

def safe_find_elements(driver_instance, by, value, timeout=None):
    """Safely finds multiple elements with timeout."""
    if timeout is None:
        timeout = ELEMENT_WAIT_TIMEOUT
    try:
        wait = WebDriverWait(driver_instance, timeout, poll_frequency=0.5)
        return wait.until(EC.presence_of_all_elements_located((by, value)))
    except (NoSuchElementException, TimeoutException, StaleElementReferenceException):
        return []

# --- New Phone Number Extraction Function ---
def extract_phone(text):
    """
    Extracts a phone number from text, prioritizing Indian numbers,
    and falls back to a generic pattern. Returns cleaned digits or "N/A".
    """
    if not isinstance(text, str): return "N/A"

    # Prefer Indian phone numbers (covers +91 or not, 6-9 followed by 9 digits)
    pattern_india = re.compile(r'(?:\+91[\-\s]?)?\b[6-9]\d{9}\b')
    match_india = pattern_india.search(text)
    if match_india:
        phone = re.sub(r'\D', '', match_india.group(0))
        if 10 <= len(phone) <= 12: # Allow for +91 or just 10 digits
             # Remove leading 91 if it's a 12-digit number starting with 91
            if len(phone) == 12 and phone.startswith("91"):
                phone = phone[2:]
            if len(phone) == 11 and phone.startswith("0"): # e.g. 080...
                phone = phone[1:]
            if len(phone) == 10: # Standard 10-digit Indian mobile
                return phone
            # Re-evaluate if other conditions for 11 or 12 make sense for India
            # For now, stricter on 10-digit after cleaning for Indian pattern.
            # If it was +91xxxxxxxxxx, it becomes 91xxxxxxxxxx (12 digits), then xxxxxxxxxx (10 digits)
            # If it was 0xxxxxxxxxx, it becomes xxxxxxxxxx (10 digits)
            # If it was just xxxxxxxxxx, it remains 10 digits
            # If it was 91 xxxxxxxxxx, also becomes 10 digits.
            # So the final check for 10 digits is good.
            # if 10 <= len(phone) <= 12 : return phone # Old logic - keep for now if specific cases needed

    # Fallback to generic international phone pattern (improved)
    # This pattern tries to capture a variety of formats, including optional country codes, extensions.
    pattern_generic = re.compile(
        r'(?:\+?\d{1,3}[-.\s]?)?'  # Optional country code +1, +44, etc.
        r'(?:\(?\d{2,5}\)?[-.\s]?)?'  # Area code (optional parens)
        r'\d{2,5}[-.\s]?\d{2,5}[-.\s]?\d{0,5}' # Main number parts
        r'(?:\s*(?:ext|x|Ext|EXT|X)\.?\s*\d{1,5})?' # Optional extension
    )

    # Search for all potential matches and pick the longest, most plausible one
    best_phone = "N/A"
    longest_cleaned_len = 0

    for match in pattern_generic.finditer(text):
        potential_phone_text = match.group(0)
        # Further clean up common issues like "Call us:" before the number
        potential_phone_text = re.sub(r'^\D+', '', potential_phone_text) # Remove leading non-digits

        cleaned_phone = re.sub(r'\D', '', potential_phone_text) # Keep only digits

        # Plausibility check for cleaned number length (e.g., 7 to 15 digits)
        if 7 <= len(cleaned_phone) <= 15:
            if len(cleaned_phone) > longest_cleaned_len:
                # Prioritize if it contains more digits than previous best
                # (Avoids matching short fragments if longer one exists)
                best_phone = cleaned_phone
                longest_cleaned_len = len(cleaned_phone)
            elif best_phone == "N/A": # First valid match
                best_phone = cleaned_phone
                longest_cleaned_len = len(cleaned_phone)

    if best_phone != "N/A":
        return best_phone

    return "N/A"


def extract_with_requests(url):
    """
    Try to extract basic information using requests/BeautifulSoup as a fallback
    when Selenium times out. This is faster but less comprehensive.
    """
    print(f"Attempting fallback extraction for {url} using requests/BeautifulSoup...")
    try:
        response = requests.get(url, headers=HEADERS, timeout=REQUEST_TIMEOUT, allow_redirects=True)
        response.raise_for_status() # Will raise an HTTPError for bad responses (4XX or 5XX)

        soup = BeautifulSoup(response.text, 'html.parser')

        page_title = soup.title.string.strip() if soup.title and soup.title.string else "N/A"

        meta_description = "N/A"
        meta_tag = soup.find('meta', attrs={'name': re.compile(r'^description$', re.I)})
        from bs4 import Tag
        if isinstance(meta_tag, Tag) and meta_tag.get('content'):
            content_val = meta_tag.get('content', '')
            if isinstance(content_val, str):
                meta_description = content_val.strip()
            elif isinstance(content_val, list) and content_val and isinstance(content_val[0], str):
                meta_description = content_val[0].strip()
            else:
                meta_description = "N/A"

        address = "N/A"
        address_tag = soup.find('address')
        if address_tag and address_tag.get_text(strip=True):
            address = address_tag.get_text(" ", strip=True)
        else:
            address_candidates = soup.find_all(['p', 'div', 'span'],
                                               string=re.compile(r'\d{1,5}\s+[\w\s\.]+,?\s*[\w\s\.]+', re.I))
            for candidate in address_candidates:
                text = candidate.get_text(" ", strip=True)
                if len(text) > 15 and (',' in text or any(word in text.lower() for word in ['street', 'road', 'lane', 'marg', 'nagar', 'avenue', 'floor', 'building'])):
                    address = text
                    break
            if address == "N/A" and address_candidates: # Fallback to first candidate if specific pattern fails
                address = address_candidates[0].get_text(" ", strip=True)

        if address != "N/A" and len(address) > 300:
            address = address[:297] + "..."

        phone = "N/A"
        tel_links = soup.find_all('a', href=re.compile(r'^tel:'))
        if tel_links:
            first_tel = tel_links[0]
            from bs4 import Tag
            href = first_tel.attrs.get('href', '') if isinstance(first_tel, Tag) else ''
            if isinstance(href, str) and href.startswith('tel:'):
                phone = extract_phone(href.replace('tel:', ''))

        if phone == "N/A":
            page_text_for_contacts = soup.get_text(" ") # Get all text with spaces
            phone = extract_phone(page_text_for_contacts)

        email = "N/A"
        email_links = soup.find_all('a', href=re.compile(r'^mailto:'))
        if email_links:
            from bs4 import Tag
            first_email = email_links[0]
            href = first_email.get('href', '') if isinstance(first_email, Tag) else ''
            if isinstance(href, str) and href.startswith('mailto:'):
                email = href.replace('mailto:', '').split('?')[0] # Remove subject, etc.

        if email == "N/A":
            page_text = soup.get_text()
            email_pattern = re.compile(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}')
            email_match = email_pattern.search(page_text)
            if email_match:
                email = email_match.group(0)

        social_links_found = set()
        import bs4
        for link_tag in soup.find_all('a', href=True):
            if isinstance(link_tag, bs4.element.Tag):
                href = link_tag.get('href')
                if href:
                    for platform_domain in SOCIAL_PLATFORMS: # Use global list
                        if isinstance(href, str) and platform_domain in href.lower():
                            social_links_found.add(href)
                            break
        social_links_str = '; '.join(sorted(list(social_links_found))) if social_links_found else "N/A"

        contact_info = "N/A"
        contact_selectors = [
            {'name': ['section', 'div', 'footer'], 'attrs': {'class': re.compile(r'contact|footer|col-contact', re.I)}},
            {'name': ['section', 'div', 'footer'], 'attrs': {'id': re.compile(r'contact|footer', re.I)}},
        ]
        for sel in contact_selectors:
            contact_section = soup.find(sel['name'], sel['attrs'])
            if contact_section:
                contact_info = contact_section.get_text(" ", strip=True)
                break

        if contact_info == "N/A": # Fallback if specific sections not found
            # Attempt to find elements containing contact-related keywords and get their parent
            keyword_elements = soup.find_all(string=re.compile(r'Contact Us|Get In Touch|Location|Address|Phone:|Email:', re.I))
            if keyword_elements:
                # Get text from a reasonable parent, e.g., parent of parent
                parent_texts = set()
                for kw_el in keyword_elements:
                    current_parent = kw_el.find_parent()
                    if current_parent:
                        parent_texts.add(current_parent.get_text(" ", strip=True))
                if parent_texts:
                     # Join distinct parent texts; might be too broad, but better than nothing
                    contact_info = " | ".join(parent_texts)


        if len(contact_info) > 500:
            contact_info = contact_info[:497] + "..."

        return {
            "source_url": url, "page_title": page_title, "meta_description": meta_description,
            "contact_info": contact_info, "address": address, "phone": phone,
            "email": email, "social_links": social_links_str,
            "extraction_method": "requests/BeautifulSoup (fallback)"
        }
    except requests.exceptions.RequestException as e:
        print(f"❌ Fallback requests error for {url}: {e}")
    except Exception as e:
        print(f"❌ Fallback extraction failed for {url}: {e}")
    return None

def scrape_website_details(driver, url):
    """
    Navigates to a website URL and extracts details with intelligent fallbacks.
    """
    print(f"🚀 Attempting to scrape: {url}")

    try:
        driver.get(url)
        print(f"✅ Page loaded for {url}")
    except TimeoutException:
        print(f"⏳ Initial page load timed out for {url}. Trying fallback extraction...")
        fallback_data = extract_with_requests(url)
        if fallback_data:
            print(f"👍 Fallback extraction succeeded for {url}")
            return fallback_data
        else:
            return {
                "source_url": url,
                "page_title": "Timeout Error (Initial Load)",
                "meta_description": "N/A",
                "contact_info": "N/A",
                "address": "N/A",
                "phone": "N/A",
                "email": "N/A",
                "social_links": "N/A",
                "extraction_method": "Failed (Timeout)"
            }
    except Exception as e:
        print(f"❌ Error loading page {url}: {e}")
        # Attempt fallback even on other load errors
        fallback_data = extract_with_requests(url)
        if fallback_data:
            print(f"👍 Fallback extraction succeeded for {url} after load error.")
            return fallback_data
        return {
            "source_url": url,
            "page_title": f"Page Load Error: {str(e)[:50]}...",
            "meta_description": "N/A",
            "contact_info": "N/A",
            "address": "N/A",
            "phone": "N/A",
            "email": "N/A",
            "social_links": "N/A",
            "extraction_method": "Failed (Load Error)"
        }

    try:
        # Wait for page to be interactive or complete
        try:
            WebDriverWait(driver, PAGE_LOAD_TIMEOUT // 2).until(
                lambda d: d.execute_script('return document.readyState === "complete" || document.readyState === "interactive"')
            )
            time.sleep(1) # Small delay to ensure JS has run after readyState change
        except TimeoutException:
            print(f"⚠️ Page readyState check timed out for {url}, continuing extraction attempt...")
        except Exception as e:
            print(f"⚠️ Error during readyState check for {url}: {e}, continuing extraction attempt...")

        page_title = driver.title if driver.title else "N/A"

        meta_description = "N/A"
        try:
            meta_desc_element = safe_find_element(driver, By.XPATH, '//meta[@name="description" or @name="Description" or @property="og:description"]', timeout=2)
            if meta_desc_element:
                meta_description = meta_desc_element.get_attribute('content') or "N/A"
        except Exception: # Catch any error during meta description extraction
            pass

        phone = "N/A"
        tel_elements = safe_find_elements(driver, By.XPATH, '//a[contains(@href, "tel:")]', 3)
        for element in tel_elements:
            if element:
                href = element.get_attribute('href')
                if href and href.startswith('tel:'):
                    extracted_phone = extract_phone(href.replace('tel:', ''))
                    if extracted_phone != "N/A":
                        phone = extracted_phone
                        break

        if phone == "N/A":
            # Broader search in body text if no tel: links
            try:
                body_text = safe_find_element(driver, By.TAG_NAME, 'body', 3)
                if body_text:
                    phone = extract_phone(body_text.text)
            except Exception:
                pass # Silently ignore if body text cannot be fetched or processed

        address = "N/A"
        address_selectors_texts = [
            (By.XPATH, '//address'), # Prioritize <address> tag
            (By.XPATH, '//*[contains(translate(text(), "ADDRESS", "address"), "address")]/following-sibling::*[1] | //*[contains(translate(text(), "LOCATION", "location"), "location")]/following-sibling::*[1]'),
            (By.CSS_SELECTOR, '.address, .location, .footer-address, [class*="Address"], [id*="Address"]'),
            (By.XPATH, '//*[contains(@class, "address") or contains(@class, "location") or contains(@id, "address") or contains(@id, "location")]')
        ]
        found_address_text = ""
        for by_type, selector_val in address_selectors_texts:
            elements = safe_find_elements(driver, by_type, selector_val, 2)
            for element in elements:
                if element and element.text:
                    text = element.text.strip()
                    # Basic plausibility for address like content
                    if len(text) > 10 and (re.search(r'\d', text) or any(kw in text.lower() for kw in ["street", "road", "city", "nagar", "marg"])):
                        found_address_text = text
                        if len(found_address_text) > 300:
                            found_address_text = found_address_text[:297] + "..."
                        break # Found a plausible address
            if found_address_text:
                address = found_address_text
                break

        email = "N/A"
        email_elements = safe_find_elements(driver, By.XPATH, '//a[contains(@href, "mailto:")]', 3)
        for element in email_elements:
            if element:
                href = element.get_attribute('href')
                if href and href.startswith('mailto:'):
                    email_candidate = href.replace('mailto:', '').split('?')[0] # Remove params
                    if "@" in email_candidate and "." in email_candidate.split('@')[-1]: # Basic validation
                        email = email_candidate
                        break

        if email == "N/A":
            try:
                body_text_element = safe_find_element(driver, By.TAG_NAME, 'body', 3)
                if body_text_element:
                    page_text = body_text_element.text
                    email_pattern = re.compile(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}')
                    email_match = email_pattern.search(page_text)
                    if email_match:
                        email = email_match.group(0)
            except Exception:
                pass

        social_links = set() # Use set for automatic deduplication
        # Use global SOCIAL_PLATFORMS
        links = safe_find_elements(driver, By.TAG_NAME, 'a', 3)
        for link in links:
            try:
                href = link.get_attribute('href')
                if href:
                    for platform_domain in SOCIAL_PLATFORMS:
                        # Check for domain (e.g., facebook.com) or common path (e.g., /company/linkedin_page)
                        if f"{platform_domain}/" in href.lower() or f".{platform_domain}" in href.lower() :
                            social_links.add(href)
                            break
            except StaleElementReferenceException:
                continue # Element became stale, skip it
            except Exception:
                continue # Other errors with a link

        social_links_str = '; '.join(sorted(list(social_links))) if social_links else "N/A"

        contact_info = "N/A"
        contact_selectors = [
            (By.XPATH, '//section[contains(@id,"contact") or contains(@class,"contact")] | //div[contains(@id,"contact") or contains(@class,"contact")] | //footer'),
            (By.CSS_SELECTOR, '.contact-info, .contact, #contact, #contact-us, .footer-contact')
        ]
        found_contact_text = ""
        for by_type, selector_val in contact_selectors:
            element = safe_find_element(driver, by_type, selector_val, 2) # Find one prominent section
            if element and element.text and len(element.text.strip()) > 20:
                found_contact_text = element.text.strip()
                if len(found_contact_text) > 500:
                    found_contact_text = found_contact_text[:497] + "..."
                break
        contact_info = found_contact_text if found_contact_text else "N/A"

        return {
            "source_url": url, "page_title": page_title, "meta_description": meta_description,
            "contact_info": contact_info, "address": address, "phone": phone,
            "email": email, "social_links": social_links_str,
            "extraction_method": "Selenium"
        }

    except TimeoutException:
        print(f"⏳ Timed out extracting elements for {url}. Trying fallback method...")
        fallback_data = extract_with_requests(url)
        if fallback_data:
            return fallback_data
        else:
            return {"source_url": url, "page_title": "Timeout Error (Extraction)", "meta_description": "N/A", "contact_info": "N/A", "address": "N/A", "phone": "N/A", "email": "N/A", "social_links": "N/A", "extraction_method": "Failed (Timeout during Extraction)"}
    except Exception as e:
        print(f"🐛 An unexpected error occurred scraping {url} with Selenium: {e.__class__.__name__} - {e}")
        fallback_data = extract_with_requests(url)
        if fallback_data:
            return fallback_data
        else:
            return {"source_url": url, "page_title": f"Error: {str(e)[:50]}...", "meta_description": "N/A", "contact_info": "N/A", "address": "N/A", "phone": "N/A", "email": "N/A", "social_links": "N/A", "extraction_method": f"Failed (Error: {e.__class__.__name__})"}

def read_urls_from_csv():
    """
    Reads URLs from the input CSV file with flexible column detection.
    Supports multiple CSV formats and handles URL cleaning.
    """
    if not os.path.exists(CSV_INPUT_FILE):
        print(f"❌ File '{CSV_INPUT_FILE}' not found.")
        manual_input = input("Would you like to enter URLs manually instead? (y/n): ").lower()
        if manual_input == 'y':
            urls_input_str = input("Enter URLs separated by commas: ")
            raw_urls = [url.strip() for url in urls_input_str.split(",") if url.strip()]
            return [cleaned_url for url in raw_urls if (cleaned_url := clean_url(url))] # Requires Python 3.8+ for walrus operator
        else:
            return []

    urls = []
    try:
        encoding = 'utf-8'
        try:
            with open(CSV_INPUT_FILE, 'r', encoding=encoding, errors='ignore') as f_check: # errors='ignore' for initial read
                f_check.read()
        except UnicodeDecodeError:
            encoding = 'latin-1'

        with open(CSV_INPUT_FILE, 'r', newline='', encoding=encoding) as f:
            sample = f.read(2048) # Read a larger sample for better sniffing
            f.seek(0)

            # Heuristic: if it doesn't look like a CSV (few commas/tabs) and has http/www, treat as plain list
            if (sample.count(',') < 5 and sample.count('\t') < 5) and ('http' in sample or 'www.' in sample):
                 # Likely a plain text file with one URL per line
                print("📋 Treating input file as a plain text list of URLs.")
                raw_urls = [line.strip() for line in f if line.strip() and (line.strip().startswith("http") or "www." in line.strip())]
                urls = [cleaned_url for url_val in raw_urls if (cleaned_url := clean_url(url_val))]
                return urls

            try:
                # Try each delimiter individually since the csv.Sniffer doesn't accept a list
                for delimiter in [',', ';', '\t']:
                    try:
                        dialect = csv.Sniffer().sniff(sample, delimiters=delimiter)
                        break
                    except csv.Error:
                        continue
                else:
                    # If no delimiter worked, default to comma
                    dialect = csv.excel
                reader = csv.reader(f, dialect)
                headers = next(reader, [])

                if not headers : # Empty file or only header was empty
                    return []

                url_column_idx = -1
                url_keywords = ['url', 'link', 'website', 'site', 'web', 'href', 'uri']

                # Try to find column by header keyword
                for idx, header in enumerate(headers):
                    if any(keyword in header.lower() for keyword in url_keywords):
                        url_column_idx = idx
                        break

                # If not found by header, try by content sampling (if multiple columns)
                if url_column_idx == -1 and len(headers) > 1:
                    f.seek(0) # Reset reader
                    next(reader) # Skip header again
                    sample_rows = [row for row_idx, row in enumerate(reader) if row_idx < 10 and row] # Sample up to 10 rows

                    if sample_rows:
                        max_url_like_count = 0
                        potential_url_col = -1
                        for col_idx in range(len(headers)):
                            current_col_url_like_count = 0
                            for row in sample_rows:
                                if col_idx < len(row) and row[col_idx] and \
                                   ('http' in row[col_idx].lower() or 'www.' in row[col_idx].lower() or '.com' in row[col_idx].lower()):
                                    current_col_url_like_count += 1
                            if current_col_url_like_count > max_url_like_count:
                                max_url_like_count = current_col_url_like_count
                                potential_url_col = col_idx

                        # If a column has a good number of URL-like strings
                        if potential_url_col != -1 and max_url_like_count > len(sample_rows) / 2 :
                            url_column_idx = potential_url_col

                # If still not found or single column, assume first column
                if url_column_idx == -1:
                    if len(headers) > 1:
                        print(f"⚠️ Could not reliably determine URL column from headers: {headers}. Trying first column.")
                    url_column_idx = 0

                print(f"ℹ️ Reading URLs from column {url_column_idx + 1} ('{headers[url_column_idx]}' if header existed).")
                f.seek(0) # Reset reader
                next(reader) # Skip header
                raw_urls = [row[url_column_idx] for row in reader if row and url_column_idx < len(row) and row[url_column_idx].strip()]
                urls = [cleaned_url for url_val in raw_urls if (cleaned_url := clean_url(url_val))]

            except csv.Error: # If sniffing fails, treat as plain text
                print("⚠️ CSV Sniffing failed. Treating as plain text URL list.")
                f.seek(0)
                raw_urls = [line.strip() for line in f if line.strip() and (line.strip().startswith("http") or "www." in line.strip())]
                urls = [cleaned_url for url_val in raw_urls if (cleaned_url := clean_url(url_val))]

        return [url for url in urls if url] # Filter out None values again

    except Exception as e:
        print(f"❌ Error reading URLs from file '{CSV_INPUT_FILE}': {e}")
        manual_input = input("Would you like to enter URLs manually instead? (y/n): ").lower()
        if manual_input == 'y':
            urls_input_str = input("Enter URLs separated by commas: ")
            raw_urls = [url.strip() for url in urls_input_str.split(",") if url.strip()]
            return [cleaned_url for url in raw_urls if (cleaned_url := clean_url(url))]
        else:
            return []


def user_url_selection():
    """
    Reads URLs from the input CSV and prompts the user to select which ones to scrape.
    """
    urls = read_urls_from_csv()

    if not urls:
        print("🚫 No valid URLs found or provided.")
        # Option for manual entry if read_urls_from_csv returned empty due to file not found and user chose 'n'
        retry_manual = input("Would you like to try entering URLs manually now? (y/n): ").lower()
        if retry_manual == 'y':
            urls_input_str = input("Enter URLs separated by commas: ")
            raw_urls = [url.strip() for url in urls_input_str.split(",") if url.strip()]
            urls = [cleaned_url for url in raw_urls if (cleaned_url := clean_url(url))] # Python 3.8+
            if not urls:
                print("🚫 No valid URLs entered manually.")
                return []
        else:
            return []

    print(f"\n📋 Found {len(urls)} URLs to potentially scrape:")
    for idx, url in enumerate(urls, 1):
        print(f"{idx}. {url}")

    while True:
        print("\n👉 Enter the numbers of URLs to scrape (e.g., 1,3,5-7), 'all' to scrape all, or 'quit' to exit:")
        choice = input("Your choice: ").strip().lower()

        if choice == "all":
            return urls
        if choice == 'quit':
            return []

        selected_urls = []
        try:
            parts = choice.split(',')
            selected_indexes = set()
            for part in parts:
                part = part.strip()
                if not part: continue
                if '-' in part:
                    start, end = map(int, part.split('-'))
                    selected_indexes.update(range(start - 1, end))
                else:
                    selected_indexes.add(int(part) - 1)

            valid_selections = 0
            for i in sorted(list(selected_indexes)): # Process in order
                if 0 <= i < len(urls):
                    selected_urls.append(urls[i])
                    valid_selections +=1
                else:
                    print(f"⚠️ Index {i+1} is out of range.")

            if valid_selections > 0:
                if valid_selections != len(selected_indexes):
                     print("⚠️ Some selected indices were invalid and were ignored.")
                return selected_urls
            else:
                print("❌ No valid URLs selected from your input.")
                # No retry loop here, will re-prompt by main while True

        except ValueError:
            print("❌ Invalid input format. Please use numbers, ranges (e.g., 1-3), 'all', or 'quit'.")
        # Loop will continue for new input

def write_data_to_csv(data_list, filename):
    """Writes the list of dictionaries to a CSV file."""
    if not data_list:
        print("ℹ️ No data to write to CSV.")
        return

    # Define the order of columns
    fieldnames = [
        "source_url", "page_title", "meta_description",
        "contact_info", "address", "phone", "email",
        "social_links", "extraction_method"
    ]

    file_exists = os.path.isfile(filename)
    try:
        with open(filename, 'a' if file_exists else 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames, extrasaction='ignore')
            if not file_exists or os.path.getsize(filename) == 0 : # Write header if new file or empty
                writer.writeheader()
            for data_row in data_list:
                writer.writerow(data_row)
        print(f"\n💾 Data successfully {'appended to' if file_exists and os.path.getsize(filename) > 0 else 'written to'} '{filename}'")
    except IOError as e:
        print(f"❌ Error writing to CSV file '{filename}': {e}")
    except Exception as e:
        print(f"❌ An unexpected error occurred during CSV writing: {e}")


# --- Main Application Logic ---
def main():
    """Main function to run the scraper."""
    print("🚀 Starting Web Scraper...")

    # Get URLs to scrape first, before initializing the driver
    urls_to_scrape = user_url_selection()
    if not urls_to_scrape:
        print("ℹ️ No URLs selected for scraping. Exiting.")
        return

    # Only initialize the driver if we have URLs to scrape
    driver = initialize_driver()
    if not driver:
        print("🛑 WebDriver initialization failed. Exiting.")
        return

    scraped_data_all = []
    total_urls = len(urls_to_scrape)
    print(f"\n🔍 Starting scraping for {total_urls} selected URL(s)...")

    start_time = time.time()

    try:
        for i, url in enumerate(urls_to_scrape):
            print(f"\n--- Scraping URL {i+1} of {total_urls}: {url} ---")
            data = scrape_website_details(driver, url)
            if data:
                scraped_data_all.append(data)

                # Write incrementally to avoid losing data if there's an error
                write_data_to_csv([data], OUTPUT_FILE)
            else: # Should not happen if scrape_website_details always returns a dict
                print(f"❓ No data returned for {url}")
                error_data = {
                    "source_url": url,
                    "page_title": "Scraping Error",
                    "meta_description": "N/A",
                    "contact_info": "N/A",
                    "address": "N/A",
                    "phone": "N/A",
                    "email": "N/A",
                    "social_links": "N/A",
                    "extraction_method": "Failed - No Data"
                }
                scraped_data_all.append(error_data)
                write_data_to_csv([error_data], OUTPUT_FILE)

            # Optional delay between requests to avoid overloading servers
            if i < total_urls - 1:
                time.sleep(0.5)  # Reduced delay for efficiency
    finally:
        # Ensure WebDriver quits even if there's an exception
        try:
            if driver:
                driver.quit()
                print("✅ WebDriver closed.")
        except Exception as e:
            print(f"⚠️ Error closing WebDriver: {e}")

    # Calculate and display elapsed time
    elapsed_time = time.time() - start_time
    minutes, seconds = divmod(int(elapsed_time), 60)

    if scraped_data_all:
        print(f"\n🎉 Scraping completed: {len(scraped_data_all)} URLs processed in {minutes}m {seconds}s.")
    else:
        print("ℹ️ No data was scraped.")

if __name__ == "__main__":
    main()