"use client";

interface LoadingIndicatorProps {
  isProcessingMultiple?: boolean;
  progress?: {
    current: number;
    total: number;
  };
}

export function LoadingIndicator({ isProcessingMultiple, progress }: LoadingIndicatorProps) {
  return (
    <div className="flex flex-col items-center justify-center py-8">
      <div className="mb-4 h-12 w-12 animate-spin rounded-full border-4 border-white/20 border-t-[hsl(280,100%,70%)]"></div>

      {isProcessingMultiple && progress ? (
        <>
          <p className="text-lg font-medium">
            Scraping Google Maps... ({progress.current + 1}/{progress.total})
          </p>
          <div className="mt-3 h-2 w-64 overflow-hidden rounded-full bg-white/10">
            <div
              className="h-full bg-[hsl(280,100%,70%)]"
              style={{
                width: `${Math.round((progress.current / progress.total) * 100)}%`
              }}
            ></div>
          </div>
          <p className="mt-2 text-sm text-white/70">
            Processing multiple cities. Please wait...
          </p>
        </>
      ) : (
        <>
          <p className="text-lg font-medium">Scraping Google Maps...</p>
          <p className="mt-2 text-sm text-white/70">
            This may take a few moments depending on the number of results.
          </p>
        </>
      )}
    </div>
  );
}
